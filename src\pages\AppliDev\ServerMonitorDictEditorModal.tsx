/* eslint-disable arrow-body-style */
/* eslint-disable react/jsx-no-undef */
/* eslint-disable react/no-unused-state */
/* eslint-disable dot-notation */
/* eslint-disable react/jsx-filename-extension */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/sort-comp */

import React, { Component } from 'react';
import { connect } from 'dva';
import { Modal, Switch, Input, Select, message, AutoComplete, Button, Radio, Form } from 'antd';
import * as styles from './ServerMonitorSingleEditorModal.less';
import { getCateName, getOptionList, getSelectName } from '@/utils/mallTool';
import { monitorDicApiList, monitorDicEnum, monitorDicList } from './models/apiServerMonitor';

export enum monitorEditEnum {
  Edit = 1,
  Create = 2,
}

// eslint-disable-next-line react/no-unused-prop-types
@connect(
  ({
    loading,
    login,
    apiServerMonitor,
  }: {
    loading: IdvaLoading;
    login: ILogin;
    apiServerMonitor: any;
  }) => ({
    uid: login.uid,
    isLoading: loading.models['apiServerMonitor'],
    list: apiServerMonitor.list,
    typeList: apiServerMonitor.typeList,
    userList: apiServerMonitor.userList,
    businessList: apiServerMonitor.businessList,
    serverList: apiServerMonitor.serverList,
    envList: apiServerMonitor.envList,
    formatList: apiServerMonitor.formatList,
    systemList: apiServerMonitor.systemList,
  })
)
class ServerMonitorDictEditorModal extends React.Component<any, any> {
  constructor(props) {
    super(props);
    const { dataModelType, current } = props;

    this.state = {
      editType: current ? monitorEditEnum.Edit : monitorEditEnum.Create,
    };
  }

  render() {
    const { dataModelType, current, isLoading } = this.props;
    const { editType } = this.state;
    const preStr = editType == monitorEditEnum.Edit ? '编辑' : '新增';
    const title = `${preStr}${getCateName(dataModelType, monitorDicList)}`;
    return (
      <Modal
        width={550}
        bodyStyle={{ padding: '5px 10px 5px 10px' }}
        closable={false}
        maskClosable={false}
        confirmLoading={!!isLoading}
        centered
        title={title}
        footer={null}
        visible={dataModelType > 0}
      >
        <div>{dataModelType > 0 && this.renderEditModal()}</div>
      </Modal>
    );
  }

  renderEditModal = () => {
    const { dataModelType, current, systemList } = this.props;
    const { editType } = this.state;
    const init = editType == monitorEditEnum.Edit ? { ...current } : {};

    const layout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
    };
    const tailLayout = {
      wrapperCol: { offset: 16, span: 16 },
    };
    const bottom = '10px';

    return (
      <div>
        <Form
          {...layout}
          initialValues={init}
          name="award"
          onFinish={this.onClickOk}
          onFinishFailed={this.onFinishFailed}
          style={{ marginBottom: bottom }}
        >
          <Form.Item
            required
            rules={[{ required: true}]}
            style={{ marginBottom: bottom }}
            label={getCateName(dataModelType, monitorDicList)}
            name="name"
          >
            <Input />
          </Form.Item>
          {dataModelType == monitorDicEnum.Server && (
            <>
              <Form.Item required rules={[{ required: true}]} style={{ marginBottom: bottom }} label="外网ip" name="outer_ip">
                <Input />
              </Form.Item>
              <Form.Item required rules={[{ required: true}]} style={{ marginBottom: bottom }} label="内网ip" name="inner_ip">
                <Input />
              </Form.Item>
              <Form.Item required rules={[{ required: true}]} label="操作系统" name="system_id">
                <Select
                  className={styles.content}
                  placeholder="请选择操作系统"
                  // defaultValue={getCateName(current.env_id, envList)}
                >
                  {systemList.map((item, index) => {
                    return (
                      <Option key={`${item.id} + ${item.name}`} value={item.id}>
                        {item.name}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </>
          )}
          {dataModelType == monitorDicEnum.Format && (
            <>
              <Form.Item
                required
                rules={[{ required: true}]}
                style={{ marginBottom: bottom }}
                label="时间format"
                name="time_format"
              >
                <Input />
              </Form.Item>
              <Form.Item
                required
                rules={[{ required: true}]}
                style={{ marginBottom: bottom }}
                label="时间正则"
                name="time_pattern"
              >
                <Input />
              </Form.Item>
            </>
          )}
          <Form.Item {...tailLayout}>
            <Button onClick={this.props.onClickClose}>取消</Button>
            <Button style={{ marginLeft: 10 }} type="primary" htmlType="submit">
              提交
            </Button>
          </Form.Item>
        </Form>
      </div>
    );
  };

  onFinishFailed = errorInfo => {
    console.log('Failed:', errorInfo);
    // eslint-disable-next-line react/destructuring-assignment
    if (this.state.editType == monitorEditEnum.Edit) {
      message.error('修改失败!');
    } else {
      message.error('新增失败!');
    }
  };

  onClickOk = values => {
    console.log('Success:', values);
    const { editType } = this.state;
    const { dispatch, dataModelType, current, systemList } = this.props;
    let req: any = {...values};
    if (editType == monitorEditEnum.Edit) {
      req = {...req, id: current.id}
    }
    if (dataModelType == monitorDicEnum.Server) {
      req = {...req, system_name: getCateName(values.system_id, systemList)}
    }

    console.log('req,', req);
    let opt;
    if (editType == monitorEditEnum.Edit) {
      opt = 'update';
    }else{
      opt = 'insert';
    }
    dispatch({
      type: `apiServerMonitor/${opt}${getCateName(dataModelType, monitorDicApiList)}`,
      payload: req,
    }).then(() => {
      this.props.onClickClose();
      this.props.refreshView();
    });
  };
}

export default ServerMonitorDictEditorModal;
