/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-09-19 17:09:30
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-16 17:38:28
 */

//活动目录
export interface IactivityIndex {
   id: number;
   name: number;
   start_time: string;
   end_time: string;
   sql_admin: string;
   end_data_time: string;
}

//活动奖励组
export interface IactivityAwardGroup {
   id: number;
   activity_id: number;
   group_name: string;
   group_index: number;
   edit_enable: number;
}

//请求奖励组
export interface IgetAwardGroupReq {
   activity_id: number;
}

//请求奖励配置
export interface IgetAwardConfReq {
   activity_id: number;
   award_group_id: number;
}

//运用活动奖励配置
export interface IactivityAwardConf {
   activity_id: number;
   award_id: number;
   weight: number;
   target_num: number;
   props_no: number;
   props_type: number;
   is_limit: number;
   num: number;
   award_name: number;
   max_times: number;
   item_dic_id: number;
   headge_num: number;
   guarante_num: number;
   is_optional: number;
   award_group_id: number;
   item_id: number;
   item_cate_id: number;
   item_cate_name: string;
   item_name: string;
   itemContent?: IactivityAwardItem[];
   item_table: string;
   img_name: string;
   price: number;
   coin_id: number;
   price_name: string;
   coin_img_name: string;
   sub_item_dic_id_list: string;
}

//奖励配置物品内容
export interface IactivityAwardItem {
   item_dic_id: number;
   item_id: number;
   item_cate_id: number;
   item_cate_name: string;
   item_name: string;
   item_table: string;
   img_name: string;
   num: number;
}
