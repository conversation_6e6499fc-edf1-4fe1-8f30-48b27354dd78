# Ant Design Pro 项目概述

## 项目简介

Ant Design Pro 是一个开箱即用的中台前端/设计解决方案，基于 React 和 Ant Design 构建，提供了企业级应用开发的最佳实践。

## 目录结构
ant-design-pro/
├── config/ # 配置文件目录
├── docs/ # 文档目录
├── functions/ # 云函数目录
├── mock/ # Mock 数据目录
├── scripts/ # 脚本目录
├── src/ # 源代码目录
│ ├── assets/ # 静态资源
│ ├── components/ # 公共组件
│ ├── layouts/ # 布局组件
│ ├── models/ # 数据模型
│ ├── pages/ # 页面组件
│ ├── services/ # 服务层
│ └── utils/ # 工具函数
├── tests/ # 测试目录
└── docker/ # Docker 配置


## 技术栈

### 主要框架和库

1. **React** (^16.5.1)
   - 用于构建用户界面的 JavaScript 库
   - 提供组件化开发模式

2. **Ant Design** (^4.8.4)
   - 企业级 UI 设计语言和 React 组件库
   - 提供丰富的预置组件和设计规范

3. **Umi** (^2.2.1)
   - 可插拔的企业级 React 应用框架
   - 提供路由、构建、部署等开箱即用的功能

4. **Dva** (^2.4.0)
   - 基于 Redux 和 Redux-Saga 的数据流方案
   - 提供状态管理和副作用处理

### 开发工具和辅助库

1. **构建和开发工具**
   - Babel: JavaScript 编译器
   - ESLint: 代码质量检查工具
   - Prettier: 代码格式化工具

2. **状态管理和工具库**
   - Lodash: 实用工具库
   - Moment.js: 日期处理库
   - Immutable.js: 不可变数据结构

3. **图表和可视化**
   - BizCharts: 基于 G2 的图表库
   - @ant-design/charts: 图表组件

### 测试和性能工具

1. **测试工具**
   - Enzyme: React 组件测试工具
   - Jest: JavaScript 测试框架

2. **性能优化**
   - 支持按需加载
   - 代码分割
   - 生产环境移除 console

## 开发环境要求

- Node.js: >= 8.0.0
- 浏览器支持：
  - 现代浏览器
  - IE11
  - 最新两个版本的 Chrome、Firefox、Safari 和 Edge

## 主要功能模块

- Dashboard
- 表单页
- 列表页
- 详情页
- 用户管理
- 结果页
- 异常页
- 账户页

## 开发命令
安装依赖
npm install
启动开发服务器
npm start
构建生产版本
npm run build
代码风格检查
npm run lint
运行测试
npm test


## 部署方式

- 支持传统部署
- 支持 Docker 部署
- 支持 Firebase 部署

## 国际化支持

内置国际化方案，支持多语言切换

## 主题定制

提供主题定制能力，满足个性化需求

## 贡献指南

1. 提交 Issue
2. 提交 Pull Request
3. 遵循代码规范
4. 通过所有测试

## 许可证

MIT License