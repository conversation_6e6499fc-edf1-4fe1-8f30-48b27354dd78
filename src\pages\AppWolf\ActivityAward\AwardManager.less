@import '~antd/lib/style/themes/default.less';
@import '~@/utils/utils.less';

.tableValItem {
  color: @primary-color;
}

.evenRow {
    background-color: #ffffff;
}

.oddRow {
    background-color: #fafafa;
}

// 确保内容垂直居中对齐
:global {
    .ant-table-tbody > tr > td {
        vertical-align: middle;
    }

    // 保持hover效果
    .ant-table-tbody > tr:hover > td {
        background-color: #e6f7ff !important;
    }

    // 加强表格线的显示
    .ant-table-bordered .ant-table-cell {
        border-right: 1px solid #f0f0f0;
    }
}

.imageMask {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.transparentModal {
  :global {
    .ant-modal-content {
      background: transparent;
      box-shadow: none;
    }
    .ant-modal-body {
      background: transparent;
    }
    .ant-modal-close {
      color: white;
    }
  }
}

// 优化的表格单元格容器
.itemContentContainer {
  min-height: 32px;

  .itemContentRow {
    display: flex;
    align-items: center;
    min-height: 32px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(24, 144, 255, 0.05);
    }

    &:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .itemContentContainer {
    .itemContentRow {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
      padding: 12px 0;
    }
  }
}

// 加载状态样式
.loadingCell {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
  color: @text-color-secondary;
}

// 空状态样式
.emptyCell {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
  color: @text-color-secondary;
  font-style: italic;
}
