/*
 * @Description: 流水管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-02 15:25:29
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  AutoComplete,
  Row,
  Col,
  Radio,
  Image,
  Table,
  Tag,
  Typography,
  message,
} from 'antd';
import * as styles from './AuditFlowManager.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import { ColumnProps } from 'antd/lib/table';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName, getOptionList } from '@/utils/mallTool';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
export const PAGESIZE = 8;

const defaultPayload = { current: 1, pageCount: PAGESIZE, where: "m.status = 1" };

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  merchantSimpleList: scriptkill.merchantSimpleList,
  scriptSimpleList: scriptkill.scriptSimpleList,
  currentMerchantScriptList: scriptkill.currentMerchantScriptList,
  auditFlowList: scriptkill.auditFlowList,
  auditFlowCount: scriptkill.auditFlowCount,
  operateUserList: scriptkill.operateUserList,
}))

export default class AuditFlowManager extends Component<any, any> {
  constructor(props) {
    super(props);

    this.state = {
      currentMerchantId: -1,
      currentScriptId: -1,
      currentUser: -1,
      currentMerchantValue: "",
      currentScriptValue: "",
      currentUserValue: "",

      currentPage: 1,
      columns: this.makeColumns(1),
    }

    this.props.dispatch({
      type: 'scriptkill/setAuditFlowList',
      payload: []
    })
    this.props.dispatch({
      type: 'scriptkill/setAuditFlowCount',
      payload: 0,
    });
    this.dispatchMerchantSimpleList();
    this.dispatchScriptSimpleList();
    this.dispatchOperateUserList();
  }

  render() {
    const { isLoading, auditFlowList } = this.props;
    return (
      <Spin spinning={!!isLoading}>
        <PageHeaderWrapper title="检索条件" content={this.renderHeader()}>
          <Table
            scroll={{ x: 1550 }}
            columns={this.state.columns}
            dataSource={auditFlowList}
            loading={!!isLoading}
            bordered={true}
            rowKey={(record, index) => index.toString()}
            pagination={{  // 分页
              pageSize: PAGESIZE,
              current: this.state.currentPage,
              total: this.props.auditFlowCount,
              onChange: this.changePage,
            }}
          />
        </PageHeaderWrapper>
      </Spin>
    );
  }

  renderHeader() {
    const { globalPlayerId } = this.props;
    const { user_noSearch, old_usernameSearch, new_usernameSearch, statusSearch, createtimeSearch, updatetimeSearch } = this.state;
    const colMd = 2;
    const colInputMd = 6;
    return (
      <>
        <Row style={{ width: 2000 }}>
          <Col md={colMd}>
            商铺名称/ID:
          </Col>
          <Col md={colInputMd} style={{ marginTop: -5, marginLeft: -40 }}>
            <AutoComplete
              allowClear={true}
              className={styles.content}
              options={getOptionList(this.props.merchantSimpleList)}
              onSelect={this.onSelectMerchant}
              onChange={this.onChangeMerchant}
              value={this.state.currentMerchantValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) =>
                option.value.indexOf(inputValue) !== -1
              }
            />
          </Col>
        </Row>
        <Row style={{ width: 2000, marginTop: 10 }}>
          <Col md={colMd}>
            剧本名称/ID:
          </Col>
          <Col md={colInputMd} style={{ marginTop: -5, marginLeft: -40 }}>
            <AutoComplete
              allowClear={true}
              className={styles.content}
              options={getOptionList(this.props.scriptSimpleList)}
              onSelect={this.onSelectScrpit}
              onChange={this.onChangeScrpit}
              value={this.state.currentScriptValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) =>
                option.value.indexOf(inputValue) !== -1
              }
            />
          </Col>
        </Row>
        <Row style={{ width: 2000, marginTop: 10 }}>
          <Col md={colMd}>
            审核员名称/ID:
          </Col>
          <Col md={colInputMd} style={{ marginTop: -5, marginLeft: -40 }}>
            <AutoComplete
              allowClear={true}
              className={styles.content}
              options={getOptionList(this.props.operateUserList)}
              onSelect={this.onSelectUser}
              onChange={this.onChangeUser}
              value={this.state.currentUserValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) =>
                option.value.indexOf(inputValue) !== -1
              }
            />
          </Col>
        </Row>
      </>
    );
  }

  makeColumns(type: any) {
    let columns: ColumnProps<any>[] = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 70,
        align: 'center',
      },
      {
        title: '审核员',
        dataIndex: 'nickname',
        key: 'nickname',
        width: 100,
        align: 'center',
      },
      {
        title: '操作',
        dataIndex: 'type',
        key: 'type',
        width: 100,
        align: 'center',
        render: (val) => {
          const color = val == 2 ? "#FF0000" : "#000000";
          return <div style={{ color }}>{getCateName(val, this.props.statusList)}</div>;
        }
      },
      {
        title: '理由',
        dataIndex: 'refuse_mes',
        align: 'center',
      },
    ];

    const time: ColumnProps<any> = {
      title: '审核时间',
      dataIndex: 'createtime',
      align: 'center',
      width: 120,
      render: (val) => {
        return <div>{val == null ? '\\' : getTimeStr(val)}</div>;
      }
    };

    const merchant: ColumnProps<any>[] = [
      {
        title: '商铺 ID',
        dataIndex: 'merchant_id',
        key: 'merchant_id',
        align: 'center',
      },
      {
        title: '商铺名称',
        dataIndex: 'merchant_name',
        key: 'merchant_name',
        align: 'center',
      }
    ]

    const script: ColumnProps<any>[] = [
      {
        title: '剧本 ID',
        dataIndex: 'script_id',
        key: 'script_id',
        align: 'center',
      },
      {
        title: '剧本名称',
        dataIndex: 'script_name',
        key: 'script_name',
        align: 'center',
      }
    ]

    if (type == 3) {
      columns = columns.concat(merchant);
      columns = columns.concat(script);
    }

    columns.push(time);
    return columns;
  }

  onChangeScrpit = (data: string) => {
    this.setState({ currentScriptValue: data });
  };

  onChangeMerchant = (data: string) => {
    this.setState({ currentMerchantValue: data });
  };

  onChangeUser = (data: string) => {
    this.setState({ currentUserValue: data });
  };

  changePage = (page) => {
    this.setState({ currentPage: page });
    const req = {
      operate_user_id: this.state.currentUser,
      merchant_id: this.state.currentMerchantId,
      script_id: this.state.currentScriptId,
      current: page,
      pageCount: PAGESIZE
    };
    this.dispatchAduitFlowList(req);
  };

  onSelectMerchant = (value) => {
    const val = value.substring(4, value.indexOf(" 名称"));
    this.setState({
      currentMerchantId: val,
      currentScriptId: -1,
      currentUser: -1,
      currentMerchantValue: value,
      currentScriptValue: "",
      currentUserValue: "",
      currentPage: 1,
      columns: this.makeColumns(1)
    });

    this.dispatchAduitFlowList({
      merchant_id: val,
      current: 1,
      pageCount: PAGESIZE
    });
  };

  onSelectScrpit = (value) => {
    const val = value.substring(4, value.indexOf(" 名称"));
    this.setState({
      currentMerchantId: -1,
      currentScriptId: val,
      currentUser: -1,
      currentMerchantValue: "",
      currentScriptValue: value,
      currentUserValue: "",
      currentPage: 1,
      columns: this.makeColumns(2)
    });

    this.dispatchAduitFlowList({
      script_id: val,
      current: 1,
      pageCount: PAGESIZE
    });
  };

  onSelectUser = (value) => {
    const val = value.substring(4, value.indexOf(" 名称"));
    this.setState({
      currentMerchantId: -1,
      currentScriptId: -1,
      currentUser: val,
      currentMerchantValue: "",
      currentScriptValue: "",
      currentUserValue: value,
      currentPage: 1,
      columns: this.makeColumns(3)
    });

    this.dispatchAduitFlowList({
      operate_user_id: val,
      current: 1,
      pageCount: PAGESIZE
    });
  };

  dispatchAduitFlowList = (req) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getAuditFlowList',
      payload: req
    }).then(() => {
    });

    dispatch({
      type: 'scriptkill/getAuditFlowListCount',
      payload: req
    }).then(() => {
    });
  }

  dispatchMerchantSimpleList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getMerchantSimpleList',
    });
  };

  dispatchScriptSimpleList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getScriptSimpleList',
    });
  };

  dispatchOperateUserList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getOperateUserList',
    });
  };
}
