export interface IGiftItem {
  id:number,
  gift_id:number,
  gift_name:string,
  item_dic_id:number,
  item_dic_name:string,
  item_cate_id:number, //头像框 2020 2010
  num:number,
  weight:number,
  sex:number,
  repeat:number,
  rate:string,
  delsign:number,
  avatarframe_flag:number,
}

export interface IGift {
  no: number,
  name: string,
}

export interface IAddGiftItemParams {
  gift_id: number,
  item_dic_id: number,
  item_cate_id: number, //头像框 2020 2010
  num: number,
  weight: number,
  sex?: number,
  repeat: number,
  rate: string,
  delsign: number,
}

export interface IUpdateGiftItemParams {
  id: number,
  gift_id: number,
  item_dic_id: number,
  item_cate_id: number, //头像框 2020 2010
  num: number,
  weight: number,
  sex?: number,
  repeat: number,
  rate: string,
  delsign: number,
}
