/* eslint-disable react/sort-comp */
/*
 * @Description: 奖励组编辑模态页
 * @version: 2.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-10-12 16:56:14
 * @LastEditors: zhanglu
 * @LastEditTime: 2024-12-19 10:00:00
 */

import React, { Component } from 'react';
import { connect } from 'dva';
import { Modal, Input, InputNumber, Form, message, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { IactivityAwardGroup, IactivityIndex } from '@/dto/wfActivityAward';
import { FormInstance } from 'antd/lib/form';

const { Item: FormItem } = Form;

// 操作类型枚举
enum ModalType {
	EDIT = 1,    // 编辑
	CREATE = 2   // 新建
}

interface IawardGroupEditModalProps {
	dataModelType: ModalType;
	current: IactivityIndex; // 活动信息
	item?: IactivityAwardGroup; // 奖励组（编辑时传入）
	onClickClose: () => void;
}

@connect(({ activityAward, loading }: { activityAward: any; loading: IdvaLoading }) => ({ activityAward, isLoading: loading.models.activityAward }))
class AwardGroupEditModal extends Component<IawardGroupEditModalProps & any> {
	formRef = React.createRef<FormInstance>();

	// 渲染带有帮助提示的标题
	renderLabelWithHelp = (label: string, helpText: string) => (
		<span>
			{label}
			<Tooltip title={helpText} placement="top">
				<QuestionCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
			</Tooltip>
		</span>
	);

	// 获取模态框标题和按钮文本
	getModalConfig = () => {
		const { dataModelType, current, item } = this.props;
		const isEdit = dataModelType === ModalType.EDIT;

		return {
			title: isEdit
				? `编辑活动【${current?.name}】的奖励组【${item?.group_name}】`
				: `创建活动【${current?.name}】的奖励组`,
			okText: isEdit ? '保存修改' : '创建奖励组'
		};
	};

	render() {
		const { dataModelType, isLoading } = this.props;
		const { title, okText } = this.getModalConfig();

		return (
			<Modal
				title={title}
				open={dataModelType > 0}
				onCancel={this.handleCancel}
				onOk={this.handleSubmit}
				confirmLoading={!!isLoading}
				cancelText="取消"
				okText={okText}
				destroyOnClose
				width={500}
			>
				{this.renderForm()}
			</Modal>
		);
	}

	renderForm = () => {
		const { item } = this.props;
		const initialValues = item ? {
			group_name: item.group_name,
			group_index: item.group_index,
		} : {};

		return (
			<Form
				ref={this.formRef}
				initialValues={initialValues}
				layout="vertical"
				preserve={false}
			>
				<FormItem
					label={this.renderLabelWithHelp('奖励组名称', '为奖励组设置一个易于识别的名称')}
					name="group_name"
					rules={[
						{ required: true, message: '请输入奖励组名称' },
						{ max: 50, message: '名称长度不能超过50个字符' }
					]}
				>
					<Input placeholder="请输入奖励组名称" />
				</FormItem>

				<FormItem
					label={this.renderLabelWithHelp('奖励分组索引', '输入100，则award_id范围为101~199。用于区分不同奖励组的ID范围')}
					name="group_index"
					rules={[
						{ required: true, message: '请输入奖励分组索引' },
						{ type: 'number', min: 100, message: '索引值不能小于100' }
					]}
				>
					<InputNumber
						placeholder="请输入奖励分组索引"
						style={{ width: '100%' }}
						min={100}
						max={9999}
					/>
				</FormItem>
			</Form>
		);
	};

	// 处理取消操作
	handleCancel = () => {
		const { onClickClose } = this.props;
		onClickClose();
	};

	// 处理提交操作
	handleSubmit = async () => {
		try {
			const values = await this.formRef.current?.validateFields();
			const { dispatch, dataModelType, current, item } = this.props;

			const isEdit = dataModelType === ModalType.EDIT;
			const request = {
				activity_id: current.id,
				group_name: values.group_name,
				group_index: values.group_index,
				award_group_id: item?.id,
				...(isEdit && item && { id: item.id }) // 编辑时需要传递ID
			};

			const actionType = isEdit
				? 'activityAward/updateAwardGroup'
				: 'activityAward/insertAwardGroup';

			await dispatch({
				type: actionType,
				payload: request
			});

			this.handleCancel();
		} catch (error) {
			console.error('提交失败:', error);
			// 表单验证失败时不需要额外处理，antd会自动显示错误信息
		}
	};
}

export default AwardGroupEditModal;
