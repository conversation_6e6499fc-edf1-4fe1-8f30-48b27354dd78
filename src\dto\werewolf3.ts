/*
 * @Description: werewolf dto 游戏板子管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-10-11 13:49:04
 * @LastEditors: hammercui
 * @LastEditTime: 2019-10-14 18:12:01
 */

//更新gameBoardOpen
export interface IupdateGameBoardOpenReq {
  gameBoardId: number;
  openTime: string;
  closeTime: string;
  newFlag: number;
  desc: string;
  hot: number;
}

//更新排序
export interface IupdateGameBoardOpenSortReq {
  newSort: GameBoardSortItem[]
}

export interface GameBoardSortItem {
  gameBoardId: number;
  sort: number;
}

//创建gameBoardOpen
export interface IcreateGameBoardOpenReq {
  gameBoardId: number;
  openTime: string;
  closeTime: string;
  newFlag: number;
  desc: string;
  hot: number;
  sort: number;
}
