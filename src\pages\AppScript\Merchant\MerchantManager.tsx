/*
 * @Description: 称号管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-22 13:10:48
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  Row,
  Col,
  Radio,
  Image,
  Table,
  Tag,
  Typography,
  Modal,
  Input,
  Popconfirm,
  AutoComplete
} from 'antd';
const { TextArea } = Input;
const { Text } = Typography;
import { PlusOutlined, CloudUploadOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import * as styles from './MerchantManager.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import AppList from '../../../pages/AppList';
import { ColumnProps } from 'antd/lib/table';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName, getOptionList, getSelectName } from '@/utils/mallTool';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import MerchantAuditModal from "./MerchantAuditModal";
import MerchantEditorModal from "./MerchantEditorModal";
import { checkNotNull } from '@/utils/emptyTool';
export const PAGESIZE = 8;

export enum tabEnum {
  old = 1,
  new = 2,
}

const defaultPayload = { tabKey: tabEnum.old, current: 1, pageCount: PAGESIZE, where: "m.status = 1" };

const tabList = [
  {
    key: tabEnum.old,
    tab: '商铺新增'
  },
  {
    key: tabEnum.new,
    tab: '商铺修改'
  },
];

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  merchantAuditCount: scriptkill.merchantAuditCount,
  merchantRefuseCount: scriptkill.merchantRefuseCount,
  merchantPassCount: scriptkill.merchantPassCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  currentMerchantScriptList: scriptkill.currentMerchantScriptList,
  merchantSimpleList: scriptkill.merchantSimpleList,
}))

export default class MerchantManager extends Component<any, any> {
  constructor(props) {
    super(props);

    this.state = {
      currentPage: 1,
      currentMerchant: null,
      dataModelType: -1,
      refuseModalType: -1,
      updateModalType: -1,
      refuseMes: "",
      imageModelType: -1,
      imageViewModelType: -1,
      where: "m.status = 1",
      tabKey: tabEnum.old,
      currentMerchantValue: "",
      currentMerchantId: null,
      merchantColumns: this.makeColumnsMerchant(1),
    }

    this.dispatchMerchantList(defaultPayload);
    this.dispatchMerchantListCount(defaultPayload);
    this.dispatchMerchantStatusCount(tabEnum.old);
    this.dispatchMerchantSimpleList();
    this.dispatchCityList();
  }

  render() {
    const { isLoading } = this.props;
    return (
      <Spin spinning={!!isLoading}>
        <PageHeaderWrapper title="检索条件"
          content={this.renderHeader()}
          tabList={tabList}
          onTabChange={this.handleTabChange}>
          {this.renderGift()}
        </PageHeaderWrapper>
      </Spin>

    );
  }

  renderHeader() {
    const { tabKey } = this.state;
    const colMd = 2;
    const colInputMd = 6;
    return (
      <>
        <Row style={{ width: 1400 }}>
          <Col md={colMd}>
            状态:
          </Col>
          <Col md={colInputMd} style={{ marginBottom: 12, marginLeft: -60 }}>
            <Radio.Group onChange={this.onChangeRadio} value={this.state.where}>
              {this.props.statusList.map((item, index) => {
                return (
                  <Radio value={item.where} disabled={index == 2 && tabKey == tabEnum.new ? true : false}>
                    {item.name}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Col>
          {/* <Col md={2} style={{ marginTop: -5 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={this.props.isSearching}
              onClick={this.handleSearch}
            >
              查询
          </Button>
          </Col> */}
        </Row>
        <Row style={{ width: 1400 }}>
          <Col md={1}>
            数量:
          </Col>
          <Col md={2}>
            {this.props.merchantAuditCount}
          </Col>
          <Col md={2} style={{ marginLeft: -35 }}>
            {this.props.merchantRefuseCount}
          </Col>
          <Col md={2} style={{ marginLeft: -5 }}>
            {tabKey == tabEnum.new ? "不可用" : this.props.merchantPassCount}
          </Col>
        </Row>
        <Row style={{ width: 2000, marginTop: 20 }}>
          <Col md={colMd}>
            商铺名称/ID:
          </Col>
          <Col md={colInputMd} style={{ marginTop: -5, marginLeft: -80, width: 1000 }}>
            <AutoComplete
              allowClear={true}
              className={styles.content}
              options={getOptionList(this.props.merchantSimpleList)}
              onSelect={this.onSelectMerchant}
              onChange={this.onChangeMerchant}
              value={this.state.currentMerchantValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) =>
                option.value.indexOf(inputValue) !== -1
              }
            />
          </Col>
        </Row>
      </>
    );
  }

  renderGift() {
    const { merchantList, isLoading } = this.props;
    const { dataModelType, updateModalType, tabKey, currentMerchant, currentPage, where, refuseModalType } = this.state;
    return (
      <div>
        <Spin spinning={!!this.props.isLoading}>
          {dataModelType > 0 &&
            <MerchantAuditModal
              dataModelType={dataModelType}
              currentMerchant={currentMerchant}
              onClickClose={this.onClickClose}
              refreshNum={this.dispatchMerchantStatusCount}
              currentPage={currentPage}
              pageSize={PAGESIZE}
              where={where}
              tabKey={tabKey} />}
          {refuseModalType > 0 &&
            <RefuseModal
              refuseModalType={refuseModalType}
              onCloseRefuseModel={this.onCloseRefuseModel}
              refuseMerchantAction={this.refuseMerchantAction}
              onRefuseMesChange={this.onRefuseMesChange}>
            </RefuseModal>
          }
          {updateModalType > 0 &&
            <MerchantEditorModal
              updateModalType={updateModalType}
              current={currentMerchant}
              onClickClose={this.onClickClose}
              currentPage={currentPage}
              pageSize={PAGESIZE}
              where={where}
              tabKey={tabKey} />}
        </Spin>

        <Card>
          <Table
            scroll={{ x: 1550 }}
            columns={this.state.merchantColumns}
            dataSource={merchantList}
            loading={!!isLoading}
            bordered={true}
            rowKey={(record, index) => index.toString()}
            pagination={{  // 分页
              pageSize: PAGESIZE,
              current: this.state.currentPage,
              total: this.props.merchantCount,
              onChange: this.changePage,
            }}
          />
        </Card>
      </div>
    );
  }

  makeColumnsMerchant(type: any) {
    const columnsMerchant: ColumnProps<any>[] = [
      {
        title: 'ID',
        key: 'id',
        width: 70,
        align: 'center',
        render: (record) => {
          if (this.state.tabKey == tabEnum.old) {
            return <div>{record.id}</div>;
          } else {
            return <div>{record.merchant_id}</div>;
          }
        }
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 100,
        align: 'center',
      },
      {
        title: '状态',
        dataIndex: 'delsign',
        align: 'center',
        width: 70,
        render: (val) => {
          return <div>{val == 1 ? '删除' : "正常"}</div>;
        }
      },
      {
        title: '操作',
        width: 120,
        dataIndex: 'delsign',
        align: 'center',
        render: (val, record, index) => {
          if (record.status == 10) {
            return <div>
              <Button className={styles.marginLeft} type="primary" danger={true} onClick={() => this.refuseMerchant(record)}>
                驳回
              </Button>
              <Button className={styles.marginLeft} type="primary" onClick={() => this.updateMerchant(record)}>
                编辑
              </Button>
              <Popconfirm
                title={`确定要下架商铺吗?`}
                onConfirm={() => this.updateMerchantDelsign(record)}
                okText="Yes"
                cancelText="No"
              >
                <Button className={styles.marginLeft} type="primary" danger={true} >
                  下架
              </Button>
              </Popconfirm>
            </div>
          } else {
            return <div>
              <Button disabled={record.status == 10} type="primary" onClick={() => this.onClickEdit(record)} danger={true}>审核</Button>
            </div>
          }
        }
      },
      {
        title: '首张图片展示',
        width: 100,
        align: 'center',
        render: (record) => {
          if (record.imgList != null && record.imgList.length > 0) {
            const str = record.imgList[0];
            if (str != null && str != '') {
              return (
                <Image src={str} width={80} height={80}></Image>
              );
            }
          }
          return <div>无资源图</div>;
        }
      },
      // {
      //   title: '省',
      //   dataIndex: 'provinceName',
      //   width: 120,
      //   align: 'center',
      // },
      {
        title: '市',
        dataIndex: 'cityName',
        width: 120,
        align: 'center',
      },
      {
        title: '具体地址',
        dataIndex: 'address',
        align: 'center',
      },
      {
        title: '电话',
        dataIndex: 'telList',
        width: 70,
        align: 'center',
        render: telList => (
          <>
            {telList.map((item, index) => {
              return (
                <div><Text>{item}</Text></div>
              );
            })}
          </>
        ),
      },
      {
        title: '营业时间',
        key: 'tags',
        align: 'center',
        width: 220,
        dataIndex: 'timeList',
        render: timeList => (
          <>
            {timeList.map((item, index) => {
              return (
                <div><Text>{"星期" + getCateName(item.day, this.props.dayList) + " " + item.start + "-" + item.end}</Text></div>
              );
            })}
          </>
        ),
      },
      {
        title: '标签',
        key: 'tags',
        align: 'center',
        dataIndex: 'tagList',
        render: tagList => (
          <>
            {tagList.map((item, index) => {
              return (
                <Tag>{item}</Tag>
              );
            })}
          </>
        ),
      },
      {
        title: '审核时间',
        dataIndex: 'audit_time',
        align: 'center',
        width: 120,
        render: (val) => {
          return <div>{val == null ? '\\' : getTimeStr(val)}</div>;
        }
      },
    ];

    const opeUser: ColumnProps<any> = {
      title: '审核员',
      key: 'ope_user',
      width: 70,
      align: 'center',
      render: (record) => {
        if (record.operate != undefined && checkNotNull(record.operate.nickname)) {
          return <div>{record.operate.nickname}</div>;
        }
        return <div>\</div>;
      }
    }

    const opeMes: ColumnProps<any> = {
      title: '拒绝理由',
      key: 'ope_mes',
      width: 70,
      align: 'center',
      render: (record) => {
        if (record.operate != undefined && checkNotNull(record.operate.refuse_mes)) {
          return <div>{record.operate.refuse_mes}</div>;
        }
        return <div>\</div>;
      }
    }

    if (type == 1) {
      return columnsMerchant;
    } else if (type == 2) {
      columnsMerchant.push(opeUser);
      columnsMerchant.push(opeMes);
    } else {
      columnsMerchant.push(opeUser);
    }

    return columnsMerchant;
  }

  getStatusId(where: any) {
    for (const item of this.props.statusList) {
      if (item.where == where) {
        return item.id;
      }
    }
    return 1;
  }

  onChangeRadio = e => {
    const where = e.target.value;
    this.setState({ where });
    this.setState({ merchantColumns: this.makeColumnsMerchant(this.getStatusId(where)) });
    this.setState({ currentPage: 1 });
    this.setState({ currentMerchantId: null });
    this.setState({ currentMerchantValue: "" });
    const req = { tabKey: this.state.tabKey, current: 1, pageCount: PAGESIZE, where, currentMerchantId: null };
    this.dispatchMerchantList(req);
    this.dispatchMerchantListCount(req);
  };

  onSelectMerchant = (value) => {
    const { where } = this.state;

    const val = value.substring(4, value.indexOf(" 名称"));
    this.setState({ currentMerchantId: val });
    this.setState({ currentMerchantValue: value });
    this.setState({ where: "" });
    this.setState({ currentPage: 1 });
    const req = { tabKey: this.state.tabKey, current: 1, pageCount: PAGESIZE, where: "", currentMerchantId: val };
    this.dispatchMerchantList(req);
    this.dispatchMerchantListCount(req);
  };

  onChangeMerchant = (data: string) => {
    this.setState({ currentMerchantValue: data });
  };

  // handleSearch = () => {
  //   this.setState({ merchantColumns: this.makeColumnsMerchant(this.getStatusId(this.state.where)) });
  //   this.changePage(1);
  //   const req = { tabKey: this.state.tabKey, current: 1, pageCount: PAGESIZE, where: this.state.where };
  //   this.dispatchMerchantList(req);
  //   this.dispatchMerchantListCount(req);
  // };

  refuseMerchant = (merchant) => {
    this.setState({ refuseModalType: 1 });
    this.setState({ refuseMes: "" });
    this.setState({ currentMerchant: merchant });
  }

  updateMerchant = (merchant) => {
    this.setState({ updateModalType: 1 });
    this.setState({ currentMerchant: merchant });
  }

  onCloseRefuseModel = () => {
    this.setState({ refuseModalType: 0 });
    this.setState({ refuseMes: "" });
  }

  onRefuseMesChange = (str) => {
    this.setState({ refuseMes: str });
  }

  updateMerchantDelsign = (merchant) => {
    const { dispatch } = this.props;
    const req = {
      data: {
        merchantId: merchant.id
      },
      current: this.state.currentPage,
      pageCount: PAGESIZE,
      tabKey: this.state.tabKey,
      where: this.state.where,
    }
    console.log("req", req);
    dispatch({
      type: 'scriptkill/updateMerchantDelsign',
      payload: req
    }).then(() => {
      this.dispatchMerchantStatusCount(this.state.tabKey);
    });
  }

  refuseMerchantAction = () => {
    const { dispatch } = this.props;
    const { currentMerchant } = this.state;

    const request: any = {
      id: currentMerchant.id,
      current: this.state.currentPage,
      pageCount: PAGESIZE,
      tabKey: this.state.tabKey,
      where: this.state.where,
      refuseMes: this.state.refuseMes,
      operateUserId: this.props.uid,
    };

    console.log("request", request);

    dispatch({
      type: 'scriptkill/refuseMerchantPassed',
      payload: request
    }).then(() => {
      this.onCloseRefuseModel();
      this.dispatchMerchantStatusCount(this.state.tabKey);
    });
  }

  changePage = (page) => {

    this.setState({ currentPage: page });
    const req = { tabKey: this.state.tabKey, current: page, pageCount: PAGESIZE, where: this.state.where, currentMerchantId: this.state.currentMerchantId };
    this.dispatchMerchantList(req);

  };

  handleTabChange = value => {
    const { uid, dispatch } = this.props;
    const { currentMerchantId, where } = this.state;
    const key = Number(value);
    this.setState({ tabKey: key });
    this.setState({ currentPage: 1 });
    const req = { tabKey: key, current: 1, pageCount: PAGESIZE, where, currentMerchantId };
    this.dispatchMerchantList(req);
    this.dispatchMerchantListCount(req);
    this.dispatchMerchantStatusCount(key);
  };

  dispatchMerchantList = (req) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getMerchantList',
      payload: req
    });
  };

  dispatchMerchantListCount = (req) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getMerchantListCount',
      payload: req
    });
  };

  dispatchMerchantStatusCount = (tabKey) => {
    for (const item of this.props.statusList) {
      item.tabKey = tabKey;
      this.dispatchMerchantListCount(item);
    }
  }

  dispatchMerchantSimpleList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getMerchantSimpleList',
    });
  };

  dispatchCityList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getCityList',
    });
  };

  onClickEdit = (merchant: any) => {
    const { dispatch } = this.props;

    dispatch({
      type: 'scriptkill/getScriptList',
      payload: merchant
    }).then(() => {
      this.setState({ dataModelType: 1, currentMerchant: merchant });
    });
  };

  onClickEditImg = (merchant: any) => {
    this.setState({ imageModelType: 1, currentMerchant: merchant });
  };

  onClickViewAniImg = (merchant: any) => {
    this.setState({ imageViewModelType: 1, currentMerchant: merchant });
  };

  onClickClose = () => {
    this.setState({ updateModalType: -1, dataModelType: -1, imageModelType: -1, imageViewModelType: -1, currentMerchant: null });
  };
}

class RefuseModal extends React.Component<any, any> {
  render() {

    const { refuseModalType, isLoading } = this.props;
    return (
      <Modal
        width={800}
        bodyStyle={{ padding: '5px 10px 5px 10px' }}
        closable={true}
        maskClosable={true}
        confirmLoading={!!isLoading}
        centered={true}
        title="填写驳回理由"
        onCancel={this.props.onCloseRefuseModel}
        footer={[]}
        visible={refuseModalType > 0 ? true : false}
      >
        <div>
          <TextArea showCount={true} maxLength={100}
            onChange={e => {
              this.props.onRefuseMesChange(e.target.value)
            }} />
          <Popconfirm
            title={`确定要驳回商铺吗?`}
            onConfirm={this.props.refuseMerchantAction}
            okText="Yes"
            cancelText="No"
          >
            <Button className={styles.marginLeft} type="primary" danger={true}>驳回</Button>
          </Popconfirm>
        </div>
      </Modal>
    )
  }
}
