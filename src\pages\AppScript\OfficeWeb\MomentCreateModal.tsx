import { Modal, Form, Input, Select, DatePicker, Spin, Switch, message } from 'antd';

import React, { RefObject, Component } from 'react';
import moment from 'moment';
import { getTimeMoment, getTimeStr, getDayStr } from '@/utils/momentTool';
import { IofficeNews } from '@/dto/wfOfficeNews';
import { OfficeNewsTagName } from './models/merchantMoment';
import { getUUID } from '@/utils/utils';
import RichTxtEdit from './RichTxtEdit';
import * as styles from './MomentCreateModal.less'
import AppList from '../../../pages/AppList';

const OSS = require('ali-oss');
const STS = OSS.STS;


const { Option } = Select;
const { TextArea } = Input;


interface INewsCreateModalProps {
  news: IofficeNews;
  isEditeModal: boolean;
  loading: boolean;
  dispatch: Function;
  onHandleCreate: (req: IofficeNews) => void;
  onHandleEdit: (req: IofficeNews) => void;
  onHandleClose: () => void;
}


const dateFormat = 'YYYY-MM-DD HH:mm:ss';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 }
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 }
  }
};


class MomentCreateModal extends Component<any, any> {
  private formRef: RefObject<Form> = React.createRef();
  private modalTitle = '新建';
  private initTitle = '';
  private initContent = '';
  private okText = '新建';
  private is_new = 0;
  private news_tag = 1;
  private oss_path_key = getUUID(12);
  private topic_name = '';

  constructor(props) {
    super(props);
    this.state = {

      loading: false,
      token: {
        access_key_id: 'LTAI4FrMp2q7twEfV27orLvM', // oss的key_id
        access_key_secret: '******************************', // oss的secret
        OSS_ENDPOINT: 'http://oss-cn-hangzhou-internal.aliyuncs.com',  // 自己oss服务器的配置信息
        OSS_BUCKET: 'werewolf-headicon', // 自己oss服务器的配置信息
      }
    }

    console.log(this.props)
    //编辑
    if (this.props.isEditeModal) {
      this.modalTitle = `编辑ID ${props.news.id}`;
      this.initTitle = props.news.title;
      this.initContent = props.news.contentBody;
      this.okText = '编辑';
      this.topic_name = props.news.topic_name;
      this.oss_path_key = props.news.oss_path_key;

      // this.is_show = props.news.is_show;
      // this.news_tag = props.news.news_tag;
      // this.is_new = props.news.is_new;
      // this.url = props.news.url;
    }
  }

  // 发送成功
  onClickOk = () => {

    const { dispatch, isEditeModal, onHandleCreate, onHandleEdit, news } = this.props;

    this.formRef.current
      .validateFields()
      .then(values => {

        let content = `<!DOCTYPE html>
        <html>
        <head>
        <meta charset="utf-8">
        <title>${values.title}</title>
        </head>
        <body style=" font-size: 3.2vw; " >`;
        content += values.contentBody;
        content += `</body>
        </html>`;

        const html = `01script/community/${getDayStr()}/${this.oss_path_key}/${this.oss_path_key}.html`;
        const htmlUrl = AppList.imageOssScriptSkill + html;
        this.uploadHtml(content, html);

        if (isEditeModal) {
          const officeNews: IofficeNews = {
            title: values.title,
            topic_name: values.topic_name,
            material_url: htmlUrl,
            id: news ? news.id : 0,
            oss_path_key: this.oss_path_key,
            material_id: this.props.news.material_id,
          };
          console.log('发送内容', officeNews);
          onHandleEdit(officeNews);
        } else {
          const officeNews: IofficeNews = {
            title: values.title,
            material_url: htmlUrl,
            topic_name: values.topic_name,
            id: news ? news.id : 0,
            oss_path_key: this.oss_path_key,
          };
          console.log('发送内容', officeNews);
          onHandleCreate(officeNews);
        }
      })
      .catch(info => {
        console.error(info);
      });
  };

  //选择新闻类型类型
  onNewsTagChange = value => {
    this.formRef.current.setFieldsValue({
      news_tag: value
    });
    console.log('new News type ', value);
    this.formRef.current.validateFields();
    // this.setState({ currNewsType: value });
  };

  render() {
    const { onHandleClose, isEditeModal, news } = this.props;
    let defaultHtml = "";
    let draftKey = "create";
    if (isEditeModal) {
      defaultHtml = news.contentBody;
      draftKey = news.id ? news.id + "" : "1";
    }
    return (

      <Modal
        visible={true}
        centered={true}
        title={this.modalTitle}
        onOk={this.onClickOk}
        onCancel={onHandleClose}
        okText={this.okText}
        width={1840}
        cancelText="取消"
      >
        <Spin spinning={this.props.loading}>
          <div className={styles.createModal}>
            <div className={styles.news_form}>
              <Form
                ref={this.formRef}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 16 }}
                layout="horizontal"
                name="validate_other"
                initialValues={{
                  title: this.initTitle,
                  contentBody: this.initContent,
                  is_show: this.is_show,
                  is_new: this.is_new,
                  news_tag: this.news_tag,
                  topic_name: this.topic_name
                }}
              >

                <Form.Item
                  name="title"
                  label="标题"
                  rules={[{ required: true, message: '请输入新闻的标题', type: 'string' }]}
                >
                  <Input />
                </Form.Item>

                <Form.Item
                  name="contentBody"
                  label="内容"
                  rules={[{ required: true, message: '请输入新闻的内容，大于8个字', type: 'string', min: 8 }]}
                >
                  <TextArea disabled={true} rows={20} />
                </Form.Item>

                <Form.Item
                  name="topic_name"
                  label="话题"
                  rules={[{ required: false, message: '请输入话题', type: 'string' }]}
                >
                  <Input />
                </Form.Item>
              </Form>
            </div>

            <div className={styles.news_edit}>
              <RichTxtEdit defaultValue={defaultHtml} draftKey={draftKey} onGetHtml={this.onGetHtml} oss_path_key={this.oss_path_key}/>
            </div>

          </div>
        </Spin>
      </Modal>
    );
  }

  onGetHtml = (html: string) => {
    this.formRef.current.setFieldsValue({
      contentBody: html
    });
    message.success("提取文本成功");
  }

  public async uploadHtml(content, name) {
    try {
      const oss = new OSS({
        accessKeyId: 'LTAI4FrMp2q7twEfV27orLvM',
        accessKeySecret: "******************************",
        bucket: 'zermatt',
      });
      const data = new OSS.Buffer(content);
      try {
        const result = await oss.put(name, data);
        // let result = await oss.put('01script/community/2021-01-05-15/test/test.html', data);
        console.log(result);
      } catch (e) {
        console.log(e);
      }
    } catch (e) {
      console.log(e)
    }
  }
}

export default MomentCreateModal;
