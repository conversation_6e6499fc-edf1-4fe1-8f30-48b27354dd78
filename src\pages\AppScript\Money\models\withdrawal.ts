/*
 * @Description //
 * @version 1.0.0
 * @Company sdbean
 * <AUTHOR>
 * @Date
 **/

import { StandardViewModelType } from '@/app';
import { getMerchantId, getMerchantMoney, getMerchantOrderBill, getWithdrawalMoney } from '@/services/apiScriptkill';
import { number } from 'prop-types';
import { message } from 'antd';

export interface MerchantOrderBill {
  id: number;
  orderId: number;
  userId: number;
  toUserId: number;
  payUserId: number;
  price: number;
  realPrice: number;
  showPrice: string;
  title: string;
  transactionChannel: number;
  payCompleteTime: number;
  status: number;
  type: number;
  productId: number;
  createTime: string;
  updateTime: string;
  delsign: number;
  exchangeOrderId: string;
  relateBillId: number;
  extractCompleteTime: string;
  extractNum: number;
}

export interface IWithdrawal {
  merchantOrderBillList: MerchantOrderBill[];
  merchantId: number;
  count: number;
  money: number;
}

const init: IWithdrawal = {
  merchantOrderBillList: [],
  merchantId: 0,
  count: 0,
  money: 0,
}

const model:StandardViewModelType = {
  namespace: 'withdrawal',
  state: init,
  effects: {
    *getMerchantOrderBill( {payload}, {call, put}) {
      const response = yield call(getMerchantOrderBill, payload);
      // 更新基础状态
      if (response) {
        yield put({ type: 'setMerchantOrderBill', payload: response.data.orderBillList});
      } else {
        yield put({ type: 'setMerchantOrderBill', payload: null });
      }
      const response1 = yield call(getMerchantMoney, payload);
      //更新基础状态
      if (response1) {
        yield put({ type: 'setMerchantMoney', payload: response1.data.wallet.waitScriptBean});
      } else {
        yield put({ type: 'setMerchantMoney', payload: 0 });
      }

    },
    *getWithdrawalMoney( {payload}, {call, put}) {
      const response = yield call(getWithdrawalMoney, payload);
      //更新基础状态
      if (response.sign == 1) {
        message.info("提现成功")
      }
      if (response.sign == 2004) {
        message.error("当前交易操作发生错误")
      }
    }
  },
  reducers: {
    setMerchantOrderBill(state: IWithdrawal, {payload}) {
      return {...state, merchantOrderBillList : payload}
    },
    setMerchantId(state: IWithdrawal, {payload} ) {
      return {...state, merchantId: payload}
    },
    setMerchantMoney(state: IWithdrawal, {payload} ) {
      return {...state, money: payload}
    }
  }
}

export default model
