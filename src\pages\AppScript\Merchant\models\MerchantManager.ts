/*
 * @Description:
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2020-04-24 13:36:09
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-12 14:59:14
 */

import { Igift, IitemDic, IitemCate, Ianimation, EanimationRoleEnum, EbagkgroundEnum, Imaskshow, Igroupbadge, IgroupFrame, IgroupBanner, IavatarFrame, InormalItem, IgroupProps, IgiftBag } from '@/dto/mallManager';

import { message } from 'antd';
import * as _ from "lodash";

import {
  updateScriptDelsign, 
  updateMerchantDelsign, 
  getCityList, 
  updateMerchant, 
  getReportInfoCount, 
  getReportInfo, 
  refuseUserBg, 
  getUserInfoBg, 
  refuseUserAvatar, 
  getUserInfoAvatar, 
  getUserInfo, 
  getScriptEditAuditFlowList, 
  getScriptEditAuditFlowListCount, 
  refuseMerchantPassed, 
  updateScriptUrl, 
  getMerchantDicList, 
  updateScript, 
  confirmMerchantTemp, 
  refuseMerchantTemp, 
  getMerchantTempListCount, 
  getMerchantTempList, 
  getOperateUserList, 
  getAuditFlowListCount, 
  getAuditFlowList, 
  insertPicture, 
  updatePicture, 
  updatePictureUrl, 
  getPictureList, 
  refuseScript, 
  refuseMerchant, 
  getScriptSimpleList, 
  confirmScript, 
  getScriptList, 
  getMerchantSimpleList, 
  getMerchantList, 
  confirmMerchant, 
  getMerchantListCount
} from '@/services/apiScriptkill';
import { tabEnum } from '../MerchantManager';
const difficultyList = [
  {
    id: 0,
    name: '不限',
  },
  {
    id: 1,
    name: '简单',
  },
  {
    id: 2,
    name: '中等',
  },
  {
    id: 3,
    name: '困难',
  },
];

const statusList = [
  {
    id: 1,
    name: '审核中',
    where: "m.status = 1",
    whereScript: "ms.status = 1",
  },
  {
    id: 2,
    name: '审核不通过',
    where: "m.status = 2",
    whereScript: "ms.status = 2",
  },
  {
    id: 10,
    name: '审核通过',
    where: "m.status = 10",
    whereScript: "ms.status = 10",
  },
];

const dayList = [
  {
    id: 1,
    name: '一',
  },
  {
    id: 2,
    name: '二',
  },
  {
    id: 3,
    name: '三',
  },
  {
    id: 4,
    name: '四',
  },
  {
    id: 5,
    name: '五',
  },
  {
    id: 6,
    name: '六',
  },
  {
    id: 7,
    name: '日',
  },
];

const reportTypeList = [
  {
    id: 0,
    name: '全部',
  },
  {
    id: 1,
    name: '头像',
  },
  {
    id: 2,
    name: '背景板',
  },
  {
    id: 3,
    name: '自定义内容',
  },
];

//已上架游戏板子
export interface Iscript {
  merchantList: any[];
  merchantSimpleList: any[];
  merchantAuditCount: number;
  merchantRefuseCount: number;
  merchantPassCount: number;
  merchantCount: number;
  statusList: any[];
  dayList: any[];
  currentMerchantScriptList: any[];
  scriptSimpleList: any[];
  pictureList: any[];
  auditFlowList: any[];
  auditFlowCount: number;
  operateUserList: any[];
  difficultyList: any[];
  merchantDicList: any[];
  scriptEditAuditFlowList: any[];
  scriptEditAuditFlowCount: number;
  userInfo: any;
  userAvatarList: any[];
  userBgList: any[];
  reportList: any[];
  reportCount: number;
  reportTypeList: any[];
  cityList: any[];
}

const init: Iscript = {
  merchantList: [],
  merchantSimpleList: [],
  statusList,
  dayList,
  difficultyList,
  merchantCount: 0,
  merchantAuditCount: 0,
  merchantRefuseCount: 0,
  merchantPassCount: 0,
  currentMerchantScriptList: [],
  scriptSimpleList: [],
  pictureList: [],
  auditFlowList: [],
  auditFlowCount: 0,
  operateUserList: [],
  merchantDicList: [],
  scriptEditAuditFlowList: [],
  scriptEditAuditFlowCount: 0,
  userInfo: {},
  userAvatarList: [],
  userBgList: [],
  reportList: [],
  reportCount: 0,
  reportTypeList,
  cityList: [],
}

export default {
  namespace: 'scriptkill',

  state: init,

  effects: {

    *getMerchantList({ payload }: { payload }, { call, put }) {
      let response = [];
      if (payload.tabKey == tabEnum.old) {
        response = yield call(getMerchantList, payload);
      } else if (payload.tabKey == tabEnum.new) {
        response = yield call(getMerchantTempList, payload);
      }

      if (response) {
        for (const item of response) {
          if (item.timeStr != null) {
            const levelList = [];
            const levelListStr = item.timeStr.split(',');
            for (const levelStr of levelListStr) {
              const levelS = levelStr.split('-');
              if (levelS.length > 2) {
                levelList.push({ id: item.id, day: levelS[0], start: levelS[1], end: levelS[2] });
              }
            }
            item.timeList = levelList;
          } else {
            item.timeList = [];
          }
          if (item.tagStr != null) {
            item.tagList = item.tagStr.split(',');
          } else {
            item.tagList = [];
          }
          if (item.telStr != null) {
            item.telList = item.telStr.split(',');
          } else {
            item.telList = [];
          }
        }


        for (const item of response) {
          item.tagListModal = [];
          for (let index = 0; index < item.tagList.length; index++) {
            const element = item.tagList[index];
            item.tagListModal.push({ ketIndex: index, content: element })
          }
        }

        yield put({ type: 'setMerchantList', payload: response });
      } else {
        yield put({ type: 'setMerchantList', payload: [] });
      }
    },

    *getMerchantSimpleList({ payload }: { payload }, { call, put }) {
      const response = yield call(getMerchantSimpleList, payload);
      if (response) {
        yield put({ type: 'setMerchantSimpleList', payload: response });
      } else {
        yield put({ type: 'setMerchantSimpleList', payload: [] });
      }
    },

    *getCityList({ payload }: { payload }, { call, put }) {
      const response = yield call(getCityList, payload);
      if (response) {
        yield put({ type: 'setcityList', payload: response });
      } else {
        yield put({ type: 'setcityList', payload: [] });
      }
    },

    *getMerchantListCount({ payload }: { payload }, { call, put }) {
      let response: any = {};
      if (payload.tabKey == tabEnum.old) {
        response = yield call(getMerchantListCount, payload);
      } else if (payload.tabKey == tabEnum.new) {
        response = yield call(getMerchantTempListCount, payload);
      }
      if (response) {
        if (payload.id == undefined) {
          yield put({ type: 'setMerchantListCount', payload: response.num });
        } else if (payload.id == 1) {
          yield put({ type: 'setMerchantAuditCount', payload: response.num });
        } else if (payload.id == 2) {
          yield put({ type: 'setMerchantRefuseCount', payload: response.num });
        } else if (payload.id == 10) {
          yield put({ type: 'setMerchantPassCount', payload: response.num });
        }
      } else {
        yield put({ type: 'setMerchantListCount', payload: 0 });
      }
    },

    *confirmMerchant({ payload }: { payload }, { call, put }) {
      let response = null;
      if (payload.tabKey == tabEnum.old) {
        response = yield call(confirmMerchant, payload);
      } else if (payload.tabKey == tabEnum.new) {
        response = yield call(confirmMerchantTemp, payload);
      }
      if (response) {
        message.success("确认成功!");
        yield put({ type: 'getMerchantList', payload });
        yield put({ type: 'getMerchantListCount', payload });
      } else {
        message.error("确认失败!");
      }
    },

    *refuseMerchantPassed({ payload }: { payload }, { call, put }) {
      const response = yield call(refuseMerchantPassed, payload);
      if (response) {
        message.success("驳回成功!");
        yield put({ type: 'getMerchantList', payload });
        yield put({ type: 'getMerchantListCount', payload });
      } else {
        message.error("驳回失败!");
      }
    },

    *getScriptList({ payload }: { payload }, { call, put }) {
      const response = yield call(getScriptList, payload);
      if (response && response.length > 0) {
        for (const item of response) {
          if (item.roleStr != null) {
            const roleList = [];
            const roleListStr = item.roleStr.split(',');
            for (const roleStr of roleListStr) {
              const roleS = roleStr.split('-mega-');
              if (roleS.length > 4) {
                roleList.push({ script_id: item.id, id: roleS[0], oss_url: roleS[1], role_desc: roleS[2], name: roleS[3], delsign: roleS[4] });
              }
            }
            item.roleList = roleList;
          } else {
            item.roleList = [];
          }

          if (item.themeStr != null) {
            const themeList = [];
            const themeListStr = item.themeStr.split(',');
            let ketIndex = 1;
            for (const themeStr of themeListStr) {
              const themeS = themeStr.split('-mega-');
              if (themeS.length > 1) {
                themeList.push({ value: `ID: ` + themeS[1] + ` 名称: ` + themeS[0], ketIndex, name: themeS[0], id: themeS[1] });
                ketIndex++;
              }
            }
            item.themeDicList = themeList;
          } else {
            item.themeDicList = [];
          }
        }
        yield put({ type: 'setCurrentMerchantScriptList', payload: response });
      } else {
        yield put({ type: 'setCurrentMerchantScriptList', payload: [] });
      }
    },

    *confirmScript({ payload }: { payload }, { call, put }) {
      const response = yield call(confirmScript, payload);
      if (response) {
        message.success("审核成功!");
        yield put({ type: 'getScriptList', payload });
      } else {
        message.error("审核失败!");
      }
    },

    *updateScript({ payload }: { payload }, { call, put }) {
      const response = yield call(updateScript, payload);
      if (response) {
        message.success("修改成功!");
        yield put({ type: 'getScriptList', payload });
      } else {
        message.error("修改失败!");
      }
    },

    *updateScriptUrl({ payload }: { payload }, { call, put }) {
      const response = yield call(updateScriptUrl, payload);
      if (response) {
        message.success("上传成功!");
        yield put({ type: 'getScriptList', payload });
      } else {
        message.error("上传失败!");
      }
    },

    *setScriptList({ payload }: { payload }, { call, put }) {
      yield put({ type: 'setCurrentMerchantScriptList', payload });
    },

    *getScriptSimpleList({ payload }: { payload }, { call, put }) {
      const response = yield call(getScriptSimpleList, payload);
      if (response) {
        yield put({ type: 'setScriptSimpleList', payload: response });
      } else {
        yield put({ type: 'setScriptSimpleList', payload: [] });
      }
    },

    *refuseMerchant({ payload }: { payload }, { call, put }) {
      let response = null;
      if (payload.tabKey == tabEnum.old) {
        response = yield call(refuseMerchant, payload);
      } else if (payload.tabKey == tabEnum.new) {
        response = yield call(refuseMerchantTemp, payload);
      }
      if (response) {
        message.success("操作成功!");
        yield put({ type: 'getMerchantList', payload });
        yield put({ type: 'getMerchantListCount', payload });
      } else {
        message.error("确认失败!");
      }
    },

    *updateMerchant({ payload }: { payload }, { call, put }) {
      const response = yield call(updateMerchant, payload);
      if (response) {
        message.success("操作成功!");
        yield put({ type: 'getMerchantList', payload });
        yield put({ type: 'getMerchantListCount', payload });
      } else {
        message.error("确认失败!");
      }
    },

    *refuseScript({ payload }: { payload }, { call, put }) {
      const response = yield call(refuseScript, payload);
      if (response) {
        message.success("操作成功!");
        yield put({ type: 'getScriptList', payload });
      } else {
        message.error("审核失败!");
      }
    },

    *getPictureList({ payload }: { payload }, { call, put }) {
      const response = yield call(getPictureList, payload);
      if (response) {
        yield put({ type: 'setPictureList', payload: response });
      } else {
        yield put({ type: 'setPictureList', payload: [] });
      }
    },

    *updatePictureUrl({ payload }: { payload }, { call, put }) {
      const response = yield call(updatePictureUrl, payload);
      if (response) {
        message.success("操作成功!");
        yield put({ type: 'getPictureList', payload });
      } else {
        message.error("操作失败!");
      }
    },

    *updatePicture({ payload }: { payload }, { call, put }) {
      const response = yield call(updatePicture, payload);
      if (response) {
        message.success("操作成功!");
        yield put({ type: 'getPictureList', payload });
      } else {
        message.error("操作失败!");
      }
    },

    *insertPicture({ payload }: { payload }, { call, put }) {
      const response = yield call(insertPicture, payload);
      if (response) {
        message.success("操作成功!");
        yield put({ type: 'getPictureList', payload });
      } else {
        message.error("操作失败!");
      }
    },

    *getAuditFlowList({ payload }: { payload }, { call, put }) {
      const response = yield call(getAuditFlowList, payload);
      if (response) {
        yield put({ type: 'setAuditFlowList', payload: response });
      } else {
        yield put({ type: 'setAuditFlowList', payload: [] });
      }
    },

    *getAuditFlowListCount({ payload }: { payload }, { call, put }) {
      const response = yield call(getAuditFlowListCount, payload);
      if (response) {
        yield put({ type: 'setAuditFlowCount', payload: response.num });
      } else {
        yield put({ type: 'setAuditFlowCount', payload: 0 });
      }
    },

    *getOperateUserList({ payload }: { payload }, { call, put }) {
      const response = yield call(getOperateUserList, payload);
      if (response) {
        yield put({ type: 'setOperateUserList', payload: response });
      } else {
        yield put({ type: 'setOperateUserList', payload: [] });
      }
    },

    *getMerchantDicList({ payload }: { payload }, { call, put }) {
      const response = yield call(getMerchantDicList, payload);
      if (response) {
        yield put({ type: 'setMerchantDicList', payload: response });
      } else {
        yield put({ type: 'setMerchantDicList', payload: [] });
      }
    },

    *getScriptEditAuditFlowList({ payload }: { payload }, { call, put }) {
      const response = yield call(getScriptEditAuditFlowList, payload);
      if (response) {
        yield put({ type: 'setScriptEditAuditFlowList', payload: response });
      } else {
        yield put({ type: 'setScriptEditAuditFlowList', payload: [] });
      }
    },

    *getScriptEditAuditFlowListCount({ payload }: { payload }, { call, put }) {
      const response = yield call(getScriptEditAuditFlowListCount, payload);
      if (response) {
        yield put({ type: 'setScriptEditAuditFlowCount', payload: response.num });
      } else {
        yield put({ type: 'setScriptEditAuditFlowCount', payload: 0 });
      }
    },

    *getUserInfo({ payload }: { payload }, { call, put }) {
      const response = yield call(getUserInfo, payload);
      if (response) {
        yield put({ type: 'setUserInfo', payload: response });
      } else {
        yield put({ type: 'setUserInfo', payload: {} });
      }
    },

    *getUserInfoAvatar({ payload }: { payload }, { call, put }) {
      const response = yield call(getUserInfoAvatar, payload);
      if (response) {
        yield put({ type: 'setUserAvatarList', payload: response });
      } else {
        yield put({ type: 'setUserAvatarList', payload: [] });
      }
    },

    *refuseUserAvatar({ payload }: { payload }, { call, put }) {
      const response = yield call(refuseUserAvatar, payload);
      if (response) {
        message.success("操作成功");
        if (payload.isReport) {
          yield put({ type: 'getReportInfo', payload });
          yield put({ type: 'getReportInfoCount', payload });
        } else {
          yield put({ type: 'getUserInfo', payload });
          yield put({ type: 'getUserInfoAvatar', payload });
        }
      } else {
        message.error("操作失败");
      }
    },

    *getUserInfoBg({ payload }: { payload }, { call, put }) {
      const response = yield call(getUserInfoBg, payload);
      if (response) {
        yield put({ type: 'setUserBgList', payload: response });
      } else {
        yield put({ type: 'setUserBgList', payload: [] });
      }
    },

    *refuseUserBg({ payload }: { payload }, { call, put }) {
      const response = yield call(refuseUserBg, payload);
      if (response) {
        message.success("操作成功");
        if (payload.isReport) {
          yield put({ type: 'getReportInfo', payload });
          yield put({ type: 'getReportInfoCount', payload });
        } else {
          yield put({ type: 'getUserInfoBg', payload });
        }
      } else {
        message.error("操作失败");
      }
    },

    *getReportInfo({ payload }: { payload }, { call, put }) {
      const response = yield call(getReportInfo, payload);
      if (response) {
        yield put({ type: 'setReportList', payload: response });
      } else {
        yield put({ type: 'setReportList', payload: [] });
      }
    },

    *getReportInfoCount({ payload }: { payload }, { call, put }) {
      const response = yield call(getReportInfoCount, payload);
      if (response) {
        yield put({ type: 'setReportCount', payload: response.num });
      } else {
        yield put({ type: 'setReportCount', payload: 0 });
      }
    },

    *updateMerchantDelsign({ payload }: { payload }, { call, put }) {
      const response = yield call(updateMerchantDelsign, payload);
      console.log("response", response);

      if (response && response.sign && response.sign == 1) {
        message.success("下架成功!");
        yield put({ type: 'getMerchantList', payload });
        yield put({ type: 'getMerchantListCount', payload });
      } else {
        message.error("下架失败!");
      }
    },

    *updateScriptDelsign({ payload }: { payload }, { call, put }) {
      const response = yield call(updateScriptDelsign, payload);
      console.log("response", response);
      if (response && response.sign && response.sign == 1) {
        message.success("下架成功!");
        yield put({ type: 'getScriptList', payload });
      } else {
        message.error("下架失败!");
      }
    },
  },

  reducers: {

    setMerchantList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, merchantList: payload }
    },

    setMerchantSimpleList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, merchantSimpleList: payload }
    },

    setMerchantListCount(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, merchantCount: payload }
    },

    setMerchantAuditCount(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, merchantAuditCount: payload }
    },

    setMerchantRefuseCount(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, merchantRefuseCount: payload }
    },

    setMerchantPassCount(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, merchantPassCount: payload }
    },

    setCurrentMerchantScriptList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, currentMerchantScriptList: payload }
    },

    setScriptSimpleList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, scriptSimpleList: payload }
    },

    setPictureList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, pictureList: payload }
    },

    setAuditFlowList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, auditFlowList: payload }
    },

    setAuditFlowCount(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, auditFlowCount: payload }
    },

    setScriptEditAuditFlowList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, scriptEditAuditFlowList: payload }
    },

    setScriptEditAuditFlowCount(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, scriptEditAuditFlowCount: payload }
    },

    setOperateUserList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, operateUserList: payload }
    },

    setMerchantDicList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, merchantDicList: payload }
    },

    setUserInfo(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, userInfo: payload }
    },

    setUserAvatarList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, userAvatarList: payload }
    },

    setUserBgList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, userBgList: payload }
    },

    setReportList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, reportList: payload }
    },

    setReportCount(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, reportCount: payload }
    },

    setcityList(state: Iscript, { payload }: { payload }): Iscript {
      return { ...state, cityList: payload }
    },
  },





}
