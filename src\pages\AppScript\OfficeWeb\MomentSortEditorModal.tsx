/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/jsx-filename-extension */
/* eslint-disable react/sort-comp */
/*
 * @Description: 称号-编辑模态页
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-10-12 16:56:14
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-19 15:30:59
 */

import React, { Component } from 'react';
import { connect } from 'dva';
import { Modal, Switch, Input, Select, message, InputNumber, Button, AutoComplete, Form } from 'antd';
import * as styles from './MomentSortEditorModal.less';
const { v4: uuidv4 } = require('uuid');

// eslint-disable-next-line react/no-unused-prop-types
@connect(({ loading, login, exchangesys }: { loading: any; login: any; exchangesys: any; }) => ({
  uid: login.uid,
  isLoading: loading.models.exchangesys,
  projectList: exchangesys.projectList,
}))

class MomentSortEditorModal extends React.Component<any, any> {
  constructor(props) {
    super(props);
    const { dataModelType } = props;
  }

  render() {
    const { isShowSortModal, current, isLoading } = this.props;
    let title = '';
    title = '编辑排序';
    return (
      <Modal
        width={550}
        bodyStyle={{ padding: '5px 10px 5px 10px' }}
        closable={false}
        maskClosable={false}
        confirmLoading={!!isLoading}
        centered={true}
        title={title}
        footer={null}
        visible={isShowSortModal}
      >
        <div>
          {this.renderEditModal()}
        </div>
      </Modal>
    );
  }

  renderEditModal = () => {
    const { dataModelType, projectList, current } = this.props;
    const Option = Select.Option;
    const init = current;

    const layout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
    };
    const tailLayout = {
      wrapperCol: { offset: 16, span: 16 },
    };

    return (
      <div>
        <Form
          {...layout}
          name="award"
          initialValues={init}
          onFinish={this.onClickOk}
          onFinishFailed={this.onFinishFailed}
        >
          <Form.Item
            label="排序值"
            name="is_recommend"
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item {...tailLayout}>
            <Button onClick={this.props.onClickClose}>
              取消
            </Button>
            <Button style={{ marginLeft: 10 }} type="primary" htmlType="submit">
              提交
            </Button>
          </Form.Item>
        </Form>
      </div>
    );
  };

  getUUID = (len) => {
    const strUUID = uuidv4(); // ⇨ '1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed'
    const strUUID2 = strUUID.replace(/-/g, '');
    return strUUID2.substring(0, len);
  }

  onFinishFailed = errorInfo => {
    console.log('Failed:', errorInfo);
    // eslint-disable-next-line react/destructuring-assignment
    if (this.props.dataModelType == 1) {
      message.error('修改失败!');
    } else {
      message.error('新增失败!');
    }
  };

  onClickOk = (values) => {
    console.log('Success:', values);
    const { dispatch, current, dataModelType } = this.props;
    const req = {
      id: current.id,
      is_recommend: values.is_recommend,
      current: this.props.currentPage,
      pageCount: this.props.pageCount,
    }
    console.log("req", req);
    dispatch({
      type: 'merchantMoment/updateMomentSort',
      payload: req
    }).then(() => {
      this.props.onClickClose();
    });

  };
}

export default MomentSortEditorModal;
