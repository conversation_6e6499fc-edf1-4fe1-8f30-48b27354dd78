/*
 * @Description: 违规刷分，对局详情
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-05
 * @LastEditors: 赵宝强
 * @LastEditTime:
 */
import React, { Component } from 'react';
import { Button, DatePicker, Form, Input, Modal, Select } from 'antd';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import { IMainPageBanner, OpenAd } from '@/pages/AppScript/Banner/models/mainPageBanner';
import moment from 'moment';
import { FormInstance } from 'antd/lib/form';
const { Option } = Select;

export interface UpdateOpenAdInfoState {
  selectType: number,
  selectPage: number,
}
export interface UpdateOpenAdInfoProps {
  isUpdateOpenAdInfo: boolean,
  currentOpenAd: OpenAd,
  onClose: () => void,
}

@connect(({ loading, login, mainPageBanner }: { loading: IdvaLoading; login: ILogin; mainPageBanner: IMainPageBanner; }) => ({
  isLoading: loading.models['mainPageBanner'],
  currentOpenAd: mainPageBanner.currentOpenAd,

}))

export default class UpdateOpenAdInfo extends Component<UpdateOpenAdInfoProps, UpdateOpenAdInfoState> {
  formRef = React.createRef<FormInstance>();
  constructor(props) {
    super(props);
    this.state = {
      selectType: this.props.currentOpenAd.type,
    };

  }

  render (){
    const{ isUpdateOpenAdInfo, currentOpenAd } = this.props;
    console.log('fsdfsd'+currentOpenAd.id)
    const { selectType } = this.state;
    const layout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
    };
    const tailLayout = {
      wrapperCol: { offset: 8, span: 16 },
    };

    const { RangePicker } = DatePicker;
    const onFinish = (values) => {
      const value = {
        id: Number(currentOpenAd.id),
        open_ad_name: values.open_ad_name,
        href_url: values.href_url,
        type: values.type,
        open_ad_start_time: values.time[0].format('YYYY-MM-DD HH:mm:ss'),
        open_ad_end_time: values.time[1].format('YYYY-MM-DD HH:mm:ss')

      };
      this.props.dispatch({
        type: 'mainPageBanner/updateOpenAdInfo',
        payload: value
      }).then(this.onCloseModel);
    };

    // @ts-ignore
    return (
      <Modal
        closable={true}
        maskClosable={false}
        centered={true}
        title={'开屏广告信息修改'}
        onCancel={this.onCloseModel}
        footer={[]}
        visible={isUpdateOpenAdInfo}
        width='800px'
      >
        <Form
          {...layout}
          name="basic"
          initialValues={{remember: true}}
          onFinish={onFinish}
        >
          <Form.Item
            label="开屏名称"
            name="open_ad_name"
            initialValue={currentOpenAd.open_ad_name}
            rules={[{ required: false}]}
          >
            <Input />
          </Form.Item>
          <Form.Item name={'type'} label="跳转类型" rules={[{ required: false }]} initialValue={currentOpenAd.type}>
            <Select onChange={this.handleTypeOptionChange} >
              <Option value={1}>店铺</Option>
              <Option value={2}>剧本</Option>
              <Option value={3}>图片URL跳转</Option>
              <Option value={4}>文章</Option>
              <Option value={5}>订单</Option>
            </Select>
          </Form.Item>
            <Form.Item name={'href_url'} label="跳转地址" rules={[{ required: true, message: '请输入跳转地址' }]} initialValue={currentOpenAd.href_url}>
                <Input />
            </Form.Item>
          <Form.Item name={'time'} label="开始时间" rules={[{ required: false }]} initialValue={[moment(currentOpenAd.open_ad_start_time), moment(currentOpenAd.open_ad_end_time)]}>
            <RangePicker showTime={true} format="YYYY-MM-DD HH:mm:ss" />
          </Form.Item>
          <Form.Item {...tailLayout}>
            <Button type="primary" htmlType="submit">
              提交
            </Button>

          </Form.Item>
        </Form>
      </Modal>

    );
  }
  //下拉框选择
  handleTypeOptionChange = (e) => {
    console.log(e);
    this.setState({
      selectType: e
    });
  };

  handlePageOptionChange = (e) => {
    console.log(e);
    this.setState({
      selectPage: e
    });
  };

  //关闭模态页
  onCloseModel = () => {
    this.props.onClose();
    window.location.reload();
  };

}



