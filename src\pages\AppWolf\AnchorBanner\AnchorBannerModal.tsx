/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-09 13:15:11
 * @LastEditTime: 2020-11-18 14:45:06
 * @LastEditors: zhanglu
 */

import React from 'react';
import { ILogin } from '@/models/login';
import { Modal, Form, Input, Steps, Button, Radio, Upload, message, Result } from 'antd';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import { IAnchorBanner } from './models/anchorBanner';
import { UploadBannerStep } from "@/dto/anchorBanner"
import * as styles from './AnchorBanner.less';
import { FormInstance } from 'antd/lib/form';
import AppList from '../../../pages/AppList';
import { UploadPath } from '@/dto/staticEnum';
const { Step } = Steps;

interface IAnchorBannerModalProps {
    dispatch?: Function
    onClose: Function
    showModal?: number
    uploadStep?: UploadBannerStep,
    isBaseing?: <PERSON><PERSON><PERSON>,
    isCompleteing?: <PERSON><PERSON><PERSON>,
    isNewBannerModal: boolean,
    imageName: string
    itemId: number
}
interface IAnchorBannerModalState {
    baseInfo: any
    imageUrl: any
    loading: Boolean
}
@connect(({ anchorBanner, loading }: { anchorBanner: IAnchorBanner; loading: IdvaLoading }) => ({
    // isLoading: loading.models['rongCloudControl'],
    anchorBannerList: anchorBanner.anchorBannerList,
    uploadStep: anchorBanner.uploadStep,
    isBaseing: loading.effects['anchorBanner/uploadNewBannerBaseInfo'],
    isCompleteing: loading.effects['anchorBanner/uploadNewBannerBaseInfo'],
    imageName: anchorBanner.imageName,
    itemId: anchorBanner.itemId
}))

class AnchorBannerModal extends React.Component<IAnchorBannerModalProps, IAnchorBannerModalState> {
    formRef = React.createRef<FormInstance>();
    constructor(props) {
        super(props)
        this.state = {
            baseInfo: '',
            imageUrl: '',
            loading: false,
        }
    }
    //渲染模态框
    render() {
        const { uploadStep, isNewBannerModal } = this.props
        const title = '新建banner';
        return (
            <div>
                <Modal
                    title={title}
                    onCancel={this.handleCancel}
                    width={1200}
                    footer={null}
                    visible={isNewBannerModal}
                >
                    {this.renderSteps()}
                    {uploadStep == UploadBannerStep.Base && this.renderStepDefault()}
                    {uploadStep == UploadBannerStep.Upload && this.renderStepUpload()}
                    {uploadStep == UploadBannerStep.Success && this.renderSuccess()}
                </Modal>
            </div>
        )
    }
    //渲染步骤条
    renderSteps() {
        return (
            <Steps current={this.props.uploadStep}>
                <Step title="基础信息" description="填写banner基础信息" />
                <Step title="上传图片" description="上传banner对应图片" />
                <Step title="成功" description="banner信息上传成功" />
            </Steps>
        )
    }
    //渲染基础表单页
    renderStepDefault = () => {
        const layout = {
            labelCol: { span: 5 },
            wrapperCol: { span: 18 }
        };
        const validateMessages = {
            required: '请填写${label}'
        };
        const onFinish = (values) => {
            const value = {
                sort: Number(values.sort),
                remark: values.remark,
                content: values.content,
                recommend_tag: Number(values.recommend_tag),
                show_type: Number(values.show_type),
                turn_to_page: Number(values.turn_to_page),
                page_url: values.url
            };
            this.setState({
                baseInfo: value
            });
            this.props.dispatch({
                type: 'anchorBanner/fetchCreateAnchorBanner',
                payload: value
            });
            this.formRef.current.resetFields();
        };
        return (
            <div className={styles.step_div}>
                <Form
                    className={styles.stepForm}
                    {...layout}
                    ref={this.formRef}
                    onFinish={onFinish}
                    validateMessages={validateMessages}
                    initialValues={{
                        type: 0,
                        page: 0
                    }}>
                    <Form.Item name={'sort'} label="序号" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name={'remark'} label="主播名称" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name={'content'} label="展示文案" rules={[{ required: true }]}>
                        <Input.TextArea />
                    </Form.Item>
                    <Form.Item name="url" label="直播间url" rules={[{ required: true }]}>
                        <Input.TextArea />
                    </Form.Item>
                    <Form.Item name={'recommend_tag'} label="标签类型" rules={[{ required: true }]}>
                        <Radio.Group name="radiogroup">
                            <Radio value={1}>主播成长</Radio>
                            <Radio value={2}>推荐</Radio>
                            <Radio value={3}>招募</Radio>
                            <Radio value={4}>活动</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item name={'show_type'} label="显示类型" rules={[{ required: true }]}>
                        <Radio.Group name="radiogroup">
                            <Radio value={1}>画廊</Radio>
                            <Radio value={2}>列表</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item name={'turn_to_page'} label="跳转类型" rules={[{ required: true }]}>
                        <Radio.Group name="radiogroup">
                            <Radio value={1}>系统内置</Radio>
                            <Radio value={2}>App跳转</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <div className={styles.submit_btn}>
                        <Button type="primary" htmlType="submit" loading={!!this.props.isBaseing}>
                            下一步
						</Button>
                    </div>
                </Form>
            </div>
        );
    }
    //渲染上传图片
    renderStepUpload = () => {
        const { loading, imageUrl } = this.state;
        const uploadUrl = '/megaupload';
        const uploadButton = (
            <div>
                {loading ? <LoadingOutlined /> : <PlusOutlined />}
                <div style={{ marginTop: 8 }}>Upload</div>
            </div>
        );
        return (
            <div className={styles.step_div}>
                <Upload
                    action={uploadUrl}
                    headers={{ 'Access-Control-Allow-Origin': '*', 'X-Requested-With': null }}
                    listType="picture-card"
                    showUploadList={false}
                    className={styles.uploadDiv}
                    data={{
                        type: UploadPath.ossH,
                        name: this.props.imageName
                    }}
                    onChange={this.handleChange}
                >
                    {imageUrl ? <img src={imageUrl} alt="avatar" style={{ width: '100%' }} /> : uploadButton}
                </Upload>

                <div className={styles.submit_btn}>
                    <Button type="primary" onClick={this.handleSubmitStepUpload} loading={!!this.props.isCompleteing}>
                        下一步
					</Button>
                </div>
            </div>
        );
    }
    handleChange = (info) => {
        if (info.file.status === 'uploading') {
            this.setState({ loading: true });
            return;
        }
        if (info.file.status === 'done') {
            this.getBase64(info.file.originFileObj, imageUrl =>
                this.setState({
                    imageUrl,
                    loading: false,
                }, () => {
                    console.log(imageUrl)
                }),
            );
            message.success(`${info.file.name} 上传成功`);
        } else if (info.file.status === 'error') {
            message.error(`${info.file.name} 上传失败`);
        }
    }
    getBase64 = (img, callback) => {
        const reader = new FileReader();
        reader.addEventListener('load', () => callback(reader.result));
        reader.readAsDataURL(img);
    }
    //渲染成功页
    renderSuccess() {
        return (
            <Result
                status="success"
                title="操作成功"
                subTitle="新增主播成功"
                extra={[
                    <Button type="primary" key="success" onClick={this.handleCancel}>完成</Button>,
                ]}
            />
        );
    }
    //提交图片的地址
    handleSubmitStepUpload = () => {
        this.props.dispatch({
            type: 'anchorBanner/fetchUploadBannerImg',
            payload: {
                id: this.props.itemId,
                img_url: `${AppList.imageOssH}${this.props.imageName}.png`
            }
        });
        this.props.dispatch({
            type: 'anchorBanner/fetchAnchorBannerList'
        })
    };
    //下一步
    handleCancel = () => {
        this.props.dispatch({
            type: 'anchorBanner/setUploadStep',
            payload: UploadBannerStep.Base
        });
        this.props.onClose()
    }
}
export default AnchorBannerModal

