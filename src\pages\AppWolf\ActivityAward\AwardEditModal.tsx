/*
 * @Description: 称号-编辑模态页
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2019-10-12 16:56:14
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-17 09:35:59
 */

import { Component } from 'react';
import { connect } from 'dva';
import { ILogin } from '@/models/login';
import { Modal, Switch, Input, Select, message, InputNumber, AutoComplete, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import * as styles from './AwardEditModal.less';
import { wfPropsGiftSourceType, wfPropsGiftType, wfPropsType } from "../../../dto/staticEnum";
import { Igift } from '@/dto/mallManager';
import { getGiftSourceStr, checkNameExist } from '@/utils/mallTool';
import { getCateName, getSelectName } from '@/utils/mallTool';
import React from 'react';
const { Option } = Select;

@connect(({ activityAward, mallItemManager, loading }: { activityAward: any; mallItemManager: any, loading: IdvaLoading }) => ({
  isLoading: loading.models['activityAward'],
  activityAward,
  itemDicList: mallItemManager.itemDicList,
  itemCateEditList: mallItemManager.itemCateEditList,
}))

class AwardEditModal extends React.Component<any, any> {
  constructor(props) {
    super(props);
    const { dataModelType, current } = props;

    if (dataModelType == 1) {
      this.state = {
        activity_id: current.activity_id,
        group_index: current.group_index,
        award_group_id: current.award_group_id,
        group_name: current.group_name,

        weight: null,
        target_num: null,
        is_limit: 0,
        num: null,
        award_name: null,
        max_times: null,
        desc: "",
        is_optional: 0,
        item_dic_id: null,
        headge_num: null,
        guarante_num: null,
      };
    } else {
      this.state = {
        activity_id: current.activity_id,
        award_group_id: current.award_group_id,
        group_name: current.group_name,
        award_id: current.award_id,

        weight: current.weight,
        target_num: current.target_num,
        is_limit: current.is_limit,
        max_times: current.max_times,
        desc: current.desc,
        is_optional: current.is_optional,
        headge_num: current.headge_num,
        guarante_num: current.guarante_num,

        num: null,
        award_name: null,
        item_dic_id: null,
      };
    }

  }

  render() {
    const { dataModelType, current, isLoading } = this.props;
    let title = '';
    let okText = '提交';
    if (dataModelType == 1) {
      title = `创建【${current.group_name} 】分组中 奖励档位`;
      okText = '提交创建';
    } else {
      title = `创建奖励`;
      okText = '提交创建';
    }
    return (
      <Modal
        bodyStyle={{ padding: '5px 10px 5px 10px' }}
        closable={false}
        maskClosable={false}
        confirmLoading={!!isLoading}
        centered={true}
        title={title}
        onCancel={this.onCloseModel}
        onOk={this.onClickOk}
        cancelText="取消"
        okText={okText}
        visible={dataModelType > 0 ? true : false}
      >
        <div>
          {dataModelType > 0 && this.renderEditModal()}
        </div>
      </Modal>
    );
  }

  // 渲染带有帮助提示的标题
  renderTitleWithHelp = (title: string, helpText: string) => {
    return (
      <div className={styles.title}>
        {title}
        <Tooltip title={helpText} placement="top">
          <QuestionCircleOutlined className={styles.helpIcon} />
        </Tooltip>
      </div>
    );
  };

  renderEditModal = () => {
    const { dataModelType } = this.props;
    const { itemCateEditList, itemDicList } = this.props;
    return (
      <div className={styles.modalForm}>
        <div className={styles.formItem}>
          {this.renderTitleWithHelp('选择道具类型：', '支持模糊搜索。选择奖励道具的分类类型，不同类型的道具有不同的属性和用途')}
          <Select
            className={styles.content}
            placeholder="请选择类型"
            defaultValue={getCateName(this.state.cate_id, itemCateEditList)}
            value={getCateName(this.state.cate_id, itemCateEditList)}
            onChange={this.onChangeCateId}
            showSearch
            optionFilterProp="children"
            filterOption={(input, option) =>
              (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
            }
          >
            {itemCateEditList.map((item, index) => {
              return (
                <Option key={index.toString()} value={itemCateEditList[index].id}>
                  {itemCateEditList[index].name}
                </Option>
              );
            })}
          </Select>
        </div>
        <div className={styles.formItem}>
          {this.renderTitleWithHelp('道具选择：', '支持模糊搜索。从已有的道具库中选择具体的奖励道具，支持搜索功能')}
          <AutoComplete
            allowClear={true}
            className={styles.content}
            options={this.state.optionList}
            onSelect={this.onChangeWeight}
            defaultValue={getSelectName(this.state.item_dic_id, itemDicList)}
            placeholder="请输入选择"
            filterOption={(inputValue, option) =>
              option.value.indexOf(inputValue) !== -1
            }
          />
        </div>
        <div className={styles.formItem}>
          {this.renderTitleWithHelp('数量：', '设置该奖励道具的发放数量，必须为正整数')}
          <InputNumber
            className={styles.content}
            value={this.state.num}
            onChange={this.onChangeNum}
          />
        </div>
        {dataModelType == 1 && <>
          <div className={styles.formItem}>
            {this.renderTitleWithHelp('权重：', '设置该奖励在抽奖池中的权重值，权重越高被抽中的概率越大')}
            <Input
              className={styles.content}
              value={this.state.weight}
              onChange={this.onChangeName}
            />
          </div>
          <div className={styles.formItem}>
            {this.renderTitleWithHelp('完成条件(价格或者任务数)：', '用户需要达到的条件才能获得此奖励，可以是消费金额或完成任务数量')}
            <InputNumber
              className={styles.content}
              value={this.state.target_num}
              onChange={this.onChangeSort}
            />
          </div>
          <div className={styles.formItem}>
            {this.renderTitleWithHelp('最多获得次数：', '单个用户最多可以获得此奖励的次数，0表示无限制')}
            <InputNumber
              className={styles.content}
              value={this.state.max_times}
              onChange={this.onChangemax_times}
            />
          </div>
          <div className={styles.formItem}>
            {this.renderTitleWithHelp('保值数：', '保证用户在一定次数内必定获得的奖励数量，用于保障用户体验')}
            <InputNumber
              className={styles.content}
              value={this.state.headge_num}
              onChange={this.onChangeCondition}
            />
          </div>
          <div className={styles.formItem}>
            {this.renderTitleWithHelp('保底数：', '用户在最坏情况下保证能获得的最低奖励数量')}
            <InputNumber
              className={styles.content}
              value={this.state.guarante_num}
              onChange={this.onChangeDescribe}
            />
          </div>
          <div className={styles.formItem}>
            {this.renderTitleWithHelp('描述：', '对该奖励的详细描述，用于向用户展示奖励信息')}
            <Input
              className={styles.content}
              value={this.state.desc}
              onChange={this.onChangedesc}
            />
          </div>
          <div className={styles.formItem}>
            {this.renderTitleWithHelp('是否限定奖励：', '开启后该奖励将成为限定奖励，具有特殊的获取条件和展示效果')}
            <Switch
              className={styles.toggle}
              checkedChildren="是"
              unCheckedChildren="否"
              defaultChecked={this.state.is_limit == 1}
              onChange={this.onChangesingle_purchase}
            />
          </div>
          <div className={styles.formItem}>
            {this.renderTitleWithHelp('是否二选一：', '开启后用户只能在多个奖励中选择其中一个，不能同时获得')}
            <Switch
              className={styles.toggle}
              checkedChildren="是"
              unCheckedChildren="否"
              defaultChecked={this.state.is_optional == 1}
              onChange={this.onChangeis_optional}
            />
          </div>
        </>
        }
      </div>
    );
  };

  onChangeCateId = (value) => {
    if (value != this.state.cate_id) {
      this.setState({ item_dic_id: -1 });
    }
    this.setState({ cate_id: value });
    this.setState({ optionList: this.getOptionList(value) });
  }

  getOptionList = (cateId) => {
    const optionList: any = [];
    for (let index = 0; index < this.props.itemDicList.length; index++) {
      const element = this.props.itemDicList[index];
      if (element.item_cate_id == cateId) {
        element.value = "ID: " + element.id + " 名称: " + element.name;
        optionList.push(element);
      }
    }

    return optionList;
  };

  onChangesingle_purchase = (checked: boolean) => {
    this.setState({ is_limit: checked ? 1 : 0 });
  };

  onChangeis_optional = (checked: boolean) => {
    this.setState({ is_optional: checked ? 1 : 0 });
  };

  onChangeWeight = (value) => {
    const val = value.substring(4, value.indexOf(" 名称"));
    this.setState({ item_dic_id: val });
    const name = value.substring(value.indexOf(" 名称") + 5, value.length);
    this.setState({ award_name: name });
  };

  getGiftTypeStr = () => {
    let string = ``;
    if (this.state.sex == 0) {
      string = `女`;
    } else if (this.state.sex == 1) {
      string = `男`;
    }
    return string;
  };

  onChangeName = ({ target: { value } }) => {
    this.setState({ weight: value });
  };

  onChangeNum = (value) => {
    this.setState({ num: value });
  };

  onChangemax_times = (value) => {
    this.setState({ max_times: value });
  };

  onChangeCondition = (value) => {
    this.setState({ headge_num: value });
  };

  onChangeDescribe = (value) => {
    this.setState({ guarante_num: value });
  };

  onChangedesc = ({ target: { value } }) => {
    this.setState({ desc: value });
  };

  onChangeSort = (value) => {
    this.setState({ target_num: value });
  };

  onChangeSex = (value) => {
    this.setState({ sex: value });
  };

  onChangeSource = (value) => {
    this.setState({ source: value });
  };

  onChangeLevel = (value) => {
    this.setState({ level: value });
  };

  onChangeType = (value) => {
    this.setState({ type: value });
  };

  onChangehot = (checked: boolean) => {
    this.setState({ is_default: checked ? 1 : 0 });
  };

  onChangedelsign = (checked: boolean) => {
    this.setState({ delsign: checked ? 0 : 1 });
  };

  checkNullStr = (value) => {
    return value || value == 0 ? value : "";
  };

  handleRequest = (isCreating: boolean) => {
    const { dataModelType, current } = this.props;

    const request: any = {
      activity_id: this.state.activity_id,
      group_index: this.state.group_index,
      award_group_id: this.state.award_group_id,
      group_name: this.state.group_name,

      weight: this.state.weight,
      target_num: this.state.target_num,
      is_limit: this.state.is_limit,
      num: this.state.num,
      award_name: this.state.award_name,
      max_times: this.state.max_times,
      desc: this.state.desc,
      is_optional: this.state.is_optional,
      item_dic_id: this.state.item_dic_id,
      headge_num: this.state.headge_num,
      guarante_num: this.state.guarante_num,
    };

    if (dataModelType == 2) {
      request.award_id = this.state.award_id
    }
    return request;
  };

  handleSubmitCreate = () => {
    const { dispatch } = this.props;
    const request = this.handleRequest(true);
    console.log("request", request);
    const flag = this.checkRequest(request, true);
    if (flag) {
      dispatch({
        type: 'activityAward/insertAward',
        payload: request
      }).then(() => {
        this.onCloseModel();
      });
    }
  };

  checkRequest = (request: any, isCreating: boolean) => {

    if (!request.item_dic_id || request.item_dic_id == "" || request.item_dic_id <= 0) {
      message.error("请选择道具");
      return false;
    }

    if (!request.weight || request.weight == "" || request.weight < 0) {
      message.error("请填写权重");
      return false;
    }

    if (!request.num || request.num == "" || request.num <= 0) {
      message.error("请填写数量");
      return false;
    }

    if (!request.max_times || request.max_times == "" || request.max_times < 0) {
      message.error("请填写最多获得次数");
      return false;
    }

    return true;
  }

  //关闭模态页
  onCloseModel = () => {
    this.props.onClickClose();
  };

  onClickOk = () => {
    const { dataModelType } = this.props;
    this.handleSubmitCreate();
  };
}

export default AwardEditModal;
