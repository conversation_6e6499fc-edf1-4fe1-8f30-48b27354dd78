/*
 * @Description: 称号管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-11-19 17:13:50
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  Popover,
  Row,
  Col,
  Radio,
  Popconfirm,
  Table,
  Tag,
  Typography,
} from 'antd';
const { Text } = Typography;
import { PlusOutlined, CloudUploadOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import * as styles from './MerchantMap.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import AppList from '../../../pages/AppList';
import { ColumnProps } from 'antd/lib/table';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName, getSelectName } from '@/utils/mallTool';
import errorImgRectangle from '../../../assets/errorImgRectangle.png';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import Center from '@/pages/Account/Center/Applications';
export const PAGESIZE = 2;

declare const window: any;

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  currentMerchantScriptList: scriptkill.currentMerchantScriptList,
}))

export default class MerchantMap extends Component<any, any> {
  constructor(props) {
    super(props);
  }

  MP(ak) {
    return new Promise(function (resolve, reject) {
      let script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = `http://api.map.baidu.com/api?v=2.0&ak=${ak}&callback=init`;
      document.head.appendChild(script)
      window.init = () => {
        resolve(window.BMap)
      }
    })
  }

  componentDidMount() {
    this.MP("BNnGNp3bM1LQ0gtuSybe3pz0gG2VK2Be").then(BMap => {
      let map = new BMap.Map('allmap');            // 创建Map实例
      let point = new BMap.Point(this.props.longitude, this.props.latitude); // 创建点坐标
      map.centerAndZoom(point, 15);
      let marker = new BMap.Marker(new BMap.Point(this.props.longitude, this.props.latitude));
      map.addOverlay(marker);
      map.enableScrollWheelZoom();  
    });
  }

  render() {
    //添加地图类型控件
    return (
      <div className={styles.mapDiv}>
        <div id='allmap' style={{  width: 500, height: 500 }}></div>
      </div>
    )
  }
}
