/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-01-16 15:37:22
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-03-06 09:01:18
 */
import appWolf from './router.app.wolf';
import appLiao from './router.app.liao';
import appDev from './router.app.dev';
import appScrpit from './router.app.scrpit';
import appMst from './router.app.mst';
import appLease from './router.app.lease';
import { AccessRouteId } from './accessRouteCof';
import appExchange from './router.app.exchange';

export default [
  // user
  {
    path: '/user',
    component: '../layouts/UserLayout',
    routes: [
      {
        path: '/user',
        redirect: '/user/login',
      },
      {
        path: '/user/login',
        component: './User/Login',
      },
    ],
  },
  // basic
  {
    path: '/',
    component: '../layouts/BasicLayout',
    routes: [
      // 主页
      {
        path: '/',
        redirect: 'appList',
      },
      // app列表
      {
        name: 'appList',
        icon: 'appstore',
        path: '/appList',
        component: 'AppList',
      },
      // 狼人杀
      appWolf,
      // AI生图
      appMst,
      // 显卡租赁
      appLease,
      //剧本杀
      appScrpit,
      //交易系统
      appExchange,
      // 了了
      appLiao,
      // 开发
      appDev,

      // 系统管理页
      {
        name: 'systemManage',
        icon: 'setting',
        path: '/systemManage',
        authority: AccessRouteId.admin_edit_route,
        routes: [
          // 人员管理
          // {
          //   path: '/systemManage/member',
          //   icon: 'team',
          //   name: 'member',
          //   authority: AccessRouteId.admin_edit_route,
          //   component: './SystemManage/MemberManage',
          // },
          // 人员管理
          {
            path: '/systemManage/UserAccessManage',
            icon: 'team',
            name: 'member',
            authority: AccessRouteId.admin_edit_route,
            component: './SystemManage/UserAccessManage',
          },
        ],
      },
      // account个人页
      {
        name: 'account',
        icon: 'user',
        path: '/account',
        routes: [
          {
            path: '/account/center',
            name: 'center',
            component: './Account/Center/Center',
          },
        ],
      },
      // 404错误
      {
        component: '404',
      },
    ],
  },
];
