/*
 * @Description: 称号-编辑模态页
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-10-12 16:56:14
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-07-22 11:55:11
 */

import React, { Component } from 'react';
import { connect } from 'dva';
import { Modal, DatePicker, Input, Select, message, InputNumber, Button, TimePicker, Form, Switch } from 'antd';
const { RangePicker } = DatePicker;
import * as styles from './BroadcastEditModal.less';
import { getCateName, getOptionList, getSelectName } from '@/utils/mallTool';
import { getTimeStr, getTimeStrToMoment, getNowStr } from '@/utils/momentTool';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Field } from '@/components/Charts';
import moment from 'moment';
const Option = Select.Option;

@connect(({ loading, login, broadcastSend, bannerStore, global }: { loading: IdvaLoading; login: ILogin; broadcastSend: any; bannerStore: any; global: any }) => ({
  uid: login.uid,
  isLoading: loading.models['broadcastSend'],
  broadcastList: broadcastSend.broadcastList,
  typeList: bannerStore.typeList
}))

class BroadcastEditModal extends React.Component<any, any> {
  private formRef: RefObject<Form> = React.createRef();
  constructor(props) {
    super(props);
    const { dataModelType, current } = props;
    if (dataModelType == 1) {
      this.state = {
        ...current, send_time: getTimeStrToMoment(current.send_time)
      }
    } else {
      this.state = {
        acTendUrl: null,
        actDesc1: null,
        actDesc2: null,
        actDesc3: null,
        actDesc4: null,
        actItemName: null,
        actUserId1: null,
        actUserId2: null,
        type: 0,
        tend_type: 0,
        tend_page: 0,
      }
    }
  }

  // eslint-disable-next-line react/sort-comp
  render() {
    const { dataModelType, current, isLoading } = this.props;
    return (
      // eslint-disable-next-line react/jsx-filename-extension
      <Modal
        width={600}
        bodyStyle={{ padding: '5px 10px 5px 10px' }}
        closable={false}
        maskClosable={false}
        confirmLoading={!!isLoading}
        centered={true}
        title={dataModelType == 1 ? "编辑" : "新增"}
        footer={null}
        visible={dataModelType > 0}
      >
        <div>
          {dataModelType > 0 && this.renderEditModal()}
        </div>
      </Modal>
    );
  }

  renderEditModal = () => {
    const { tabKey, awardList, current } = this.props;

    const init = { ...this.state };

    const layout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
    };
    const tailLayout = {
      wrapperCol: { offset: 16, span: 16 },
    };

    if (tabKey == 1) {
      return (
        <div>
          <Form
            {...layout}
            name="lotteryActivity"
            ref={this.formRef}
            initialValues={init}
            onFinish={this.onClickOk}
            onFinishFailed={this.onFinishFailed}
          >
            <Form.Item
              label="活动名称"
              name="activity_name"
              rules={[{ required: true }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="文案"
              name="actDesc1"
              rules={[{ required: true }]}
            >
              <Input />
            </Form.Item>
            <Form.Item name={'tend_type'} label="跳转类型" rules={[{ required: true }]}>
              <Select onChange={this.handleTypeOptionChange}>
                <Option value={0}>全部列表</Option>
                <Option value={1}>app-浏览器</Option>
              </Select>
            </Form.Item>
            {this.state.tend_type == 0 && (
              <Form.Item name="tend_page" label="跳转类型详情" rules={[{ required: true }]}>
                <Select
                  placeholder=""
                  onChange={this.handlePageOptionChange}
                  allowClear={true}
                >
                  {this.props.typeList.map((item, index) => (
                    <Option key={index} value={item.dialog_type}>
                      {item.dialog_type_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )}
            {(this.state.tend_type == 1 || this.state.tend_page == 0 || this.state.tend_page == 13 || this.state.tend_page == 16) && (
              <Form.Item name="acTendUrl" label="网页url" rules={[{ required: true }]}>
                <Input.TextArea />
              </Form.Item>
            )}
            <Form.Item name="send_time" label="发送时间" rules={[{ required: false }]}>
              <DatePicker
                style={{ width: 385 }}
                showTime={true}
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
            <Form.Item {...tailLayout}>
              <Button onClick={this.props.onClickClose}>
                取消
              </Button>
              <Button style={{ marginLeft: 10 }} type="primary" htmlType="submit">
                提交
              </Button>
            </Form.Item>
          </Form>
        </div>
      );
    } else {
      return (
        <div>
          <Form
            {...layout}
            name="lotteryActivity"
            ref={this.formRef}
            initialValues={init}
            onFinish={this.onClickOk}
            onFinishFailed={this.onFinishFailed}
          >
            <Form.Item
              label="描述1"
              name="actDesc1"
              rules={[{ required: false }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="用户ID1"
              name="actUserId1"
              rules={[{ required: false }]}
            >
              <InputNumber style={{ width: 385 }} />
            </Form.Item>
            <Form.Item
              label="描述2"
              name="actDesc2"
              rules={[{ required: false }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="用户ID2"
              name="actUserId2"
              rules={[{ required: false }]}
            >
              <InputNumber style={{ width: 385 }} />
            </Form.Item>
            <Form.Item
              label="描述3"
              name="actDesc3"
              rules={[{ required: false }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="物品名"
              name="actItemName"
              rules={[{ required: false }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="描述4"
              name="actDesc4"
              rules={[{ required: false }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="跳转位置"
              name="acTendUrl"
              rules={[{ required: false }]}
            >
              <Input />
            </Form.Item>
            <Form.Item {...tailLayout}>
              <Button onClick={this.props.onClickClose}>
                取消
              </Button>
              <Button style={{ marginLeft: 10 }} type="primary" htmlType="submit">
                提交
              </Button>
            </Form.Item>
          </Form>
        </div>
      );
    }
  };

  handlePageOptionChange = (e) => {
    this.setState({
      tend_page: e
    });
  };

  handleTypeOptionChange = (e) => {
    console.log(e);
    this.setState({
      tend_type: e
    });
  };

  onFinishFailed = errorInfo => {
    console.log('Failed:', errorInfo);
    // eslint-disable-next-line react/destructuring-assignment
    if (this.props.dataModelType == 1) {
      message.error('修改失败!');
    } else {
      message.error('新增失败!');
    }
  };

  onClickOk = (values) => {
    console.log('Success:', values);
    const { dispatch, current, dataModelType } = this.props;

    if (!values.acTendUrl &&
      !values.actDesc1 &&
      !values.actDesc2 &&
      !values.actDesc3 &&
      !values.actDesc4 &&
      !values.actItemName &&
      !values.actUserId1 &&
      !values.actUserId2 &&
      this.props.tabKey == 2) {
      message.error("请至少填写一个");
      return;
    }

    const req = {
      acTendUrl: values.acTendUrl ? values.acTendUrl : "",
      actDesc1: values.actDesc1 ? values.actDesc1 : "",
      actDesc2: values.actDesc2 ? values.actDesc2 : "",
      actDesc3: values.actDesc3 ? values.actDesc3 : "",
      actDesc4: values.actDesc4 ? values.actDesc4 : "",
      actItemName: values.actItemName ? values.actItemName : "",
      actUserId1: values.actUserId1 || values.actUserId1 == 0 ? values.actUserId1 : "",
      actUserId2: values.actUserId2 || values.actUserId2 == 0 ? values.actUserId2 : "",
      tend_type: values.tend_type || values.tend_type == 0 ? values.tend_type : "",
      tend_page: values.tend_page || values.tend_page == 0 ? values.tend_page : "",
      send_time: values.send_time ? getTimeStr(values.send_time) : "",
      activity_name: values.activity_name ? values.activity_name : "",
      is_activity: this.props.tabKey == 1 ? 1 : 0,
      id: this.state.id,
      admin_id: this.props.uid,
    }
    console.log("req", req);

    if (dataModelType == 1) {
      dispatch({
        type: 'broadcastSend/updateBroadcast',
        payload: req
      }).then(() => {
        this.props.onClickClose();
      });
    } else {
      dispatch({
        type: 'broadcastSend/insertBroadcast',
        payload: req
      }).then(() => {
        this.props.onClickClose();
      });
    }
  };
}

export default BroadcastEditModal;
