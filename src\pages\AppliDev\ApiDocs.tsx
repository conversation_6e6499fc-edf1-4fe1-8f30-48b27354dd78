import React from 'react';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { Card, Table } from 'antd';
import { IapiDocItem } from '../../dto/werewolf';
import { Iglobal } from '@/models/global';
import { connect } from 'dva';
import { ColumnProps } from 'antd/lib/table';

export interface IApiDocsProps {
	list: IapiDocItem[];
}

@connect(({ global, apiDoc }: { global: Iglobal; apiDoc: IapiDocItem[] }) => ({
	list: apiDoc
}))
export default class ApiDocs extends React.Component<IApiDocsProps, any> {
	constructor(props) {
		super(props);
		props.dispatch({ type: 'apiDoc/fetchList' });
	}

	private columns: ColumnProps<IapiDocItem>[] = [
		{
			title: '名称',
			dataIndex: 'doc_name'
		},

		{
			title: '点击跳转',
			dataIndex: 'doc_url',
			render: value => (
				<a href={value} target="_blank">
					地址
				</a>
			)
		},
		{
			title: '描述',
			dataIndex: 'doc_desc'
		}
	];

	render() {
		const { list } = this.props;
		return (
			<PageHeaderWrapper title="接口文档列表">
				<Card>
					<Table
						// title={() => '接口文档列表'}
						columns={this.columns}
						dataSource={list}
						// bordered
						rowKey={(record, index) => index.toString()}
						//pagination={null}
					/>
				</Card>
			</PageHeaderWrapper>
		);
	}
}
