// 列表

export interface IrecordListRes {
    length: number;
    id: number,
    game_config_id: number,
    record_date: string,
    gname: string
}
export interface IrecordList {
    id: number,
    boardInfo: IrecordListInfo
}
export interface IrecordListInfo {
    id: number,
    record_date: string,
    gname: string
}
export interface IupdateRecord {
    id1: number
    id2: number
    gameBoardId1: Number
    gameBoardId2: Number
}