/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-02-23 13:48:13
 * @LastEditTime: 2022-03-07 16:38:29
 * @LastEditors: jiawen.wang
 */
export interface IAwardPoolList {
  id: number;
  name: string;
  remark: string;
}

export interface IAwardPoolReq {
  award_sort: number;
  award_weight: number;
  delsign: number;
  mining_frame_id: number;
  mining_frame_level: number;
  mining_frame_probability: number;
  pool_id: number;
  pool_name: string;
  pool_sort: number;
}
export interface IAddAwardPoolReq {
  name: string;
  remark: string;
}

export interface IAwardListReq {
  item_dic_id: number;
  item_cate_name: string;
  item_cate_id: number,
  item_dic_pic: string;
  name: string;
  id: number;
  remark: string;
  level: number;
}

export interface IUpdateStoneListItemReq {
  name: string;
  pic: string;
  delsign: number;
  id: number;
}

export interface IStoneListItem {
  id: number;
  name: string;
  pic: string; //banner
  delsign: number; //0上架 1下架
}

export interface IAddStonePoolReq {
  mininig_frame_id: number;
  pool_id: number;
  level: number; // 框石等级
  probability: number; //奖池掉率
  sort: number; //排序
  delsign: number; //0:上架 1:下架
  remark: string; //奖池描述
}

export interface IUpdateStonePoolReq {
  relation_id: number;
  mininig_frame_id: number; //框石ID
  pool_id: number; // 奖池ID
  level: number; //框石等级
  probability: number; //奖池掉率
  sort: number; //排序
  delsign: number; //0:上架 1:下架
  remark: string; //奖池描述
}

export interface IStonePoolListItem {
  mining_frame_id: number;
  level: number;
  pool_id: number;
  name: string;
  sort: number;
  probability: number;
  delsign: number;
}

export interface IStonePoolListReq {
  mining_frame_id: number;
}


export interface IStoneDropRateItem {
  id: number;
  type: number;
  weight: number;
  num: number;
  mining_frame_id: number;
  coefficient: number;
  ca_start: number;
  ca_end: number;
  pa_start: number;
  pa_end: number;
  delsign: number;
  name: string;

  _ca_desc: string; //本地添加显示属性
}

export interface IStoneDropRateTableItem {
  id: number;
  ca_start: number;
  ca_end: number;
  _ca_desc:string;
  content: IStoneDropRateItem[];
  key: string;
  totalWeight: number;
}

export interface IAddStoneDropRateRequestItem{
  weight:number;
  mining_frame_id:number;
}
export interface IAddStoneDropRateRequest {
  ca_start: number;
  ca_end: number;
  content:IAddStoneDropRateRequestItem[];
}

export interface IDeleteStoneDropRateRequest {
  ca_start: number;
  ca_end: number;
  content: { mining_frame_id: number }[];
}

export interface IEditStoneDropRateRequest {
  ca_start: number;
  ca_end: number;
  ca_start_modif: number;
  ca_end_modif: number;
  dropList:IAddStoneDropRateRequestItem[];
}
