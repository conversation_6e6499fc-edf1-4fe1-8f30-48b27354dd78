import React, { Component, Fragment } from 'react';
import Result from '@/components/Result';
import * as styles from './NewBanner.less';
import { connect } from 'dva';
import { ILogin } from '@/models/login';
import { Icon as LegacyIcon, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Upload, Button, Modal, Steps, Input, Select, Card, Form, DatePicker } from 'antd';
import { UploadBannerStep, INewBannerStore, IbannerBaseInfo } from '@/dto/wfBanner';
import { FormInstance } from 'antd/lib/form';
import { UploadPath } from '@/dto/staticEnum';
import AppList from '../../../pages/AppList';

const Step = Steps.Step;
const Option = Select.Option;
const { Meta } = Card;
export interface UploadModalProps {
  uid?: number,
  dispatch?: Function,
  isNewBannerModal: boolean,
  onClose: () => void,
  step?: UploadBannerStep,
  isBaseing?: Boolean,
  isCompleteing?: Boolean,
  imageName?: string,
  banner_id?: any,
  typeList?: any[],
  isWebPageShow: boolean
}
const selectBefore = (
  <Select defaultValue="http://" className="select-before">
    <Option value="https://">http://</Option>
    <Option value="http://">https://</Option>
  </Select>
);

export interface UploadModalState {
  isUploading: Boolean,
  imageUrl: string,
  baseInfo: IbannerBaseInfo,
  selectType: number,
  selectPage: number
}
@connect(({ loading, login, bannerStore }: { loading: IdvaLoading, login: ILogin, bannerStore: INewBannerStore }) => ({
  uid: login.uid,
  imageName: bannerStore.imageName,
  banner_id: bannerStore.banner_id,
  typeList: bannerStore.typeList,
  isBaseing: loading.effects['bannerStore/uploadNewBannerBaseInfo'],
  isCompleteing: loading.effects['bannerStore/fetchFrameComplete'],
  step: bannerStore.uploadStep
}))
class NewBannerModal extends Component<UploadModalProps, UploadModalState> {
  formRef = React.createRef<FormInstance>();
  constructor(props) {
    super(props);
    this.state = {
      isUploading: false,
      imageUrl: '',
      baseInfo: null,
      selectType: 0,

    };
  }



  render() {
    const { isNewBannerModal, step } = this.props;
    const title = step === UploadBannerStep.Upload ? '继续编辑' : '新建banner';
    return (
      <Modal visible={isNewBannerModal} onCancel={this.handleCloseModal} title={title} footer={null} width={1200} centered={true}>
        {this.renderUploadSteps()}
        {step == UploadBannerStep.Base && this.renderStepDefault()}
      </Modal>
    );
  }

  // dfs
  handleCloseModal = () => {

    this.props.dispatch({
      type: 'bannerStore/setUploadStep',
      payload: UploadBannerStep.Base
    });
    // this.formRef.current.resetFields();

    this.props.onClose();
  };

  /**
   * 步骤条
   */
  renderUploadSteps() {
    return (
      <Steps current={this.props.step}>
        <Step title="基础信息" description="填写banner基础信息" />

      </Steps>
    );
  }
  /**
   * Step1 填写banner信息表单
   */
  renderStepDefault() {
    const layout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 18 }
    };
    const validateMessages = {
      required: '请填写${label}'
    };
    const onFinish = (values) => {
      const value = {
        sort_id: Number(values.sort_id),
        banner_name: values.banner_name,
        url: values.href_url,
        type: values.type,
        page: values.page,
        start_time: values.time[0].format('YYYY-MM-DD HH:mm:ss'),
        end_time: values.time[1].format('YYYY-MM-DD HH:mm:ss')
      };
      this.props.dispatch({
        type: 'mainPageBanner/insertMainPageBanner',
        payload: value
      }).then(this.handleCloseModal);
    };
    const { RangePicker } = DatePicker;
    const { typeList } = this.props;
    const { selectType, selectPage } = this.state;
    return (
      <div className={styles.step_div}>
        <Form
          {...layout}
          className={styles.stepForm}
          onFinish={onFinish}
          validateMessages={validateMessages}
          initialValues={{
            type: 0,
            page: 0
          }}
          ref={this.formRef}
        >
          <Form.Item name={'sort_id'} label="序号(不可重复)" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name={'banner_name'} label="banner名称" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name={'type'} label="跳转类型" rules={[{ required: true }]}>
            <Select onChange={this.handleTypeOptionChange}>
              <Option value={1}>店铺</Option>
              <Option value={2}>剧本</Option>
              <Option value={3}>图片URL跳转</Option>
              <Option value={4}>文章</Option>
              <Option value={5}>订单</Option>
            </Select>
          </Form.Item>
          {(selectType == 3) && (
            <Form.Item name="href_url" label="网页url" rules={[{ required: false }]}>
              {/*<Input.TextArea />*/}
              <div style={{ marginBottom: 16 }}>
                <Input addonBefore={selectBefore} />
              </div>
            </Form.Item>
          )}
          {(selectType == 1 || selectType == 2 || selectType == 4 || selectType == 5) && (
            <Form.Item name="href_url" label="跳转id" rules={[{ required: false }]}>
              <div style={{ marginBottom: 16 }}>
                <Input placeholder="Basic usage" />
              </div>
            </Form.Item>
          )}
          <Form.Item name="time" label="起始时间" rules={[{ required: true }]}>
            <RangePicker showTime={true} format="YYYY-MM-DD HH:mm:ss" />
          </Form.Item>

          <div className={styles.submit_btn}>
            <Button type="primary" htmlType="submit" loading={!!this.props.isBaseing}>
                确认
            </Button>
          </div>
        </Form>
      </div>
    );
  }

  //下拉框选择
  handleTypeOptionChange = (e) => {
    console.log(e);
    this.setState({
      selectType: e
    });
  };

  handlePageOptionChange = (e) => {
    console.log(e);
    this.setState({
      selectPage: e
    });
  };

  /**
   * 点击了第一步提交基础信息的下一步
   */
  handleSubmitStepDefault = (e) => { };

  /**
   * Step2 上传头像框所需图片
   */
  renderStepUpload = () => {
    console.log('上传头像框所需图片---------------------------')
    return (
      <div className={styles.step_div}>
        {this.renderUploadModel()}
        <div className={styles.submit_btn}>
          <Button type="primary" onClick={this.handleSubmitStepUpload} loading={!!this.props.isCompleteing}>
            下一步
          </Button>
        </div>
      </div>
    );
  };

  /**
   * 上传模块
   */
  renderUploadModel = () => {
    const { imageName } = this.props;
    const uploadUrl = '/megaupload';
    const { imageUrl, isUploading } = this.state;
    const uploadButton = (
      <div>
        <Icon type={isUploading ? 'loading' : 'plus'} />
        <div className="ant-upload-text">Upload</div>
      </div>
    );
    return (
      <div>
        <Upload
          action={uploadUrl}
          headers={{ 'Access-Control-Allow-Origin': '*', 'X-Requested-With': null }}
          listType="picture-card"
          showUploadList={false}
          className={styles.uploadDiv}
          data={{
            type: UploadPath.ossH,
            name: imageName
          }}
          onChange={this.handleChange}
        >
          {imageUrl ? <img src={imageUrl} style={{ width: '100%' }} /> : uploadButton}
        </Upload>
        <div className={styles.uploadDiv}>{imageName}.png</div>
      </div>
    );
  };

  handleChange = (info) => {
    console.log(info)

    if (info.file.status === 'uploading') {
      this.setState({ isUploading: true });
      return;
    }
    if (info.file.status === 'done') {
      this.getBase64(info.file.originFileObj, (imageUrl) => {
        console.log('imageUrl=>', imageUrl);
        this.setState({
          imageUrl,
          isUploading: false
        });
      });
    }
  };

  getBase64(img, callback) {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result));
    reader.readAsDataURL(img);
  }

  handleSubmitStepUpload = () => {
    this.props.dispatch({
      type: 'bannerStore/fetchFrameComplete',
      payload: {
        id: this.props.banner_id,
        banner_img: `${AppList.imageOssH}${this.props.imageName}.png`
      }
    });
  };

  /**
   * Step3 上传成功返回数据 界面
   */
  renderStepSuccess() {
    const { baseInfo } = this.state;
    const { imageName } = this.props;
    const banner_img = `${AppList.imageOssH}${imageName}.png`;

    const information = (
      <div className={styles.information_div}>
        <Card className={styles.card_div} hoverable={true}>
          <Meta avatar={<img className={styles.meta_img} src={banner_img} />} title={baseInfo.banner_name} />
        </Card>
      </div>
    );

    const actions = (
      <Fragment>
        <Button type="primary" onClick={this.handleCloseModal}>
          完成
        </Button>
      </Fragment>
    );
    return (
      <Result
        type="success"
        title="操作成功"
        description="新增banner成功"
        extra={information}
        actions={actions}
        style={{ margin: '0 auto', maxWidth: 560, padding: '24px 0 8px' }}
      />
    );
  }
}
export default NewBannerModal;
