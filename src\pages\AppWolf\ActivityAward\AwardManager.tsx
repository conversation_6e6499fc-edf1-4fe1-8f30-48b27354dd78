import dva from 'dva';
import { Component, default as React } from 'react';
// tslint:disable-next-line:no-duplicate-imports
import { connect } from 'dva';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { Form, Input, Button, Menu, Select, Tabs, Card, Spin, Table, Image, Modal } from 'antd';
import { IactivityAward } from './models/activityAward';
import { PlusOutlined } from '@ant-design/icons';
import { ColumnProps } from 'antd/lib/table';
import * as styles from './AwardManager.less';
import { getItemImg } from '@/utils/assetsUtils';
import AwardGroupEditModal from "./AwardGroupEditModal";
import AwardEditModal from "./AwardEditModal";
import {
	IactivityAwardConf,
	IgetAwardGroupReq,
	IactivityIndex,
	IgetAwardConfReq,
	IactivityAwardItem,
	IactivityAwardGroup
} from '@/dto/wfActivityAward';
import { getSubItemList } from '@/services/apiAtyAward';
import { ModalType } from '@/const/ui_enum';
const Option = Select.Option;
const { TabPane } = Tabs;

export interface IawardManagerProps {
	dispatch: Function;
	isLoading: boolean;
	activityAward: IactivityAward;
}

@connect(({ activityAward, loading }: { activityAward: IactivityAward; loading: IdvaLoading }) => ({
	isLoading: loading.models['activityAward'],
	activityAward
}))
export default class AwardManager extends Component<IawardManagerProps, any> {
	constructor(props) {
		super(props);

		this.state = {
			current: null,
			dataModelType: -1,
			dataAwardModelType: -1,
			isSubItemModalVisible: false,
			subItemImages: [],
			subItemLoading: false
		}

		props.dispatch({
			type: 'activityAward/fetchAtyList'
		}).then(() => {
			console.info('请求结束');
		});

		props.dispatch({
			type: 'mallItemManager/getItemCateList'
		});
		props.dispatch({
			type: 'mallItemManager/getItemDicList',
			payload: { cateId: null }
		});
	}

	columns: ColumnProps<IactivityAwardConf>[] = [
		{
			title: 'award_id',
			dataIndex: 'award_id',
			render: val => <div className={styles.tableValItem}>{val}</div>,
			ellipsis: true,  // 超出宽度显示省略号
		},
		{
			title: '权重',
			dataIndex: 'weight',
			render: val => <div className={styles.tableValItem}>{val}</div>,
			width: 'auto',
		},
		{
			title: '最多领取次数',
			dataIndex: 'max_times',
			render: val => <div className={styles.tableValItem}>{val}</div>,
			width: 'auto',
		},
		{
			title: '是否限制领取次数',
			dataIndex: 'is_limit',
			render: val => <div className={styles.tableValItem}>{val}</div>,
			width: 'auto',
		},
		{
			title: '是否二选一',
			dataIndex: 'is_optional',
			render: val => <div className={styles.tableValItem}>{val}</div>,
			width: 'auto',
		},
		{
			title: '保值',
			dataIndex: 'headge_num',
			render: val => <div className={styles.tableValItem}>{val}</div>,
			width: 'auto',
		},
		{
			title: '保底',
			dataIndex: 'guarante_num',
			render: val => <div className={styles.tableValItem}>{val}</div>,
			width: 'auto',
		},
		{
			title: '完成条件',
			dataIndex: 'target_num',
			render: val => <div className={styles.tableValItem}>{val}</div>,
			width: 'auto',
		},
		{
			title: 'conf_id',
			dataIndex: 'itemContent',
			render: (_, record) => {
				if (!record.itemContent || record.itemContent.length === 0) return null;
				return record.itemContent.map((item, i) => (
					<div key={i} style={{ borderBottom: i < record.itemContent.length - 1 ? '1px solid #f0f0f0' : 'none', padding: '8px 0' }}>
						{item.id}
					</div>
				));
			},
			width: 'auto',
		},
		{
			title: '物品名称',
			dataIndex: 'itemContent',
			render: (_, record) => {
				if (!record.itemContent || record.itemContent.length === 0) return null;

				return record.itemContent.map((item, i) => (
					<div key={i} style={{ display: 'flex', borderBottom: i < record.itemContent.length - 1 ? '1px solid #f0f0f0' : 'none', padding: '8px 0' }}>
						<div>{item.item_name}</div>
						{record.sub_item_dic_id_list ? (
							<Button
								type="link"
								onClick={() => {
									this.fetchSubItemList(record.sub_item_dic_id_list);
									this.setState({ isSubItemModalVisible: true });
								}}
							>
								查看详情
							</Button>
						) : (
							<Image
								src={getItemImg(item.item_id, item.item_cate_id, item.item_table, item.img_name)}
								style={{ height: '70px', width: 'auto', marginLeft: 10, cursor: 'pointer' }}
								preview={{
									mask: '点击查看大图',
									maskClassName: styles.imageMask
								}}
							/>
						)}
					</div>
				));
			},
			ellipsis: true,
		},
		{
			title: '物品数量',
			dataIndex: 'itemContent',
			render: (_, record) => {
				if (!record.itemContent || record.itemContent.length === 0) return null;
				return record.itemContent.map((item, i) => (
					<div key={i} style={{ borderBottom: i < record.itemContent.length - 1 ? '1px solid #f0f0f0' : 'none', padding: '8px 0' }}>
						{item.num}
					</div>
				));
			},
			width: 'auto',
		},
		{
			title: '物品类别名称',
			dataIndex: 'itemContent',
			render: (_, record) => {
				if (!record.itemContent || record.itemContent.length === 0) return null;
				return record.itemContent.map((item, i) => (
					<div key={i} style={{ borderBottom: i < record.itemContent.length - 1 ? '1px solid #f0f0f0' : 'none', padding: '8px 0' }}>
						{item.item_cate_name}
					</div>
				));
			},
			ellipsis: true,
		},
		{
			title: 'item_dic_id',
			dataIndex: 'itemContent',
			render: (_, record) => {
				if (!record.itemContent || record.itemContent.length === 0) return null;
				return record.itemContent.map((item, i) => (
					<div key={i} style={{ borderBottom: i < record.itemContent.length - 1 ? '1px solid #f0f0f0' : 'none', padding: '8px 0' }}>
						{item.item_dic_id}
					</div>
				));
			},
			width: 'auto',
		},
		{
			title: '物品子表id',
			dataIndex: 'itemContent',
			render: (_, record) => {
				if (!record.itemContent || record.itemContent.length === 0) return null;
				return record.itemContent.map((item, i) => (
					<div key={i} style={{ borderBottom: i < record.itemContent.length - 1 ? '1px solid #f0f0f0' : 'none', padding: '8px 0' }}>
						{item.item_id}
					</div>
				));
			},
			width: 'auto',
		},
		{
			title: '物品类别id',
			dataIndex: 'itemContent',
			render: (_, record) => {
				if (!record.itemContent || record.itemContent.length === 0) return null;
				return record.itemContent.map((item, i) => (
					<div key={i} style={{ borderBottom: i < record.itemContent.length - 1 ? '1px solid #f0f0f0' : 'none', padding: '8px 0' }}>
						{item.item_cate_id}
					</div>
				));
			},
			width: 'auto',
		},
		{
			title: '描述',
			dataIndex: 'itemContent',
			render: (_, record) => {
				if (!record.itemContent || record.itemContent.length === 0) return null;
				return record.itemContent.map((item, i) => (
					<div key={i} style={{ borderBottom: i < record.itemContent.length - 1 ? '1px solid #f0f0f0' : 'none', padding: '8px 0' }}>
						{item.desc || '-'}
					</div>
				));
			},
			ellipsis: true,
			width: 'auto',
		},
		{
			title: '操作',
			width: 100,  // 操作列固定宽度
			fixed: 'right', // 固定在右侧
			render: record =>
				<div className={styles.tableValItem}>
					<Button
						size="small"
						type="primary"
						style={{ marginLeft: 8, backgroundColor: '#1890ff' }}
						onClick={() => this.onEditAward(record)}
					>
						编辑奖励
					</Button>

					<Button
						size="small"
						type="primary"
						onClick={() => this.onAddAwardItem(record)}
						icon={<PlusOutlined />}
					>
						新增物品
					</Button>
				</div>
		},
	];


	render() {
		const {
			dataModelType, //
			dataAwardModelType,
			current, // 
			isSubItemModalVisible,
			subItemImages,
			subItemLoading
		} = this.state;
		const { activityAward } = this.props;
		const { selectAtyItem, selectGroupItem } = activityAward;

		// 将新建奖励组按钮移动到 tabBarExtraContent
		const tabBarExtraContent = (
			<Button
				size="small"
				type="primary"
				onClick={this.onCreateAty}
				icon={<PlusOutlined />}
			>
				新建奖励组
			</Button>
		);

		// 构建标题文本
		const titleText = selectAtyItem ? `奖励配置 (活动ID: ${selectAtyItem.id})` : '奖励配置';

		return (
			<PageHeaderWrapper title={titleText} content={this.renderHeader()}>
				{/* <h3>奖励组</h3> */}
				<Spin spinning={!!this.props.isLoading}>
					{dataModelType > 0 &&
						<AwardGroupEditModal
							dataModelType={dataModelType}
							current={current}
							item={selectGroupItem}
							onClickClose={this.onClickClose} />}
					{dataAwardModelType > 0 &&
						<AwardEditModal
							dataAwardModelType={dataAwardModelType}
							current={current}
							onClickClose={this.onClickClose} />}
					{/* 新增子物品详情模态框 */}
					<Modal
						visible={isSubItemModalVisible}
						onCancel={() => this.setState({ isSubItemModalVisible: false })}
						footer={null}
						width={800}
						title={null}
						wrapClassName={styles.transparentModal}
						bodyStyle={{
							padding: 0,
							backgroundColor: 'transparent'
						}}
						style={{
							backgroundColor: 'transparent',
							boxShadow: 'none'
						}}
						maskStyle={{
							backgroundColor: 'rgba(0, 0, 0, 0.65)'
						}}
					>
						<Spin spinning={subItemLoading}>
							<div style={{
								display: 'flex',
								flexWrap: 'wrap',
								gap: '10px',
								justifyContent: 'center',
								backgroundColor: 'transparent'
							}}>
								<div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
									<div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
										{subItemImages.slice(0, 3).map((imgUrl, index) => (
											<Image
												key={index}
												src={imgUrl}
												style={{ height: '250px', width: 'auto' }}
												preview={{
													mask: '点击查看大图',
													maskClassName: styles.imageMask
												}}
											/>
										))}
									</div>
									<div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
										{subItemImages.slice(3, 5).map((imgUrl, index) => (
											<Image
												key={index + 3}
												src={imgUrl}
												style={{ height: '250px', width: 'auto' }}
												preview={{
													mask: '点击查看大图',
													maskClassName: styles.imageMask
												}}
											/>
										))}
									</div>
								</div>
							</div>
						</Spin>
					</Modal>
					{this.renderAwardList(tabBarExtraContent)}
				</Spin>
			</PageHeaderWrapper>
		);
	}

	// 修改 renderAwardList 方法以接收 tabBarExtraContent 参数
	renderAwardList = (tabBarExtraContent) => {
		const { activityAward } = this.props;
		if (activityAward.groupList == null || activityAward.groupList.length === 0) {
			return tabBarExtraContent;
		}
		if (activityAward.selectGroupItem == null) {
			return tabBarExtraContent;
		}

		return (
			<div>
				<Card>
					<Tabs
						defaultActiveKey={activityAward.selectGroupItem.id + ''}
						onChange={this.onChangeGroup}
						tabBarExtraContent={tabBarExtraContent}
					>
						{activityAward.groupList.map((item: IactivityAwardGroup, index) => {
							return (
								<TabPane tab={item.group_name} key={item.id + ''}>
									<Button
										size="small"
										type="primary"
										style={{ marginLeft: 8, backgroundColor: '#1890ff' }}
										onClick={() => this.onEditGroup(item)}
									>
										编辑本组
									</Button>

									<Button
										size="small"
										type="primary"
										danger
										onClick={() => {
											Modal.confirm({
												title: '确认删除',
												content: '确定要删除该奖励组吗？会删除本组的所有奖励配置，删除后不可恢复。',
												okText: '确认',
												cancelText: '取消',
												onOk: () => this.onDeleteGroup(item),
											});
										}}
										style={{ marginLeft: 8 }}
									>
										删除本组
									</Button>
									<Table
										columns={this.columns}
										dataSource={activityAward.confList}
										rowKey={(record, index) => record.award_id || index?.toString() || Math.random().toString()}
										sticky={true}
										pagination={false}
										bordered={true}
										rowClassName={(record, index) => index % 2 === 0 ? styles.evenRow : styles.oddRow}
										scroll={{ x: 'max-content' }}
										footer={() => (
											<div>
												<Button
													type="primary"
													icon={<PlusOutlined />}
													onClick={this.onCreateAward}
												>
													新建奖励
												</Button>
											</div>
										)}
									/>
								</TabPane>
							);
						})}
					</Tabs>
				</Card>
			</div>
		);
	};

	//渲染头部信息
	renderHeader() {
		const { activityAward } = this.props;
		return (
			<div>
				{/* 活动列表 */}
				{activityAward.activityList && activityAward.activityList.length > 0 && this.renderAtySelect()}
			</div>
		);
	}

	//渲染头部活动信息
	renderAtySelect = () => {
		const { activityAward, dispatch } = this.props;
		const { activityList, selectAtyItem } = activityAward;
		return (
			<Spin spinning={!!this.props.isLoading}>
				请选择活动：
				<Select
					defaultValue={selectAtyItem ? "id:" + selectAtyItem.id + "  " + selectAtyItem.name : "id:" + activityList[0].id + "  " + activityList[0].name}
					style={{ width: 260, marginLeft: 10 }}
					onChange={this.onChangeAty}
				>
					{activityList.map((item, index) => (
						<Option key={index} value={index}>
							{item.id + "  " + item.name}
						</Option>
					))}
				</Select>
				<Button
					size="small"
					type="primary"
					// onClick={this.onCreateAty}
					icon={<PlusOutlined />}
					style={{ marginLeft: 10 }}
				>
					新建活动
				</Button>

			</Spin>
		);
	};

	//更换活动
	onChangeAty = index => {
		const { activityAward, dispatch } = this.props;
		const atyItem: IactivityIndex = activityAward.activityList[index];
		dispatch({ type: 'activityAward/setSelectAtyId', payload: atyItem });

		const req: IgetAwardGroupReq = {
			activity_id: atyItem.id
		};
		dispatch({ type: 'activityAward/fetchAwardGroupList', payload: req });
	};

	//新建分组
	onCreateAty = () => {
		const { activityAward, dispatch } = this.props;
		const { activityList, selectAtyItem } = activityAward;
		this.setState({ dataModelType: ModalType.CREATE, current: selectAtyItem });
	};
	// 编辑奖励组
	onEditGroup = (item: IactivityAwardGroup) => {
		const { activityAward, dispatch } = this.props;
		const { selectGroupItem, selectAtyItem } = activityAward;
		this.setState({ dataModelType: ModalType.EDIT, current: selectAtyItem });
	};
	// 删除奖励组
	onDeleteGroup = (item: IactivityAwardGroup) => {
		const { activityAward, dispatch } = this.props;
		const { selectGroupItem, selectAtyItem } = activityAward;
		const req = {
			activity_id: selectAtyItem.id,
			award_group_id: selectGroupItem.id
		};
		dispatch({ type: 'activityAward/fetchDeleteAwardGroup', payload: req });
	};

	// 新建奖励
	onCreateAward = () => {
		const { activityAward, dispatch } = this.props;
		const { selectGroupItem, selectAtyItem } = activityAward;
		const current = {
			activity_id: selectAtyItem.id,
			group_index: selectGroupItem.group_index,
			award_group_id: selectGroupItem.id,
			group_name: selectGroupItem.group_name
		}
		this.setState({ dataAwardModelType: ModalType.CREATE, current });
	};
	// 编辑奖励
	onEditAward = (item: IactivityAwardConf) => {

	}
	// 新增奖励物品
	onAddAwardItem = (record) => {
		console.info('record', record);
		const { activityAward, dispatch } = this.props;
		const { selectGroupItem, selectAtyItem } = activityAward;
		const current = {
			activity_id: record.activity_id,
			award_group_id: record.award_group_id,
			award_id: record.award_id,
			group_name: selectGroupItem.group_name,
			weight: record.weight,
			target_num: record.target_num,
			is_limit: record.is_limit,
			max_times: record.max_times,
			desc: record.desc,
			is_optional: record.is_optional,
			headge_num: record.headge_num,
			guarante_num: record.guarante_num,
		}
		this.setState({ dataAwardModelType: ModalType.ADD_ITEM, current });
	};

	//更换奖励组
	onChangeGroup = (key: string) => {
		const { activityAward, dispatch } = this.props;
		for (const item of activityAward.groupList) {
			if (item.id + '' == key) {
				dispatch({ type: 'activityAward/setSelectGroup', payload: item });
				const req: IgetAwardConfReq = {
					activity_id: activityAward.selectAtyItem.id,
					award_group_id: item.id
				};
				dispatch({ type: 'activityAward/fetchAwardConfList', payload: req });
			}
		}
	};

	// 关闭模态页
	onClickClose = () => {
		this.setState({ dataModelType: -1, dataAwardModelType: -1, current: null });
	};

	// 修改查询子物品列表的方法
	fetchSubItemList = async (subItemIds: string) => {
		this.setState({ subItemLoading: true });
		try {
			const response = await getSubItemList({ subItemIds });
			if (response) {
				const images = response.map(item =>
					getItemImg(item.item_id, item.item_cate_id, item.item_table, item.img_name)
				);
				this.setState({ subItemImages: images });
			}
		} finally {
			this.setState({ subItemLoading: false });
		}
	};
}
