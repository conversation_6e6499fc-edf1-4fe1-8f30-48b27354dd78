import { IsetModelChangeTagParams, IsetModelSaveParams, IsetModelSearchParams,IsetModelTagParams } from '../dto/setModelDto';
import request from '@/utils/request';

import { API_VERSION } from './api';

export async function searchModelInfo(params: IsetModelSearchParams) {

    return request(`${API_VERSION}/werewolf/setModel/searchModelInfo`, {
        method: `POST`,
        body: params,
    });
}

export async function searchFromModelList(params: IsetModelSearchParams) {
    return request(`${API_VERSION}/werewolf/setModel/searchFromModelList`, {
        method: `POST`,
        body: params,
    });
}

export async function delPic(params: IsetModelSearchParams) {

    return request(`${API_VERSION}/werewolf/setModel/delPic`, {
        method: `POST`,
        body: params,
    });    
}

export async function addPic(params: IsetModelSearchParams) {

    return request(`${API_VERSION}/werewolf/setModel/addPic`, {
        method: `POST`,
        body: params,
    });
}

export async function modelTypeList() {

    return request(`${API_VERSION}/werewolf/setModel/modelTypeList`, {
        method: `POST`,
        body: {},
    });
}

export async function classificationTypeList(params: IsetModelTagParams) {

    return request(`${API_VERSION}/werewolf/setModel/classificationTypeList`, {
        method: `POST`,
        body: params
    });
}

export async function classificationTypeMyList(params: IsetModelTagParams) {

    return request(`${API_VERSION}/werewolf/setModel/classificationTypeList`, {
        method: `POST`,
        body: params
    });
}

export async function addClassificationType(params: IsetModelChangeTagParams) {

    return request(`${API_VERSION}/werewolf/setModel/addClassificationType`, {
        method: `POST`,
        body: params
    });
}

export async function delClassificationType(params: IsetModelChangeTagParams) {

    return request(`${API_VERSION}/werewolf/setModel/delClassificationType`, {
        method: `POST`,
        body: params
    });
}

export async function baseModelList() {

    return request(`${API_VERSION}/werewolf/setModel/baseModelList`, {
        method: `POST`,
        body: {},
    });
}

export async function saveModel(params: IsetModelSaveParams) {

    return request(`${API_VERSION}/werewolf/setModel/saveModel`, {
        method: `POST`,
        body: params,
    });
}
