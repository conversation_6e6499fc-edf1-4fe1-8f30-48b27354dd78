/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-12-29 16:45:38
 */
// https://umijs.org/config/
import os from 'os';
import pageRoutes from './router.config';
import webpackPlugin from './plugin.config';
import defaultSettings from '../src/defaultSettings';

const plugins = [
  [
    'umi-plugin-react',
    {
      antd: true,
      dva: {
        hmr: true,
      },
      targets: {
        ie: 11,
      },

      locale: {
        enable: true, // default false
        default: 'zh-CN', // default zh-CN
        baseNavigator: true, // default true, when it is true, will use `navigator.language` overwrite default
      },
      // 按需加载
      // dynamicImport: {
      //   webpackChunkName: true,
      //   loadingComponent: './components/PageLoading/index',
      //   level:1
      // },
      ...(!process.env.TEST && os.platform() === 'darwin'
        ? {
            dll: {
              include: ['dva', 'dva/router', 'dva/saga', 'dva/fetch', 'lodash', 'moment'],
              exclude: ['@babel/runtime'],
            },
            hardSource: false,
          }
        : {}),
    },
    '@babel/plugin-transform-private-methods',
  ],
];

// judge add ga
if (process.env.APP_TYPE === 'site') {
  plugins.push([
    'umi-plugin-ga',
    {
      code: '***********-6',
    },
  ]);
}

const config = {
  // add for transfer to umi
  plugins,
  history: 'hash', // 解决打包刷新后刷新路由问题
  targets: {
    ie: 11,
  },
  define: {
    APP_TYPE: process.env.APP_TYPE || '',
  },
  // 路由配置
  routes: pageRoutes,
  // Theme for antd
  // https://ant.design/docs/react/customize-theme-cn
  theme: {
    'primary-color': defaultSettings.primaryColor,
  },
  externals: {
    '@antv/data-set': 'DataSet',
  },
  proxy: {
    '/room1': {
      target: 'http://*************:9926/',
      changeOrigin: true,
      pathRewrite: { '^/room1': '' },
    },
    '/room2': {
      target: 'http://*************:9919/',
      changeOrigin: true,
      pathRewrite: { '^/room2': '' },
    },
    '/api/': {
      // target: 'http://**************:4632/',
      target: 'http://localhost:7001/',
      // target: 'http://*************:7002',
      // target: 'http://127.0.0.1:7003/',
      // target: 'http://*************:4632/',
      // target: 'http://**************:4632/',
      changeOrigin: true,
      pathRewrite: { '^/': '' },
    },
    '/wfreplay': {
      target: 'http://**************:4632/',
      changeOrigin: true,
    },
    // '/headicon/': {
    //   target: 'http://coder.53site.com/',
    //   // target: 'http://127.0.0.1:7003/',
    //   // target: 'http://**************/',
    //   changeOrigin: true,
    //   pathRewrite: { '^/': '' },
    // },
    '/megaupload': {
      // target:"https://werewolf.53site.com/Werewolf/CustomerService/data/uploadImgTest.php",
      // target: 'http://127.0.0.1:81/megaupload',
      // target: 'http://**************/megaupload',
      target: 'http://**************:4632/megaupload',
      changeOrigin: true,
    },
    '/megauploadOssCN': {
      target: 'http://**************:4632/megauploadOssCN',
      changeOrigin: true,
    },
    '/Werewolf': {
      target: 'http://coder.53site.com',
      // target: 'http://werewolf.53site.com',

      // target: 'http://*************:4632/getServer',
      changeOrigin: true,
    },
    '/simulator': {
      // target: 'http://127.0.0.1:9969/',
      target: 'http://121.196.28.144:9969/',
      changeOrigin: true,
    },
    '/getFrameImg': {
      target: 'http://**************:4632/getFrameImg',
      changeOrigin: true,
    },
    '/checkFrameImgComplete': {
      target: 'http://**************:4632/checkFrameImgComplete',
      changeOrigin: true,
    },
    '/copyAvatarFrame': {
      target: 'http://**************:4632/copyAvatarFrame',
      // target: 'http://coder.53site.com/Werewolf/frame/copyAvatarFrame.php',
      changeOrigin: true,
    },
    '/wechat': {
      target: 'http://coder.53site.com',
      changeOrigin: false,
    },
    '/WerewolfExchange/': {
      target: 'http://**************:4632/',
      changeOrigin: true,
    },
    // '/board/': {
    //   target: 'http://**************:9926/',
    //   changeOrigin: true,
    // },
    '/WerewolfExchangeCredit/': {
      target: 'http://**************:4632/',
      changeOrigin: true,
    },
    '/exchangeSys/': {
      // target: 'http://localhost:8081/',
      target: 'http://**************:4632/',
      changeOrigin: true,
      // pathRewrite: { '^/exchangeSys': '' },
    },
    '/skExchange/': {
      target: 'http://**************:4632/',
      // target: 'http://localhost:8088/',
      changeOrigin: true,
      // pathRewrite: { '^/exchangeSys': '' },
    },
  },
  ignoreMomentLocale: true,
  lessLoaderOptions: {
    javascriptEnabled: true,
  },
  disableRedirectHoist: true,
  cssLoaderOptions: {
    modules: true,
    getLocalIdent: (context, localIdentName, localName) => {
      if (
        context.resourcePath.includes('node_modules') ||
        context.resourcePath.includes('ant.design.pro.less') ||
        context.resourcePath.includes('global.less')
      ) {
        return localName;
      }
      const match = context.resourcePath.match(/src(.*)/);
      if (match && match[1]) {
        const antdProPath = match[1].replace('.less', '');
        const arr = antdProPath
          .split('/')
          .map(a => a.replace(/([A-Z])/g, '-$1'))
          .map(a => a.toLowerCase());
        return `antd-pro${arr.join('-')}-${localName}`.replace(/--/g, '-');
      }
      return localName;
    },
  },

  outputPath: './megaconsole', // 导出路径
  publicPath: '/megaconsole/', // 导出index引用带megaconsole前缀
  hash: true, // 导出文件带hash

  manifest: {
    basePath: '/megaconsole',
  },
  // 该字段控制动态修改主题
  // chainWebpack: webpackPlugin,//改善经常编译95%的问题
  extraBabelPlugins: [
    ['@babel/plugin-proposal-decorators', { legacy: true }],
    ['@babel/plugin-transform-class-properties', { loose: true }],
    ['@babel/plugin-transform-private-methods', { loose: true }],
    ['@babel/plugin-transform-private-property-in-object', { loose: true }],
    ['@babel/plugin-proposal-logical-assignment-operators', { loose: true }],
  ],
};

// 生产环境 去掉console
if (process.env.NODE_ENV === 'production') {
  config.extraBabelPlugins = ['transform-remove-console'];
  console.log('生产环境新增babel插件：transform-remove-console \n');
}

export default config;
