/*
 * @Description: 称号-编辑模态页
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2019-10-12 16:56:14
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-21 14:03:19
 */

import { Component } from 'react';
import { connect } from 'dva';
import { ILogin } from '@/models/login';
import { Modal, Switch, Input, Select, message, InputNumber, Button, AutoComplete, Popconfirm } from 'antd';
import * as styles from './ScriptEditorModal.less';
import { wfPropsGiftSourceType, wfPropsGiftType, wfPropsType } from "../../../dto/staticEnum";
import { PlusOutlined, MinusCircleOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { getGiftSourceStr, checkNameExist } from '@/utils/mallTool';
import { getCateName, getSelectName, getOptionList } from '@/utils/mallTool';
import React from 'react';
const { TextArea } = Input;
const { Option } = Select;

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  currentMerchantScriptList: scriptkill.currentMerchantScriptList,
  difficultyList: scriptkill.difficultyList,
  merchantDicList: scriptkill.merchantDicList,
}))

class ScriptEditorModal extends React.Component<any, any> {
  constructor(props) {
    super(props);
    const { dataModelType, currentScript } = props;
    //编辑
    if (dataModelType == 1) {
      this.state = {
        id: currentScript.id,
        name: currentScript.name,
        publisher: currentScript.publisher,
        role_num: currentScript.roleNum,
        roleStr: currentScript.roleStr,
        hot: currentScript.hot,
        script_desc: currentScript.desc,
        status: currentScript.status,
        themeList: currentScript.themeList,
        tips: currentScript.tips,
        difficulty: currentScript.difficulty,
        delsign: currentScript.delsign,
        themeDicList: JSON.parse(JSON.stringify(currentScript.themeDicList)),
      };
    } else {
      this.state = {
        id: currentScript.id,
        name: currentScript.name,
        publisher: currentScript.publisher,
        role_num: currentScript.roleNum,
        roleStr: currentScript.roleStr,
        hot: 0,
        script_desc: currentScript.desc,
        status: currentScript.status,
        themeList: currentScript.themeList,
        tips: currentScript.tips,
        difficulty: currentScript.difficulty,
        delsign: 0
      };
    }
  }

  render() {
    const { dataModelType, currentScript, isLoading } = this.props;
    let title = '';
    let okText = '提交';
    if (dataModelType == 1) {
      title = '编辑剧本ID【' + currentScript.id + '】';
      okText = '提交编辑';
    } else {
      title = '创建称号';
      okText = '提交创建';
    }
    return (
      <Modal
        width={550}
        bodyStyle={{ padding: '5px 10px 5px 10px' }}
        closable={false}
        maskClosable={false}
        confirmLoading={!!isLoading}
        centered={true}
        title={title}
        onCancel={this.onCloseModel}
        onOk={this.onClickOk}
        cancelText="取消"
        okText={okText}
        visible={dataModelType > 0 ? true : false}
      >
        <div>
          {dataModelType > 0 && this.renderEditModal()}
        </div>
      </Modal>
    );
  }

  renderEditModal = () => {
    const { dataModelType } = this.props;
    return (
      <div className={styles.modalForm}>
        <div className={styles.formItem}>
          <div className={styles.title}>名称：</div>
          <Input
            className={styles.content}
            value={this.state.name}
            onChange={this.onChangeName}
          />
        </div>
        <div className={styles.formItem}>
          <div className={styles.title}>描述：</div>
          <TextArea
            rows={4}
            className={styles.content}
            value={this.state.script_desc}
            onChange={this.onChangeDesc}
          />
        </div>
        <div className={styles.formItem}>
          <div className={styles.title}>发行商：</div>
          <Input
            className={styles.content}
            value={this.state.publisher}
            onChange={this.onChangeSort}
          />
        </div>
        <div className={styles.formItem}>
          <div className={styles.title}>角色数量：</div>
          <InputNumber
            className={styles.content}
            value={this.state.role_num}
            onChange={this.onChangeNum}
          />
        </div>
        <div className={styles.formItem}>
          <div className={styles.title}>提示：</div>
          <TextArea
            rows={4}
            className={styles.content}
            value={this.state.tips}
            onChange={this.onChangeCondition}
          />
        </div>
        {/* <div className={styles.formItem}>
          <div className={styles.title}>热门：</div>
          <Switch
            className={styles.toggle}
            checkedChildren="是"
            unCheckedChildren="否"
            defaultChecked={this.state.hot == 1}
            onChange={this.onChangeHot}
          />
        </div> */}
        <div className={styles.formItem}>
          <div className={styles.title}>难易度：</div>
          <Select
            className={styles.content}
            placeholder="请选择难易度"
            defaultValue={getCateName(this.state.difficulty, this.props.difficultyList)}
            onChange={this.onChangeType}>
            {this.props.difficultyList.map((item, index) => {
              return (
                <Option key={index.toString()} value={item.id}>
                  {item.name}
                </Option>
              );
            })}
          </Select>
        </div>
        <div className={styles.formItem}>
          <div className={styles.title}>主题选择：</div>
          <div className={styles.contentItem}>
            {this.state.themeDicList.map((item, index) => {
              return (
                // tslint:disable-next-line: jsx-key
                <div key={index} className={styles.modalFormSub}>
                  <div className={styles.formItemSub}>
                    <AutoComplete
                      allowClear={false}
                      className={styles.content}
                      options={getOptionList(this.props.merchantDicList)}
                      onSelect={(value, Option) => {
                        this.onSelectScrpit(value, Option, item);
                      }}
                      onChange={(value) => {
                        this.onChangeScrpit(value, item)
                      }}
                      defaultValue={getSelectName(item.id, this.props.merchantDicList)}
                      value={item.value == undefined ? "" : item.value}
                      placeholder="请输入选择"
                      filterOption={(inputValue, option) =>
                        option.value.indexOf(inputValue) !== -1
                      }
                    />
                  </div>
                  <div className={styles.formItemSub}>
                    <Popconfirm
                      title="确定要删除主题吗?"
                      onConfirm={() => {
                        this.deleteItem(item);
                      }}
                      okText="Yes"
                      cancelText="No"
                    >
                      <Button
                        className={styles.contentButtonSub}
                        type="primary"
                        shape="circle"
                        icon={<MinusCircleOutlined />}
                        size="small"
                      />
                    </Popconfirm>
                  </div>
                </div>
              );

            })}
            <Button className={styles.searchButton} type="primary" icon={<PlusOutlined />} onClick={this.onClickCreateTheme}>新增主题</Button>
          </div>
        </div>
        {/* <div className={styles.formItem}>
          <div className={styles.title}>状态：</div>
          <Switch
            className={styles.toggle}
            checkedChildren="上架"
            unCheckedChildren="下架"
            defaultChecked={this.state.delsign == null || this.state.delsign == 0}
            onChange={this.onChangedelsign}
          />
        </div> */}
      </div>
    );
  };

  onSelectScrpit = (value, option, item) => {
    const { themeDicList } = this.state;
    const val = value.substring(4, value.indexOf(" 名称"));
    for (const theme of themeDicList) {
      if (theme.ketIndex == item.ketIndex) {
        item.id = val;
        item.value = value;
      }
    }
    this.setState({ themeDicList })
  };

  onChangeScrpit = (value, item) => {
    const { themeDicList } = this.state;
    for (const theme of themeDicList) {
      if (theme.ketIndex == item.ketIndex) {
        item.value = value;
      }
    }
    this.setState({ themeDicList })
  };

  getThemeValue = (item) => {
    const { themeDicList } = this.state;
    for (const theme of themeDicList) {
      if (theme.ketIndex == item.ketIndex) {
        item.id = null;
      }
    }
    this.setState({ themeDicList })
  };

  onClickCreateTheme = () => {
    const { themeDicList } = this.state;
    let ketIndex = 0;
    for (const item of themeDicList) {
      if (ketIndex < item.ketIndex) {
        ketIndex = item.ketIndex;
      }
    }
    ketIndex = ketIndex + 1;
    const item = { ketIndex, name: null, id: null };
    themeDicList.push(item);
    this.setState({ themeDicList })
  };

  deleteItem = (item: any) => {
    const { themeDicList } = this.state;
    let index = 0;
    for (const theme of themeDicList) {
      if (theme.ketIndex == item.ketIndex) {
        break;
      }
      index++;
    }
    themeDicList.splice(index, 1);
    this.setState({ themeDicList })
  }

  onChangeHot = (checked: boolean) => {
    this.setState({ hot: checked ? 1 : 0 });
  };

  onChangeDesc = ({ target: { value } }) => {
    this.setState({ script_desc: value });
  };

  onChangeName = ({ target: { value } }) => {
    this.setState({ name: value });
  };

  onChangeNum = (value) => {
    this.setState({ role_num: value });
  };

  onChangeCondition = ({ target: { value } }) => {
    this.setState({ tips: value });
  };

  onChangeDescribe = ({ target: { value } }) => {
    this.setState({ script_desc: value });
  };

  onChangeSort = ({ target: { value } }) => {
    this.setState({ publisher: value });
  };

  onChangeSex = (value) => {
    this.setState({ sex: value });
  };

  onChangeSource = (value) => {
    this.setState({ source: value });
  };

  onChangeLevel = (value) => {
    this.setState({ level: value });
  };

  onChangeType = (value) => {
    this.setState({ difficulty: value });
  };

  onChangehot = (checked: boolean) => {
    this.setState({ is_default: checked ? 1 : 0 });
  };

  onChangedelsign = (checked: boolean) => {
    this.setState({ delsign: checked ? 0 : 1 });
  };

  checkNullStr = (value) => {
    return value || value == 0 ? value : "";
  };

  checkSame = (value1, value2, needPre) => {
    if (value1 != value2) {
      if (needPre) {
        return value1 + "->" + value2;
      } else {
        return value2;
      }

    }
    return "";
  };

  checkSameDifficulty = (value1, value2) => {
    if (value1 != value2) {
      const val1 = getCateName(value1, this.props.difficultyList);
      const val2 = getCateName(value2, this.props.difficultyList);
      return val1 + "->" + val2;
    }
    return "";
  };

  handleRequestOperateInfo = (request: any) => {

    const item: any = this.props.currentScript;

    const operateInfo: any = {};
    operateInfo.operate_user_id = this.props.uid;
    operateInfo.operate_type = 1;

    operateInfo.name = this.checkSame(item.name, request.name, true);
    operateInfo.script_desc = this.checkSame(item.desc, request.script_desc, false);
    operateInfo.publisher = this.checkSame(item.publisher, request.publisher, true);
    operateInfo.role_num = this.checkSame(item.roleNum, request.role_num, true);
    operateInfo.tips = this.checkSame(item.tips, request.tips, false);
    operateInfo.hot = this.checkSame(item.hot, request.hot, true);
    operateInfo.difficulty = this.checkSameDifficulty(item.difficulty, request.difficulty);
    operateInfo.delsign = this.checkSame(item.delsign, request.delsign, true);
    request.operateInfo = operateInfo;
    this.checkSameTheme(request);
  }

  handleRequest = (isCreating: boolean) => {
    const request: any = {
      id: this.state.id,
      name: this.state.name,
      publisher: this.state.publisher,
      role_num: this.state.role_num,
      hot: this.state.hot,
      script_desc: this.state.script_desc,
      status: this.state.status,
      themeList: this.state.themeList,
      tips: this.state.tips,
      difficulty: this.state.difficulty,
      delsign: this.state.delsign,
      themeDicList: this.state.themeDicList,

      where: this.props.where,
      merchant_id: this.props.currentMerchantId,
    };
    return request;
  };

  //提交编辑
  handleSubmitEdit = () => {
    const { dispatch } = this.props;
    const request = this.handleRequest(false);
    this.handleRequestOperateInfo(request);

    console.log("request", request);
    const flag = this.checkRequest(request, false);

    if (flag) {
      dispatch({
        type: 'scriptkill/updateScript',
        payload: request
      }).then(() => {
        this.onCloseModel();
      });
    }
  };

  handleSubmitCreate = () => {
    const { dispatch } = this.props;
    const request = this.handleRequest(true);
    console.log("request", request);
    const flag = this.checkRequest(request, true);
    if (flag) {
      // dispatch({
      //   type: 'mallManager/createTitle',
      //   payload: request
      // }).then(() => {
      //   this.onCloseModel();
      // });
    }
  };

  getOperateTheme = (request: any) => {
    let oriStr = "";
    let newStr = "";
    for (const item of this.props.currentScript.themeDicList) {
      oriStr += item.id + ",";
    }
    for (const item of request.themeDicList) {
      newStr += item.id + ",";
    }
    request.operateInfo.theme = oriStr.substring(0, oriStr.length - 1) + "->" + newStr.substring(0, newStr.length - 1);
  }

  checkSameTheme = (request: any) => {
    if (request.themeDicList.length != this.props.currentScript.themeDicList.length) {
      this.getOperateTheme(request);
      return false;
    } else {
      request.themeDicList.sort((a, b) => a.id - b.id);
      this.props.currentScript.themeDicList.sort((a, b) => a.id - b.id);

      for (let index = 0; index < this.props.currentScript.themeDicList.length; index++) {
        const oriT = this.props.currentScript.themeDicList[index];
        const newT = request.themeDicList[index];
        if (oriT.id != newT.id) {
          this.getOperateTheme(request);
          return false;
        }
      }
      return true;
    }
  }

  checkRequest = (request: any, isCreating: boolean) => {
    if (!isCreating && (request.id == null || request.id == undefined || request.id <= 0)) {
      message.error("数据错误，请重新编辑");
      return false;
    }

    if (request.script_desc == this.props.currentScript.desc &&
      request.publisher == this.props.currentScript.publisher &&
      request.role_num == this.props.currentScript.roleNum &&
      request.tips == this.props.currentScript.tips &&
      request.hot == this.props.currentScript.hot &&
      request.difficulty == this.props.currentScript.difficulty &&
      request.name == this.props.currentScript.name &&
      request.delsign == this.props.currentScript.delsign &&
      this.checkSameTheme(request)) {
      message.error("没有更改");
      return false;
    }

    if (request.name == null || request.name == undefined || request.name == "") {
      message.error("请填写名称");
      return false;
    }

    if (request.script_desc == null || request.script_desc == undefined || request.script_desc == "") {
      message.error("请填写描述");
      return false;
    }

    if (request.publisher == null || request.publisher == undefined || request.publisher == "") {
      message.error("请填写发行商");
      return false;
    }

    if (request.role_num == null || request.role_num == undefined || request.role_num < 0) {
      message.error("请填写角色数量");
      return false;
    }

    if (request.tips == null || request.tips == undefined || request.tips == "") {
      message.error("请填写提示");
      return false;
    }

    if (request.themeDicList == undefined || request.themeDicList == null || request.themeDicList.length <= 0) {
      message.error("请填写主题");
      return false;
    }
    if (request.themeDicList && request.themeDicList.length > 1) {
      message.error("剧本主题只能设置1个");
      return false;
    }
    for (const item of request.themeDicList) {
      if (item.id == undefined || item.id == null || item.id == "") {
        message.error("请填写所有主题");
        return false;
      }
    }

    return true;
  }

  //关闭模态页
  onCloseModel = () => {
    this.props.onClickClose();
  };

  onClickOk = () => {
    const { dataModelType } = this.props;
    if (dataModelType == 1) {
      this.handleSubmitEdit();
    } else {
      this.handleSubmitCreate();
    }
  };
}

export default ScriptEditorModal;
