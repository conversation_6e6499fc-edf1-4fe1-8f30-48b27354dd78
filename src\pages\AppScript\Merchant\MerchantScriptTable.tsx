/*
 * @Description: 称号管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-12 11:18:13
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  Popconfirm,
  Modal,
  Col,
  Radio,
  Image,
  Table,
  Input,
  Typography,
} from 'antd';
const { TextArea } = Input;
const { Text } = Typography;
import { PlusOutlined, CloudUploadOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import * as styles from './MerchantScriptTable.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import AppList from '../../../pages/AppList';
import { ColumnProps } from 'antd/lib/table';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName, getSelectName } from '@/utils/mallTool';
import errorImgRectangle from '../../../assets/errorImgRectangle.png';
import { checkNotNull } from '@/utils/emptyTool';
import ScriptEditorModal from "../Script/ScriptEditorModal"
import ScriptImageEditorModal from "../Script/ScriptImageEditorModal"
export const PAGESIZE = 5;

const defaultPayload = { current: 1, pageCount: PAGESIZE, where: "m.status = 1" };

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  currentMerchantScriptList: scriptkill.currentMerchantScriptList,
  difficultyList: scriptkill.difficultyList,
}))

export default class MerchantScriptTable extends Component<any, any> {
  constructor(props) {
    super(props);

    this.state = {
      currentPage: 1,
      currentMerchant: null,
      dataModelType: -1,
      imageModelType: -1,
      imageViewModelType: -1,
      where: "m.status = 1",
    }
  }

  render() {
    const { isLoading } = this.props;
    const { refuseModalType, dataModelType, imageModelType } = this.state;
    return (
      <Spin spinning={!!isLoading}>
        {this.renderScript()}
        {refuseModalType > 0 &&
          <RefuseModal
            refuseModalType={refuseModalType}
            onCloseRefuseModel={this.onCloseRefuseModel}
            refuseMerchantAction={this.refuseMerchantAction}
            onRefuseMesChange={this.onRefuseMesChange}
            currentScript={this.state.currentScript}>
          </RefuseModal>
        }
        {dataModelType > 0 &&
          <ScriptEditorModal
            dataModelType={dataModelType}
            currentScript={this.state.currentScript}
            onClickClose={this.onClickClose}
            currentMerchantId={this.props.currentMerchantId}
            currentScriptId={this.props.currentScriptId}
            where={this.props.where} />}
        {imageModelType > 0 &&
          <ScriptImageEditorModal
            imageModelType={imageModelType}
            currentScript={this.state.currentScript}
            onClickClose={this.onClickClose}
            currentMerchantId={this.props.currentMerchantId}
            currentScriptId={this.props.currentScriptId}
            where={this.props.where} />
        }
      </Spin>

    );
  }

  renderScript() {
    const { scriptList, isLoading } = this.props;
    const { dataModelType, imageViewModelType, currentMerchant } = this.state;
    return (
      <div>
        <Card>
          <Table
            scroll={{ x: 2050 }}
            columns={this.makeColumnsMerchant(this.props.type)}
            dataSource={scriptList}
            expandable={{
              expandedRowRender: record =>
                <div>
                  <Table
                    scroll={{ x: 800 }}
                    columns={this.columnsRole}
                    dataSource={record.roleList}
                    loading={!!isLoading}
                    bordered={true}
                    rowKey={(record, index) => index.toString()}
                    pagination={false}
                  />
                </div>,
              rowExpandable: record => record.roleList != null && record.roleList.length > 0,
            }}
            loading={!!isLoading}
            bordered={true}
            rowKey={(record, index) => index.toString()}
            pagination={{  // 分页
              pageSize: PAGESIZE,
              current: this.state.currentPage,
              total: scriptList.length,
              onChange: this.changePage,
            }}
          />
        </Card>
      </div>
    );
  }

  makeColumnsMerchant(type: any) {
    const columnsMerchant: ColumnProps<any>[] = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 70,
        align: 'center',
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 100,
        align: 'center',
      },
      {
        title: '审核',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        align: 'center',
        render: (val) => {
          let color = "#000000";
          switch (val) {
            case 1:
              color = "#FF0000";
              break;
            case 2:
              color = "#1E90FF";
              break;
            case 10:
              color = "#000000";
              break;
            default:
              color = "#000000";
              break;
          }
          let str = getCateName(val, this.props.statusList);
          if (val == 20) {
            str = "下线";
          }
          return <div style={{ color }}>{str}</div>;
        }
      },
      {
        title: '状态',
        dataIndex: 'delsign',
        align: 'center',
        width: 70,
        render: (val) => {
          return <div>{val == 1 ? '删除' : "正常"}</div>;
        }
      },
      {
        title: '图片展示',
        width: 100,
        align: 'center',
        render: (record) => {
          if (record.img != null) {
            const str = record.img;
            if (str != null && str != '') {
              return (
                <Image src={str} width={80} height={80}></Image>
              );
            }
          }
          return <div>无资源图</div>;
        }
      },
      {
        title: '描述',
        dataIndex: 'desc',
        align: 'center',
      },
      {
        title: '热门',
        dataIndex: 'hot',
        align: 'center',
        width: 70,
        render: (val) => {
          return <div>{val == 1 ? "是" : "否"}</div>;
        }
      },
      {
        title: '角色数量',
        dataIndex: 'roleNum',
        width: 70,
        align: 'center',
      },
      {
        title: '主题',
        dataIndex: 'themeList',
        align: 'center',
      },
      {
        title: '难易度',
        dataIndex: 'difficulty',
        align: 'center',
        width: 80,
        render: (val) => {
          return <div>{getCateName(val, this.props.difficultyList)}</div>;
        }
      },
      {
        title: '提示',
        dataIndex: 'tips',
        align: 'center',
      },
      {
        title: '发行商',
        dataIndex: 'publisher',
        width: 100,
        align: 'center',
      },
    ];

    const operate: ColumnProps<any> = {
      title: '操作',
      width: 120,
      dataIndex: 'delsign',
      align: 'center',
      render: (val, record, index) => {
        if (record.status == 1 || record.status == 2) {//审核中/审核不通过
          return (
            <div>
              <Popconfirm
                title={`确定要审核通过当剧本吗?`}
                onConfirm={() => this.onClickAudit(record)}
                okText="Yes"
                cancelText="No"
              >
                <Button className={styles.marginLeft} type="primary" danger={true}>
                  通过
                </Button>
              </Popconfirm>
              <Button className={styles.marginLeft} type="primary" danger={true} onClick={() => this.refuseMerchant(record)}>
                驳回
              </Button>
            </div>
          )
        } else if (record.status == 20) {//下线
          return (
            <div>
              <Button disabled={!this.props.action} className={styles.marginLeft} type="primary" onClick={() => this.editMerchant(record)}>
                修改信息
              </Button>
              <Button disabled={!this.props.action} className={styles.marginLeft} type="primary" onClick={() => this.editImageMerchant(record)}>
                修改图片
              </Button>
            </div>
          )
        } else {//审核通过
          return (
            <div>
              <Button disabled={!this.props.action} className={styles.marginLeft} type="primary" onClick={() => this.editMerchant(record)}>
                修改信息
              </Button>
              <Button disabled={!this.props.action} className={styles.marginLeft} type="primary" onClick={() => this.editImageMerchant(record)}>
                修改图片
              </Button>
              <Popconfirm
                title={`确定要下架剧本吗?`}
                onConfirm={() => this.updateScriptDelsign(record)}
                okText="Yes"
                cancelText="No"
              >
                <Button className={styles.marginLeft} type="primary" danger={true} >
                  下架
              </Button>
              </Popconfirm>
            </div>
          )
        }

      }
    };

    const opeUser: ColumnProps<any> = {
      title: '审核员',
      key: 'ope_user',
      width: 70,
      align: 'center',
      render: (record) => {
        if (record.operate != undefined && checkNotNull(record.operate.nickname)) {
          return <div>{record.operate.nickname}</div>;
        }
        return <div>\</div>;
      }
    }

    const opeMes: ColumnProps<any> = {
      title: '拒绝理由',
      key: 'ope_mes',
      width: 70,
      align: 'center',
      render: (record) => {
        if (record.operate != undefined && checkNotNull(record.operate.refuse_mes)) {
          return <div>{record.operate.refuse_mes}</div>;
        }
        return <div>\</div>;
      }
    }

    const opeTime: ColumnProps<any> = {
      title: '审核时间',
      key: 'ope_createtime',
      width: 120,
      align: 'center',
      render: (record) => {
        if (record.operate != undefined && checkNotNull(record.operate.createtime)) {
          return <div>{getTimeStr(record.operate.createtime)}</div>;
        }
        return <div>\</div>;
      }
    }

    if (this.props.action) {
      columnsMerchant.splice(4, 0, operate);
    }

    if (type == 1) {
      return columnsMerchant;
    } else if (type == 2) {
      columnsMerchant.push(opeUser);
      columnsMerchant.push(opeMes);
      columnsMerchant.push(opeTime);
    } else {
      columnsMerchant.push(opeUser);
      columnsMerchant.push(opeTime);
    }
    return columnsMerchant;
  }



  //表格列规则
  columnsRole: ColumnProps<any>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'delsign',
      align: 'center',
      width: 70,
      render: (val) => {
        return <div>{val == 1 ? '删除' : "正常"}</div>;
      }
    },
    {
      title: '图片展示',
      width: 100,
      align: 'center',
      render: (record) => {
        if (record.oss_url != null) {
          const str = record.oss_url;
          if (str != null && str != '') {
            return (
              <Image src={str} width={80} height={80}></Image>
            );
          }
        }
        return <div>无资源图</div>;
      }
    },
    {
      title: '描述',
      dataIndex: 'role_desc',
      key: 'role_desc',
    },
  ];

  changePage = (page) => {
    this.setState({ currentPage: page });
  };

  onClickAudit = (script: any) => {
    const { dispatch, currentMerchantScriptList } = this.props;
    script.merchant_id = this.props.currentMerchantId;
    script.where = this.props.where;
    script.operateUserId = this.props.uid;
    dispatch({
      type: 'scriptkill/confirmScript',
      payload: script
    });
  };

  updateScriptDelsign = (script) => {
    const { dispatch } = this.props;
    const req = {
      data: {
        scriptId: script.id
      },
      id: script.id,
      merchant_id :this.props.currentMerchantId,
      where : this.props.where,
    }
    console.log("req", req);
    dispatch({
      type: 'scriptkill/updateScriptDelsign',
      payload: req
    });
  }

  refuseMerchant = (script) => {
    this.setState({ refuseModalType: 1 });
    this.setState({ refuseMes: "" });
    this.setState({ currentScript: script });
  }

  editMerchant = (script) => {
    this.setState({ dataModelType: 1 });
    this.setState({ currentScript: script });
  }

  editImageMerchant = (script) => {
    this.setState({ imageModelType: 1 });
    this.setState({ currentScript: script });
  }

  //关闭模态页
  onCloseRefuseModel = () => {
    this.setState({ refuseModalType: 0 });
    this.setState({ refuseMes: "" });
  };

  refuseMerchantAction = (script) => {
    const { dispatch, currentMerchantScriptList } = this.props;
    script.merchant_id = this.props.currentMerchantId;
    script.where = this.props.where;
    script.operateUserId = this.props.uid;
    script.refuseMes = this.state.refuseMes;

    dispatch({
      type: 'scriptkill/refuseScript',
      payload: script
    }).then(() => {
      this.onCloseRefuseModel();
    });
  }

  onRefuseMesChange = (str) => {
    this.setState({ refuseMes: str });
  }

  onClickClose = () => {
    this.setState({ dataModelType: -1, imageModelType: -1, currentScript: null });
  };
}


class RefuseModal extends React.Component<any, any> {
  render() {

    const { refuseModalType, isLoading } = this.props;
    return (
      <Modal
        width={800}
        bodyStyle={{ padding: '5px 10px 5px 10px' }}
        closable={true}
        maskClosable={true}
        confirmLoading={!!isLoading}
        centered={true}
        title="填写驳回理由"
        onCancel={this.props.onCloseRefuseModel}
        footer={[]}
        visible={refuseModalType > 0 ? true : false}
      >
        <div>
          <TextArea showCount={true} maxLength={100}
            onChange={e => {
              this.props.onRefuseMesChange(e.target.value)
            }} />
          <Popconfirm
            title={`确定要驳回剧本审核吗?`}
            onConfirm={() => this.props.refuseMerchantAction(this.props.currentScript)}
            okText="Yes"
            cancelText="No"
          >
            <Button className={styles.marginLeft} type="primary" danger={true}>驳回</Button>
          </Popconfirm>
        </div>
      </Modal>
    )
  }
}