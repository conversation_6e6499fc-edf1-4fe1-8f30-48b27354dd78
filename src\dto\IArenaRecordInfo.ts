export interface IArenaActivityList {
  id: number;
  user_no: number;//
  create_time: string;//对局时间
  win: string;//胜负
  escape: number;//逃跑
  score: number;//分数
  valid: number; //有效局
  role_img: string;//图片
}

export interface IArenaUserInfo {
  id: number;
  user_id: number;//
  current_level: number;//当前等级
  win: number;//胜
  lose: number;//负
  score: number;//分数
  mvp: number; //mvp数
  nickname: string;//名字
}

export interface IArenaResp {
  info: IArenaUserInfo[];
  list: IArenaActivityList[];
}
