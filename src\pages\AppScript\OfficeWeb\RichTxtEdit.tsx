/**
 *
 * @param param0 富文本编辑器
 */

import BraftEditor, { EditorState, ExtendControlType } from 'braft-editor';
import { ContentUtils } from 'braft-utils';
import 'braft-editor/dist/index.css';
import * as styles from './RichTxtEdit.less';
import { Card, message, Button, Upload, Spin } from 'antd';
import React from 'react';
import { getHtmlDraft, saveHtmlDraft, getGlobalEnv } from '@/utils/utils';
import Icon, { PictureOutlined } from '@ant-design/icons';
import AppList from './../../AppList';
import moment from 'moment';
import { UploadChangeParam, RcFile, UploadFile } from 'antd/es/upload/interface';
import { number } from 'prop-types';
import { UploadPath } from '@/dto/staticEnum';
import { getDayStr } from '@/utils/momentTool';
import { getUUID } from '@/utils/utils';
// 将HTML字符串转换为编辑器所需要的EditorState实例
const htmlToEditorState = (htmlString: string) => {
	const editorState = BraftEditor.createEditorState(htmlString);
	return editorState;
};

export interface RichTxtEditProps {
	defaultValue: string;
	draftKey: string;
	onGetHtml: (html: string) => void;
}

export interface RichTxtEditState {
	// htmlString: string;
	editorState: EditorState;
	isSpining: boolean;
}

// 定义rem基准值
const sizeBase = 16;
// // 定义输入转换函数
// const unitImportFn = (unit, type, source) => {
//   // type为单位类型，例如font-size等
//   // source为输入来源，可能值为create或paste
//   console.log(type, source)
//   // 此函数的返回结果，需要过滤掉单位，只返回数值
//   if (unit.indexOf('rem')) {
//     return parseFloat(unit) * sizeBase
//   } else {
//     return parseFloat(unit)
//   }
// }

// // 定义输出转换函数
// const unitExportFn = (unit, type, target) => {
//   console.log('type',type)
//   console.log('target',target)
//   if (type === 'line-height') {
//     // 输出行高时不添加单位
//     return unit
//   }
//   console.log('target',target)
//   // target的值可能是html或者editor，对应输出到html和在编辑器中显示这两个场景
//   if (target === 'html') {
//     // 只在将内容输出为html时才进行转换
//     return unit / sizeBase + 'rem'
//   } else {
//     // 在编辑器中显示时，按px单位展示
//     return unit + 'px'
//   }
// }
//输出时px转rem
const outPutRemHtml = (editorState: EditorState) => {
	let oldHtml = editorState.toHTML();
	const patternRex = /([1-9]\d*|0)(.\d*)?px/g;
	const pxArray = oldHtml.match(patternRex);
	console.log('px数组', pxArray);
	// tslint:disable-next-line: prefer-for-of
	// tslint:disable-next-line:forin
	for (const i in pxArray) {
		const pxItem = pxArray[i];
		const pxNumer = pxItem.split('px')[0];
		const remNumber = (pxNumer / sizeBase).toFixed(2);
		const remItem = remNumber + 'rem';
		oldHtml = oldHtml.replace(pxItem, remItem);
	}
	return oldHtml;
};

//输入时rem转px
export const inPutRemHtml = (oldHtml: string) => {
	const patternRex = /([1-9]\d*|0)(.\d*)?rem/g;
	const remArray = oldHtml.match(patternRex);
	// console.log('rem数组', remArray);
	// tslint:disable-next-line: prefer-for-of
	// tslint:disable-next-line:forin
	for (const i in remArray) {
		const remItem = remArray[i];
		const remNumer = parseFloat(remItem.split('rem')[0]);
		const pxNumber = (remNumer * sizeBase).toFixed(2);
		const pxItem = pxNumber + 'px';
		oldHtml = oldHtml.replace(remItem, pxItem);
	}
	return oldHtml;
};

const ppToBr = (oldHtml: string) =>{
  const patternRex = /<p><\/p>/g;
	const remArray = oldHtml.match(patternRex);
	// console.log('rem数组', remArray);
	// tslint:disable-next-line: prefer-for-of
	// tslint:disable-next-line:forin
	for (const i in remArray) {
		const remItem = remArray[i];
		oldHtml = oldHtml.replace(remItem, '<br/>');
	}
	return oldHtml;
}

class RichTxtEdit extends React.Component<any, any> {
	private editorInstance = null;
	private interval = null;
	private updateImtes;
	constructor(props) {
		super(props);
		let initHtml = props.defaultValue;
		const draftHtml = getHtmlDraft(props.draftKey);
		// console.log("存档内容",draftHtml);
		if (draftHtml && draftHtml !== initHtml) {
			initHtml = draftHtml;
		}
		console.log("initHtml",initHtml);
		this.state = {
			// htmlString: initHtml,
			editorState: htmlToEditorState(inPutRemHtml(initHtml)),
			isSpining: false
		};
	}
	// 手动保存草稿
	onClickSaveDraft = () => {
		saveHtmlDraft(this.props.draftKey, this.editorInstance.getValue().toHTML());
		message.success('保存草稿成功');
	};

	// 自定义上传
	onUpdateImgHandle = (info: UploadChangeParam) => {
		if (info.file.status === 'uploading') {
			console.log('上传中。。。。');
			this.setState({ isSpining: true });
		} else {
			console.log('上传结束', info);
		}
		if (info.file.status === 'done') {
			const oriName = info.file.name;		
			const nameList = oriName.split(".");
			const extension = nameList.pop();
			const imgUrl = this.updateImtes.url + this.updateImtes.name + "." + extension;
			console.log('上传成功，oss地址', imgUrl);
			this.setState({
				editorState: ContentUtils.insertMedias(this.state.editorState, [
					{
						type: 'IMAGE',
						url: imgUrl
					}
				])
			});
			this.setState({ isSpining: false });
		} else if (info.file.status === 'error') {
			message.error(`${info.file.name} 上传失败`);
			this.setState({ isSpining: false });
		}
	};

	getUploadData = (file: UploadFile) => {
		console.log('上传文件name', file.name);
		const fileType = file.name.split('.')[1];
		console.log('上传文件类型', fileType);
		const timestamp = moment().valueOf();
		const name = `${getDayStr()}/${this.props.oss_path_key}/${getUUID(12)}`;
		this.updateImtes = {
			id: 1,
			url: AppList.imageOssScriptSkillMerchantPic,
			name,
			status: false,
			type: UploadPath.ossMomentPicture,
			fileExtension: "",
			showName: "资源图",
			needInsertDocument: 0,
		};
		return this.updateImtes;
	};

	private extendControls: ExtendControlType[] = [
		{
			key: 'antd-uploader',
			type: 'component',
			component: (
				<Upload
					action={'/megaupload'}
					headers={{ 'Access-Control-Allow-Origin': '*', 'X-Requested-With': null }}
					showUploadList={false}
					data={this.getUploadData}
					// beforeUpload={this.beforeUpload}
					onChange={this.onUpdateImgHandle}
				>
					{/* 这里的按钮最好加上type="button"，以避免在表单容器中触发表单提交，用Antd的Button组件则无需如此 */}
					<button type="button" className="control-item button upload-button" data-title="oss上传">
						<PictureOutlined />
					</button>
				</Upload>
			)
		},
		{
			key: 'save-draft',
			type: 'button',
			text: '保存草稿',
			onClick: this.onClickSaveDraft
		}
	];

	render() {
		const { defaultValue } = this.props;
		const { editorState, isSpining } = this.state;
		return (
			<Spin spinning={!!isSpining} tip={'图片上传中...'}>
				<div className={styles.mega_rich_txt_root}>
					<Card className={styles.edit_root}>
						<BraftEditor
							ref={instance => (this.editorInstance = instance)}
							defaultValue={editorState}
							onChange={this.onHandleInput}
							// converts={{ unitImportFn, unitExportFn }}
							value={editorState}
							extendControls={this.extendControls}
						/>
						<Button type="primary" onClick={this.onClickGetHtml}>
							提取文本
						</Button>
					</Card>

					<Card className={styles.preview_root}>
						<div>实时预览: </div>
						<div className={styles.content} dangerouslySetInnerHTML={{ __html: ppToBr(editorState.toHTML())  }} />
					</Card>
				</div>
			</Spin>
		);
	}


	tick = () => {
		// 定时保存
		if (saveHtmlDraft(this.props.draftKey,ppToBr(this.state.editorState.toHTML()) )) {
			message.success('保存草稿成功');
		}
	};

	componentDidMount() {
		this.interval = setInterval(this.tick, 3000);
	}

	componentWillUnmount() {
		clearInterval(this.interval);
	}

	onHandleInput = (editorState: EditorState) => {
		this.setState({
			editorState
		});
	};

	onClickGetHtml = () => {
    let outHtml = outPutRemHtml(this.state.editorState)
    outHtml = ppToBr(outHtml)
		this.props.onGetHtml(outHtml);
	};
}

export default RichTxtEdit;
