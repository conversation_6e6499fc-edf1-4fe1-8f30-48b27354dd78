/*
 * @Description: 称号管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-19 13:57:35
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  AutoComplete,
  Row,
  Col,
  Radio,
  Image,
  Table,
  Tag,
  Typography,
  message,
  InputNumber,
  Popconfirm,
  Input
} from 'antd';
const { Text } = Typography;
import { CheckOutlined, EditOutlined } from '@ant-design/icons';
import { PlusOutlined, CloudUploadOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import * as styles from './UserInfoManager.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import AppList from '../../../pages/AppList';
import { ColumnProps } from 'antd/lib/table';
import { checkNotNull } from '@/utils/emptyTool';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName, getSelectName } from '@/utils/mallTool';
import { getUserAvatar } from '../ScriptUtils/ScriptUtils';
import UserInfoAvatarManager from './UserInfoAvatarManager';
import UserInfoBgManager from './UserInfoBgManager';
export const PAGESIZE = 8;

export enum tabEnum {
  avatar = 1,
  bg = 2,
}

const userTabList = [
  {
    key: tabEnum.avatar,
    tab: "头像历史",
  },
  {
    key: tabEnum.bg,
    tab: "背景板历史",
  },
];

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  merchantSimpleList: scriptkill.merchantSimpleList,
  scriptSimpleList: scriptkill.scriptSimpleList,
  userInfo: scriptkill.userInfo,
  merchantDicList: scriptkill.merchantDicList,
}))

export default class UserInfoManager extends Component<any, any> {
  constructor(props) {
    super(props);

    this.state = {
      userId: null,
      pageTabkey: tabEnum.avatar,
    }

  }

  render() {
    const { isLoading } = this.props;
    return (
      <Spin spinning={!!isLoading}>
        <PageHeaderWrapper
          title="检索条件"
          content={this.renderHeader()}>
          {this.renderDefaultStatus()}
        </PageHeaderWrapper>
      </Spin>
    );
  }

  renderHeader() {
    const { isLoading } = this.props;
    const { statusId } = this.state;
    return (
      <Row gutter={{ md: 8 }} style={{ width: 500 }}>
        <Col md={8} sm={12} style={{ marginBottom: 12 }}>
          <InputNumber
            style={{ width: 200, marginRight: 10 }}
            placeholder="玩家的id"
            min={0}
            onChange={(value: number) => {
              this.setState({ userId: Number(value) });
            }}
          />
        </Col>
        <Col md={8} sm={12} style={{ marginBottom: 12 }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={this.props.isSearching}
            onClick={this.handleSearch}
          >
            查询
					</Button>
        </Col>
      </Row>
    );
  }

  renderDefaultStatus() {
    const { userInfo, isLoading, } = this.props;
    const { pageTabkey } = this.state;
    const isShowSpin = !!isLoading;
    if (!userInfo || Object.keys(userInfo).length === 0) {
      const str = "请查询数据";
      return <Spin spinning={isShowSpin}>{!isLoading && <div>{str}</div>}</Spin>;
    }
    if (!userInfo.id) {
      const str = "查询数据为空";
      return <Spin spinning={isShowSpin}>{!isLoading && <div>{str}</div>}</Spin>;
    }
    return (
      <Spin spinning={isShowSpin}>
        <Card>
          <div className={styles.statusAvatar}>
            <Image className={styles.nickDiv} src={getUserAvatar(userInfo.avatar)} />

            <div className={styles.nickDiv}>
              <div>ID: {userInfo.id}</div>
              <div className={styles.eidtDiv}>
                昵称: {userInfo.nickname}
              </div>
              <div className={styles.eidtDiv}>
                性别: {this.getUserSexStr(userInfo.sex)}
              </div>
            </div>
          </div>

          <Row gutter={{ md: 4, xs: 4 }} className={styles.statusRow}>
            <Col md={6} xs={12} style={{ marginBottom: 12 }} className={styles.statusColumn}>
              <div>创建时间: {getTimeStr(userInfo.createtime)}</div>
            </Col>
            <Col md={6} xs={12} style={{ marginBottom: 12 }} className={styles.statusColumn}>
              <div>经验值: {userInfo.exp}</div>
            </Col>
            <Col md={6} xs={12} style={{ marginBottom: 12 }} className={styles.statusColumn}>
              <div className={styles.eidtDiv}>等级: {userInfo.level}</div>
            </Col>
            <Col md={6} xs={12} style={{ marginBottom: 12 }} className={styles.statusColumn}>
              <div>综合得分: {userInfo.score}</div>
            </Col>
            <Col md={6} xs={12} style={{ marginBottom: 12 }} className={styles.statusColumn}>
              <div>跳车次数: {userInfo.escape_num}</div>
            </Col>
            <Col md={6} xs={12} style={{ marginBottom: 12 }} className={styles.statusColumn}>
              <div>总剧本数: {userInfo.script_num}</div>
            </Col>
            <Col md={6} xs={12} style={{ marginBottom: 12 }} className={styles.statusColumn}>
              <div>找到真相: {userInfo.find}</div>
            </Col>
            <Col md={6} xs={12} style={{ marginBottom: 12 }} className={styles.statusColumn}>
              <div>魅力: {userInfo.charm}</div>
            </Col>
            <Col md={6} xs={12} style={{ marginBottom: 12 }} className={styles.statusColumn}>
              <div>城市: {userInfo.city}</div>
            </Col>
            <Col md={6} xs={12} style={{ marginBottom: 12 }} className={styles.statusColumn}>
              <div>商人: {userInfo.merchantFlag > 0 ? "是" : "否"}</div>
            </Col>
          </Row>
        </Card>
        <Card
          tabList={userTabList}
          onTabChange={this.onTabChange}>
          {pageTabkey == tabEnum.avatar && <UserInfoAvatarManager />}
          {pageTabkey == tabEnum.bg && <UserInfoBgManager />}
        </Card>
      </Spin>
    );
  }

  onTabChange = key => {
    this.setState({ pageTabkey: key });
    this.dispatchTab(key);
  };

  getUserSexStr = (sex) => {
    switch (sex) {
      case 0:
        return "无";
      case 1:
        return "男";
      case 2:
        return "女";
      default:
        return "";
    }
  }

  handleSearch = () => {
    const { dispatch } = this.props;
    const { userId, pageTabkey } = this.state;
    if (!userId || userId < 1) {
      message.error("请先填写玩家 ID");
      return;
    }
    const req = { userId };
    dispatch({
      type: 'scriptkill/getUserInfo',
      payload: req
    }).then(() => {
      this.dispatchTab(pageTabkey);
    });
  };

  getUserInfoAvatar = () => {
    const { dispatch } = this.props;
    const { userId } = this.state;
    const req = { userId };
    if (userId) {
      dispatch({
        type: 'scriptkill/getUserInfoAvatar',
        payload: req
      }).then(() => {
      });
    }
  }

  getUserInfoBg = () => {
    const { dispatch } = this.props;
    const { userId } = this.state;
    const req = { userId };
    if (userId) {
      dispatch({
        type: 'scriptkill/getUserInfoBg',
        payload: req
      }).then(() => {
      });
    }
  }

  dispatchTab = (pageTabkey) => {
    switch (Number(pageTabkey)) {
      case tabEnum.avatar:
        this.getUserInfoAvatar();
        return;
      case tabEnum.bg:
        this.getUserInfoBg();
        return;
      default:
        return;
    }
  }

}
