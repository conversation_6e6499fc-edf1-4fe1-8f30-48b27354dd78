/*
 * @Description: 称号管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-26 09:50:13
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  Modal,
  Row,
  Col,
  Radio,
  Image,
  Popconfirm,
  Avatar,
  Typography,
  message,
} from 'antd';
const { Meta } = Card;
import { EditOutlined, UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import * as styles from './MomentPictureManager.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import AppList from '../../../pages/AppList';
import { ColumnProps } from 'antd/lib/table';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName, getOptionList } from '@/utils/mallTool';
import { checkNotNull } from '@/utils/emptyTool';
import { getErrorImg, getImageSize } from '@/utils/resUtils';
import MomentPictureImageEditorModal from './MomentPictureImageEditorModal';
import MomentPictureEditorModal from './MomentPictureEditorModal';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import Center from '@/pages/Account/Center/Applications';
export const PAGESIZE = 8;
const MB = 1000000;
const KB = 1000;

@connect(({ loading, login, merchantMoment }: { loading: IdvaLoading; login: ILogin; merchantMoment: any }) => ({
  uid: login.uid,
  isLoading: loading.models['merchantMoment'],
  momentList: merchantMoment.momentList,
  momentListCount: merchantMoment.momentListCount,
  pictureList: merchantMoment.pictureList,
}))

class MomentPictureManager extends Component<any, any> {
  constructor(props) {
    super(props);

    this.state = {
      currentMerchantId: -1,
      imageModelType: -1,
      dataModelType: -1,
      currentMerchantValue: "",
      currentPicture: "",
      pictureSizeList: [],
    }
    // this.dispatchClearPictureList();
    this.dispatchPicList();
  }


  render() {
    const { isLoading } = this.props;
    const { imageModelType, dataModelType, currentPicture, new_usernameSearch, statusSearch, createtimeSearch, updatetimeSearch } = this.state;
    return (
      <Modal
        title="图片管理"
        visible={true}
        centered={true}
        onOk={this.props.onHandleClose}
        onCancel={this.props.onHandleClose}
        okText="确定"
        width={1700}
        cancelText="取消"
      >
        <Spin spinning={!!isLoading}>
          <Button style={{ marginBottom: 10 }} type="primary" icon={<UploadOutlined />} onClick={this.createPicture}>
            新建图片
            </Button>
          <Row gutter={[4, 4]}>
            {this.props.pictureList && this.props.pictureList.length > 0 &&
              this.props.pictureList.map((item, index) => {
                return (
                  <Col key={index} span={6}>
                    <Card
                      hoverable={true}
                      // bodyStyle={{ height: 0 }}//marginTop: -50,
                      style={{ width: 400 }}
                      cover={this.getCover(item)}
                      actions={[
                        <Popconfirm
                          disabled={!item.material_url}
                          title={`确定要${item.delsign == 1 ? "上架" : "下架"}该图片吗?`}
                          onConfirm={() => { this.onClickDelete(item) }}
                          okText="Yes"
                          cancelText="No"
                        >
                          <Button type="link" disabled={!item.material_url}><DeleteOutlined key="setting" />{item.delsign == 1 ? "上架" : "下架"}</Button>,
                      </Popconfirm>,
                        <Button type="link" onClick={() => { this.onClickEdit(item) }}><EditOutlined key="setting" />编辑</Button>,
                        <Button type="link" onClick={() => { this.onClickUpload(item) }}><UploadOutlined key="setting" />上传</Button>,
                      ]}
                    >
                      <Meta
                        title={"排序值: " + item.sort_id}
                        description={`状态: ${item.delsign == 1 ? "下架" : "正常"}` + " 高度: " + item.show_height + " 文件大小: " + this.getShowImageSizeText(item.material_url)}
                      />
                    </Card>
                  </Col>

                );
              })}
            {!this.props.pictureList || this.props.pictureList.length <= 0 &&
              <div className={styles.marginCenter}><div>还没有图片</div></div>
            }
          </Row>

          {imageModelType > 0 &&
            <MomentPictureImageEditorModal
              imageModelType={imageModelType}
              currentPicture={currentPicture}
              onClickClose={this.onClickClose}
              dispatchPicList={this.dispatchPicList}
              news={this.props.news}
            />}
          {dataModelType > 0 &&
            <MomentPictureEditorModal
              dataModelType={dataModelType}
              onClickClose={this.onClickClose}
              currentPicture={currentPicture}
              moment_id={this.props.news.id}
            />}
        </Spin>
      </Modal>
    );
  }

  getCover = (item) => {
    if (item.material_url != undefined && item.material_url != null && item.material_url != "") {
      return <Image src={item.material_url} width={400} height={240} />
    } else {
      return <div style={{ width: 400, height: 120, textAlign: "center", marginTop: 120 }}>请上传图片</div>
    }
  }

  onClickDelete = (item) => {
    const { dispatch } = this.props;

    const delsign = item.delsign == 0 ? 1 : 0;

    if (this.props.news.delsign == 0 && delsign == 1) {
      this.props.dispatch({
        type: 'merchantMoment/getMomentPictureCanShowCount',
        payload: {
          moment_id: this.props.news.id,
        }
      }).then((data) => {
        const num = data.num;
        if (delsign == 1 && num <= 1) {//上架
          message.error("下架失败，已经上架的剧本圈至少要有一张图片是上架状态！")
          return;
        } else {
          item.delsign = delsign;
          dispatch({
            type: 'merchantMoment/updateMomentPicture',
            payload: item
          });
        }
      });
    } else {
      item.delsign = delsign;
      dispatch({
        type: 'merchantMoment/updateMomentPicture',
        payload: item
      });
    }
  }

  createPicture = () => {
    this.setState({ dataModelType: 2, currentPicture: { merchant_id: this.state.currentMerchantId } });
  }

  public async getShowImageSize(url) {
    const response = await getImageSize(url);

    const pictureSizeList: any[] = this.state.pictureSizeList;
    pictureSizeList.push({ url, size: response });

    this.setState({ pictureSizeList });
  }

  getShowImageSizeText(url) {
    for (const item of this.state.pictureSizeList) {
      if (item.url == url) {
        let size = item.size;
        if (size > MB) {
          size = (size / MB).toFixed(2) + "MB";
        } else {
          size = (size / KB).toFixed(2) + "KB";
        }
        return size;
      }
    }
    return "0B";
  }

  onClickEdit = (item) => {
    this.setState({ dataModelType: 1, currentPicture: item });
  }

  onClickUpload = (item) => {
    this.setState({ imageModelType: 1, currentPicture: item });
  }

  onClickClose = () => {
    this.setState({ dataModelType: -1, imageModelType: -1, currentPicture: null });
  }

  dispatchPicList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'merchantMoment/getMomentPicList',
      payload: {
        moment_id: this.props.news.id,
      },
    }).then(() => {
      if (this.props.pictureList != null && this.props.pictureList.length > 0) {
        for (const item of this.props.pictureList) {
          if (item.material_url) {
            this.getShowImageSize(item.material_url);
          }
        }
      }
    });
  };
}

export default MomentPictureManager;
