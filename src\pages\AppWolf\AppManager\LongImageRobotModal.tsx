import { IVisibleBinderContentProps } from "@/componentsEasy/visibleBinder"
import { INewPlayerRobotInsertParams, INewPlayerRobotItem, INewPlayerRobotUpdateTypeParams } from "@/dto/newPlayerRobotDao";
import { ILogin } from "@/models/login";
import { getUid } from "@/utils/request";
import { DatePicker, Form, Input, Modal, Select, Upload, Button, message, Checkbox, Collapse } from "antd"
import { InboxOutlined } from '@ant-design/icons';
import { useForm } from "antd/lib/form/Form";
import { connect } from "dva";
import moment from "moment";
import React, { useEffect, useState, RefObject, Component, useRef } from "react"
import { INewPlayerRobotModel } from "./models/newPlayerRobotModel";
import { UploadChangeParam } from 'antd/lib/upload';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { UploadPath } from '@/dto/staticEnum';
import AppList from '../../AppList';
import { number } from "prop-types";

const Option = Select.Option;

export interface INewPlayerRobotModalProps {
    item: INewPlayerRobotItem,
    dispatch: any,
    items: any,
    itemId: string,
    isAllProp:boolean,
    typeList: any
}



const LongImageRobotModal: React.FC<INewPlayerRobotModalProps & IVisibleBinderContentProps> = (props) => {

    const { visible, setVisible, item, dispatch, isAllProp, itemId, typeList } = props;

    const [form] = useForm()
    const [formRef] = [form];

    const [name, setName] = useState('');
    const [buttonName, setButtonName] = useState('');

    const curImgUrl = useRef('');
    const curImgButtonUrl = useRef('');
    const [isUpComplete, setIsUpComplete] = useState(false);
    const [isUpButtonComplete, setIsUpButtonComplete] = useState(false);
    const [msg_type, setMsg_type] = useState(-1);
    const [web_type, setWeb_type] = useState(-1);
    const [tend_type, setTend_type] = useState(0);
    const [tend_page, setTend_Page] = useState(0);
    const [tendShop, setTendShop] = useState(0);

    const ossData = (item: any) => {
        const obj = {
            name: item,
            bucket: "werewolf-resource",
            ossPath: "s/h5/",
        }
        return obj;
    }

    const getTendShop = (): string => {
        return tendShop == 1 ? "1" : "0";
    };

    useEffect(() => {


        // dispatch({
        //     type: 'illustratedBook/fetchItems',
        //     payload: {},
        // })
    }, [])

    //选中商城
    const onShopChange = (e: CheckboxChangeEvent) => {
        setTendShop(e.target.checked ? 1 : 0);

        if (e.target.checked) {
            setTend_type(0);
        } else {
            setTend_type(1);
        }
    };


    //长图
    //上传结果
    const onUploadIconChange = (info: UploadChangeParam) => {

        if (info.file.status !== 'uploading') {
        }
        if (info.file.status === 'done') {

            curImgUrl.current = name + '.png';


            formRef.setFieldsValue({
                longImage: curImgUrl.current
            });

            message.success(`${info.file.name} 上传成功`);

            setIsUpComplete(true);
        } else if (info.file.status === 'error') {
            message.error(`${info.file.name} 上传失败`);
        }
    };

    // 上传之前校验
    const beforeUploadIcon = (file): boolean => {
        const isPNG: boolean = file.type === 'image/png';
        if (!isPNG) {
            message.error('必须上传png格式图片！');
        }
        return isPNG;
    };

    //按钮图
    //上传结果
    const onUploadButtonChange = (info: UploadChangeParam) => {

        if (info.file.status !== 'uploading') {
        }
        if (info.file.status === 'done') {

            curImgButtonUrl.current = buttonName + '.png';

            formRef.setFieldsValue({
                buttonImage: curImgButtonUrl.current
            });

            message.success(`${info.file.name} 上传成功`);

            setIsUpButtonComplete(true);
        } else if (info.file.status === 'error') {
            message.error(`${info.file.name} 上传失败`);
        }
    };


    const onClickDelImg = () => {

        setIsUpComplete(false);
        curImgUrl.current = '';

        formRef.setFieldsValue({
            longImage: null
        });
    };

    const onClickDelButtonImg = () => {

        setIsUpButtonComplete(false);
        curImgButtonUrl.current = '';



        formRef.setFieldsValue({
            buttonImage: null
        });
    };

    const handleTypeOptionChange = (e) => {
        console.log(e);
        setTend_type(e);


    };

    const handlePageOptionChange = (e) => {
        setTend_Page(e);
    };

    const handleMsgTypeOptionChange = (e) => {

        // onClickDelButtonImg();

        setMsg_type(e);

        formRef.setFieldsValue({
            msg_type: e
        });

    };

    const handleWebTypeOptionChange = (e) => {

        // onClickDelButtonImg();

        setWeb_type(e);

        formRef.setFieldsValue({
            web_type: e
        });

    };

    useEffect(() => {
        if (item) {
            form.setFieldsValue({
                //编辑弹窗数据对应
                id: item.id,
                longImage: item.longImage,
                url_name: item.url_name,
                buttonImage: item.buttonImage == 'undefined' ? "" : item.buttonImage,
                msg_type: item.msg_type == 'undefined' ? "" : item.msg_type,
                web_type: item.web_type == 'undefined' ? "" : item.web_type,
                tend_type: item.tend_type == 'undefined' ? "" : item.tend_type,
                tend_page: item.tend_page == 'undefined' ? "" : item.tend_page,
                acTendUrl: item.acTendUrl == 'undefined' ? "" : item.acTendUrl,
                tend_shop: item.tend_shop == 'undefined' ? "" : item.tend_shop,
                tab1: item.tab1 == 'undefined' ? "" : item.tab1,
                tab2: item.tab2 == 'undefined' ? "" : item.tab2,
            })
            curImgUrl.current = item.longImage;
            curImgButtonUrl.current = item.buttonImage;

            if (curImgUrl.current) {
                setIsUpComplete(true);
            }

            if (curImgButtonUrl.current) {
                setIsUpButtonComplete(true);
            }

            setTend_Page(item.tend_page == 'undefined' ? "" : item.tend_page);
            setTend_type(item.tend_type == 'undefined' ? "" : item.tend_type);
            setTendShop(item.tend_shop == 'undefined' ? "" : item.tend_shop);
            setMsg_type(item.msg_type == 'undefined' ? "" : item.msg_type);
            setWeb_type(item.web_type == 'undefined' ? "" : item.web_type);
            if (item.tend_shop == "1") {
                setTendShop(1);
            } else {
                setTendShop(0);
            }

        }

        if (itemId) {
            setName('longImage_' + itemId);
            curImgUrl.current = name + '.png';

            setButtonName('buttonImage_' + itemId);
            curImgButtonUrl.current = buttonName + '.png';
        }

    }, [item, itemId, visible, curImgUrl, curImgButtonUrl])

    function handleCancel() {
        setVisible(false)
        //清除表单内容
        form.resetFields()
        clearData();
    }

    function handleOk() {

        form.submit()

    }

    function clearData() {


        curImgUrl.current = '';
        curImgButtonUrl.current = '';
        setIsUpComplete(false);
        setIsUpButtonComplete(false);
        setMsg_type(0);
        setWeb_type(-1);
        setTend_type(0);
        setTend_Page(0);
        setTendShop(0);
    }

    function handleFinish(values) {

        const { isAllProp} = props;

        if (item) {
            //编辑
            const params: INewPlayerRobotUpdateTypeParams = {
                ...values,
                id: item.id,
                // admin_id: getUid(),
                msg_type: values.msg_type,
                longImage: (values.msg_type == 0) ? values.longImage : ((values.msg_type == 1 && isUpComplete) ? curImgUrl.current : values.longImage),
                buttonImage: (values.msg_type == 0) ? values.buttonImage : ((values.msg_type == 1 && isUpButtonComplete) ? curImgButtonUrl.current : values.buttonImage),

                web_type: values.web_type,

                tend_type: getTendShop() != "1" ? values.tend_type : "1",
                tend_page: getTendShop() != "1" ? values.tend_page : "",
                acTendUrl: getTendShop() != "1" ? (values.acTendUrl == undefined ? "" : values.acTendUrl) : "",

                tend_shop: getTendShop() == "1" ? "1" : "0",
                tab1: getTendShop() == "1" ? values.tab1 : "",
                tab2: getTendShop() == "1" ? values.tab2 : "",
                isAll:isAllProp,

            }

            dispatch({
                type: 'illustratedBook/updateItem',
                payload: params,
            })
        } else {
            const params: INewPlayerRobotInsertParams = {
                ...values,
                msg_type: values.msg_type,
                // longImage: values.msg_type == 0 ? values.longImage : curImgUrl.current,
                // buttonImage: values.msg_type == 0 ? values.buttonImage : curImgButtonUrl.current,
                domainName: AppList.imageOssSH5,

                web_type: values.web_type,
                isAll:isAllProp,
                // tend_type: getTendShop()!="1"?values.tend_type:"1",
                // tend_page: getTendShop()!="1"?values.tend_page:"",
                // acTendUrl: getTendShop()!="1"?(values.acTendUrl==undefined?"":values.acTendUrl):"",

                // tend_shop: getTendShop()=="1"?"1":"0",
                // tab1:  getTendShop()=="1"?values.tab1:"",
                // tab2: getTendShop()=="1"?values.tab2:"",
            }

            dispatch({
                type: 'illustratedBook/insertItem',
                payload: params,
            })
        }
        handleCancel()

    }

    const formItemLayout = {
    };

    const formTailLayout = {
        labelCol: { span: 50 },
        wrapperCol: { span: 50, offset: 5 },
    };
    function render() {

        return (

            <Modal
                title={item ? `编辑链接【ID = ${item.id}】` : '新增链接'}
                visible={visible}
                width={800}
                onCancel={handleCancel}
                onOk={handleOk}
                okText="提交"
                cancelText="取消"
            >
                {item == null ? <Form
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 20 }}
                    form={formRef}
                    name='control-hooks'
                    // initialValues={{ sum: 888 }}
                    onFinish={handleFinish}
                    size='middle'
                >
                    <Form.Item {...formItemLayout} name='msg_type' label="类型" rules={[{ required: true }]}>
                        <Select onChange={handleMsgTypeOptionChange} placeholder="请选择有没有点击事件" allowClear>
                            <Option value={0}>没有点击事件</Option>
                            <Option value={1}>有点击事件</Option>
                        </Select>
                    </Form.Item>
                    <Form.Item {...formItemLayout} name='url_name' label="链接名称" rules={[{ required: true }]}>
                        <Input placeholder="请输入链接名称" />
                    </Form.Item>
                {
                    (form.getFieldValue('msg_type') == 1 || msg_type == 1)
                    &&
                    <Form.Item {...formItemLayout} name='web_type' label="是否是大厅链接" rules={[{ required: true }]}>
                        <Select onChange={handleWebTypeOptionChange} placeholder="请选择否是大厅链接" allowClear>
                            <Option value={0}>不是大厅链接</Option>
                            <Option value={1}>是大厅链接</Option>
                        </Select>
                    </Form.Item>
                }

                </Form> : <Form></Form>}


                {item ? <Form
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 20 }}
                    form={formRef}
                    name='control-hooks'
                    // initialValues={{ sum: 888 }}
                    onFinish={handleFinish}
                    size='middle'
                >

                    <Form.Item {...formItemLayout} name='msg_type' label="类型" rules={[{ required: true }]}>
                        <Select onChange={handleMsgTypeOptionChange} placeholder="请选择有没有点击事件" allowClear>
                            <Option value={0}>没有点击事件</Option>
                            <Option value={1}>有点击事件</Option>
                        </Select>
                    </Form.Item>
                    <Form.Item {...formItemLayout} name='url_name' label="链接名称" rules={[{ required: true }]}>
                        <Input placeholder="请输入链接名称" />
                    </Form.Item>
                    {
                        (form.getFieldValue('msg_type') == 1 || msg_type == 1)
                        &&
                        <Form.Item {...formItemLayout} name='web_type' label="是否是大厅链接" rules={[{ required: true }]}>
                            <Select onChange={handleWebTypeOptionChange} placeholder="请选择否是大厅链接" allowClear>
                                <Option value={0}>不是大厅链接</Option>
                                <Option value={1}>是大厅链接</Option>
                            </Select>
                        </Form.Item>
                    }


                    {
                        <Form.Item {...formItemLayout} label="长图" name="longImage" rules={[{ required: true }]} >
                            {(!isUpComplete && !form.getFieldValue('longImage')) ? (
                                <div>
                                    <Upload
                                        action={'/megauploadOssCN'}
                                        headers={{ 'Access-Control-Allow-Origin': '*', 'X-Requested-With': null }}
                                        withCredentials={true}
                                        data={ossData(name)}
                                        listType="picture-card"
                                        showUploadList={true}
                                        onChange={onUploadIconChange}
                                        beforeUpload={beforeUploadIcon}
                                    >
                                        <div>
                                            <div className="ant-upload-text">点击上传图片（只接受png）</div>
                                        </div>

                                    </Upload>
                                </div>
                            ) : (
                                <div>
                                    <img
                                        style={{ width: 465 / 2., height: 250 / 2. }}
                                        src={AppList.imageOssSH5 + form.getFieldValue('longImage') + `?${Date.now()}`}
                                    />

                                    <Button onClick={onClickDelImg}>删除</Button>
                                </div>
                            )}
                        </Form.Item>
                    }

                    {
                        (!(form.getFieldValue('msg_type') == 0 || msg_type == 0) && (form.getFieldValue('web_type') == 0 || web_type == 0))
                        &&
                        <Form.Item {...formItemLayout} label="按钮图" name="buttonImage" rules={[{ required: true }]} >
                            {(!isUpButtonComplete && !form.getFieldValue('buttonImage')) ? (
                                <div>
                                    <Upload
                                        action={'/megauploadOssCN'}
                                        headers={{ 'Access-Control-Allow-Origin': '*', 'X-Requested-With': null }}
                                        withCredentials={true}
                                        data={ossData(buttonName)}
                                        listType="picture-card"
                                        showUploadList={true}
                                        onChange={onUploadButtonChange}
                                        beforeUpload={beforeUploadIcon}
                                    >
                                        <div>
                                            <div className="ant-upload-text">点击上传图片（只接受png）</div>
                                        </div>

                                    </Upload>
                                </div>
                            ) : (
                                <div>
                                    <img
                                        style={{ width: 465 / 2., height: 250 / 2. }}
                                        src={AppList.imageOssSH5 + form.getFieldValue('buttonImage') + `?${Date.now()}`}
                                    />

                                    <Button onClick={onClickDelButtonImg}>删除</Button>
                                </div>
                            )}
                        </Form.Item>
                    }
                    {/* 上传图片 */}
                    {msg_type == 1 && (<Form.Item {...formTailLayout} valuePropName="checked">
                        {tendShop == 1 && <Checkbox onChange={onShopChange} defaultChecked={true}>勾选跳转商城</Checkbox>}
                        {tendShop != 1 && <Checkbox onChange={onShopChange} defaultChecked={false}>勾选跳转商城</Checkbox>}
                    </Form.Item>
                    )}
                    {msg_type == 1 && tendShop != 1 && (
                        <Form.Item {...formItemLayout} name='tend_type' label="跳转类型" rules={[{ required: true }]}>
                            <Select onChange={handleTypeOptionChange} placeholder="请选择跳转类型" allowClear>
                                <Option value={0}>全部列表</Option>
                                <Option value={1}>app-浏览器</Option>
                            </Select>
                        </Form.Item>
                    )}
                    {msg_type == 1 && tendShop != 1 && tend_type == 0 && (
                        <Form.Item {...formItemLayout} name='tend_page' label="跳转类型详情" rules={[{ required: true }]}>
                            <Select
                                placeholder="请选择跳转类型详情"
                                onChange={handlePageOptionChange}
                                allowClear={true}
                            >
                                {typeList.map((item, index) => (
                                    <Option key={index} value={item.dialog_type}>
                                        {item.dialog_type_name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    )}
                    {msg_type == 1 && tendShop != 1 && (tend_type == 1 || tend_page == 0 || tend_page == 13 || tend_page == 16) && (
                        <Form.Item {...formItemLayout} name='acTendUrl' label="网页url" rules={[{ required: true }]}>
                            <Input.TextArea placeholder="请输入网页url" />
                        </Form.Item>
                    )}
                    {msg_type == 1 && tendShop == 1 && (
                        <Form.Item {...formItemLayout} name='tab1' label="tab1" rules={[{ required: true }]}>
                            <Input.TextArea placeholder="请输入tab1" />
                        </Form.Item>
                    )}
                    {msg_type == 1 && tendShop == 1 && (
                        <Form.Item {...formItemLayout} name='tab2' label="tab2" rules={[{ required: true }]}>
                            <Input.TextArea placeholder="请输入tab2" />
                        </Form.Item>
                    )}
                </Form> : <></>}


            </Modal>
        )
    }

    return render()
}

const mapStateToProps =
    ({
        loading,
        login,
        illustratedBook,
        bannerStore,

        broadcastSend,
        illustratedBookbannerStore,
        global

    }: {
        loading: IdvaLoading;
        login: ILogin;
        illustratedBook: INewPlayerRobotModel;


        broadcastSend: any;
        bannerStore: any;
        global: any

    }) => ({
        isLoading: loading.models.illustratedBook,
        uid: login.uid,

        // isLoading: loading.models.broadcastSend,
        broadcastList: broadcastSend.broadcastList,
        items: illustratedBook.items,
        typeList: bannerStore.typeList
    });



export default connect(mapStateToProps)(LongImageRobotModal);

