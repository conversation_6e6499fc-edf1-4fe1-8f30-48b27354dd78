export interface IBroadcastRedPacketItem {
    id: number,
    admin_id: number,
    admin_nickName: string,
    sum: number,
    name: number,
    desc: number,
    create_time: string,
    send_time: string,
    type: number, //'0：等待广播  1：已经取消 2：已经广播 3：发送中'
}

export interface IBroadcastRedPacketInsertParams {
    admin_id: number,
    sum: number,
    name: string,
    desc: string,
    send_time: string,
}

export interface IBroadcastRedPacketUpdateParams extends IBroadcastRedPacketInsertParams {
    id: number,
    type?: number,
}

export interface IBroadcastRedPacketUpdateTypeParams {
    id: number,
    admin_id: number,
    type: number,
}