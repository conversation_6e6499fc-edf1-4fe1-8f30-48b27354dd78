import React, { RefObject, Component } from 'react';
import { Modal, Form, Input, Select, DatePicker, Spin, Switch, message } from 'antd';
import RichTxtEdit from './RichTxtEdit';
import * as styles from './CreateModal.less'

const { Option } = Select;
const { TextArea } = Input;

interface ICreateModalProps {
    dispatch: Function;
    loading: boolean;

}
class CreateModal extends Component<ICreateModalProps, any> {

    private formRef: RefObject<Form> = React.createRef();
    private modalTitle = '新建公告';
    private okText = '新建';
    private initTitle = '人狼デスマッチ';
    private initContent = '';
    private okText = '新建';

    constructor(props) {
        super(props);
        console.log("this.props=>", this.props)
        if (this.props.isEditeModal) {
            this.modalTitle = '编辑公告';
            this.initTitle = props.notice.title;
            this.initContent = props.notice.content;
            this.okText = '编辑';
            // this.is_show = props.news.is_show;
            // this.news_tag = props.news.news_tag;
            // this.is_new = props.news.is_new;
            // this.url = props.news.url;

        }
    }
    render() {
        const { onHandleClose, isEditeModal, news } = this.props;

        const defaultHtml = "";
        const draftKey = "create";

        return (
            <Modal
                visible={true}
                title={this.modalTitle}
                onOk={this.onClickOk}

                okText={this.okText}
                onCancel={onHandleClose}
                cancelText="取消"
                width={1640}
            >
                <Spin spinning={false}>
                    <div className={styles.createModal}>

                        <div className={styles.news_form}>

                            <Form
                                ref={this.formRef}
                                labelCol={{ span: 6 }}
                                wrapperCol={{ span: 16 }}
                                layout="horizontal"
                                name="validate_other"
                                initialValues={{
                                    title: this.initTitle,
                                    content: this.initContent,
                                    // is_show: this.is_show,
                                    // is_new: this.is_new,
                                    // news_tag: this.news_tag,
                                    // url: this.url
                                }}
                            >
                                <Form.Item
                                    name="title"
                                    label="标题"
                                    rules={[{ required: true, message: '请输入新公告的标题', type: 'string', min: 6 }]}
                                >
                                    <Input />
                                </Form.Item>
                                <Form.Item
                                    name="content"
                                    label="公告内容"
                                    rules={[{ required: true, message: '请输入公告的内容', type: 'string', min: 8 }]}
                                >
                                    <TextArea rows={20} />
                                </Form.Item>
                            </Form>
                        </div>
                        <div className={styles.news_edit}>

                            <RichTxtEdit defaultValue={defaultHtml} draftKey={draftKey} onGetHtml={this.onGetHtml} />
                        </div>
                    </div>
                </Spin>
            </Modal>
        )
    }
    onClickOk = () => {
        this.formRef.current.validateFields().then(values => {
            console.log("fasong", values.url)
        }).catch(info => {
            console.error(info);
        });
    }
    onGetHtml = (html: string) => {
        this.formRef.current.setFieldsValue({
            content: html
        });
        message.success("提取文本成功");
    }
}


export default CreateModal