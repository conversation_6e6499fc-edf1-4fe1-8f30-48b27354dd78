/*
 * @Description: 监控字典表管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2022-10-20 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-10-20 15:19:21
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  Row,
  Col,
  Radio,
  Image,
  Table,
  Tag,
  Typography,
  Modal,
  Input,
  Popconfirm,
  AutoComplete,
  Space,
} from 'antd';
const { TextArea } = Input;
const { Text } = Typography;
import { PlusOutlined, CloudUploadOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import * as styles from './ServerMonitorDictManager.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import AppList from '../../../pages/AppList';
import { ColumnProps } from 'antd/lib/table';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName, getOptionList, getSelectName } from '@/utils/mallTool';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { checkNotNull } from '@/utils/emptyTool';
import {
  dispatchDictList,
  monitorDicApiList,
  monitorDicEnum,
  monitorDicList,
  monitorDicTabList,
} from './models/apiServerMonitor';
import ServerMonitorDictEditorModal from './ServerMonitorDictEditorModal';
export const PAGESIZE = 8;
@connect(
  ({
    loading,
    login,
    apiServerMonitor,
  }: {
    loading: IdvaLoading;
    login: ILogin;
    apiServerMonitor: any;
  }) => ({
    uid: login.uid,
    isLoading: loading.models['apiServerMonitor'],
    list: apiServerMonitor.list,
    typeList: apiServerMonitor.typeList,
    userList: apiServerMonitor.userList,
    businessList: apiServerMonitor.businessList,
    serverList: apiServerMonitor.serverList,
    envList: apiServerMonitor.envList,
    formatList: apiServerMonitor.formatList,
    systemList: apiServerMonitor.systemList,
  })
)
export default class ServerMonitorDictManager extends Component<any, any> {
  constructor(props) {
    super(props);

    this.state = {
      columns: this.makeColumns(monitorDicTabList[0].key),
      tabKey: monitorDicTabList[0].key,

      currentPage: 1,
      currentData: null,
      dataModelType: -1,
    };

    dispatchDictList(this.props.dispatch, monitorDicTabList[0].key);
  }

  render() {
    const { isLoading } = this.props;
    return (
      <Spin spinning={!!isLoading}>
        <PageHeaderWrapper tabList={monitorDicTabList} onTabChange={this.handleTabChange}>
          {this.renderGift()}
        </PageHeaderWrapper>
      </Spin>
    );
  }

  renderGift() {
    const { dispatch, isLoading } = this.props;
    const { dataModelType, tabKey, currentData } = this.state;
    return (
      <>
        <Spin spinning={!!this.props.isLoading}>
          {dataModelType > 0 && (
            <ServerMonitorDictEditorModal
              current={currentData}
              dataModelType={dataModelType}
              onClickClose={this.onClickClose}
              refreshView={() => dispatchDictList(dispatch, dataModelType)}
            />
          )}
        </Spin>
        <Card>
          <Space direction="horizontal">
            <Button type="primary" onClick={() => this.onClickCreate()}>
              {`新增${getCateName(tabKey, monitorDicList)}`}
            </Button>
          </Space>
          <Table
            style={{ marginTop: 10 }}
            columns={this.state.columns}
            dataSource={this.getDictDataList(tabKey)}
            loading={!!isLoading}
            bordered={true}
            rowKey={(record, index) => index.toString()}
            pagination={{
              pageSize: PAGESIZE,
              current: this.state.currentPage,
              total: this.getDictDataList(tabKey).length,
            }}
          />
        </Card>
      </>
    );
  }

  makeColumns(type: any) {
    const columns: ColumnProps<any>[] = [
      {
        title: 'ID',
        key: 'id',
        dataIndex: 'id',
        width: 70,
        align: 'center',
      },
      {
        title: `${getCateName(type, monitorDicList)}名称`,
        dataIndex: 'name',
        key: 'name',
        align: 'center',
      },
      // {
      //   title: '状态',
      //   dataIndex: 'delsign',
      //   align: 'center',
      //   width: 70,
      //   render: val => {
      //     return <div>{val == 1 ? '删除' : '正常'}</div>;
      //   },
      // },
      {
        title: '操作',
        width: 220,
        dataIndex: 'delsign',
        align: 'center',
        render: (val, record, index) => {
          return (
            <div>
              <Button type="primary" onClick={() => this.onClickEdit(record)}>
                编辑
              </Button>
              <Popconfirm
                title={`确定要删除吗?`}
                onConfirm={() => this.deleteAction(record)}
                okText="Yes"
                cancelText="No"
              >
                <Button
                  danger
                  style={{ marginLeft: 10 }}
                  type="primary"
                >
                  删除
                </Button>
              </Popconfirm>
            </div>
          );
        },
      },
    ];

    const serverColumn: ColumnProps<any>[] = [
      {
        title: '外网ip',
        key: 'outer_ip',
        dataIndex: 'outer_ip',
        align: 'center',
      },
      {
        title: '内网ip',
        key: 'inner_ip',
        dataIndex: 'inner_ip',
        align: 'center',
      },
      {
        title: '操作系统',
        key: 'system_name',
        dataIndex: 'system_name',
        align: 'center',
      },
    ];

    const formatColumn: ColumnProps<any>[] = [
      {
        title: '时间format',
        key: 'time_format',
        dataIndex: 'time_format',
        align: 'center',
      },
      {
        title: '时间正则',
        key: 'time_pattern',
        dataIndex: 'time_pattern',
        align: 'center',
      },
    ];

    if (type == monitorDicEnum.Server) {
      columns.splice(2, 0, ...serverColumn);
    } else if (type == monitorDicEnum.Format) {
      columns.splice(2, 0, ...formatColumn);
    }

    return columns;
  }

  getDictDataList(type) {
    switch (type) {
      case monitorDicEnum.Type:
        return this.props.typeList;
      case monitorDicEnum.User:
        return this.props.userList;
      case monitorDicEnum.Business:
        return this.props.businessList;
      case monitorDicEnum.Server:
        return this.props.serverList;
      case monitorDicEnum.Env:
        return this.props.envList;
      case monitorDicEnum.Format:
        return this.props.formatList;
      case monitorDicEnum.System:
        return this.props.systemList;
      default:
        return [];
    }
  }

  onClickCreate = () => {
    const { tabKey } = this.state;
    this.setState({ dataModelType: tabKey, currentData: null });
  };

  onClickEdit = data => {
    const { tabKey } = this.state;
    this.setState({ dataModelType: tabKey, currentData: data });
  };

  deleteAction = (record) => {
    const { dispatch } = this.props;
    const { tabKey } = this.state;

    const request: any = {
      id: record.id,
    };

    console.log('request', request);

    dispatch({
      type: `apiServerMonitor/delete${getCateName(tabKey, monitorDicApiList)}`,
      payload: request,
    }).then(() => {
      dispatchDictList(dispatch, tabKey);
    });
  };

  handleTabChange = value => {
    const { uid, dispatch } = this.props;
    const key = Number(value);
    dispatchDictList(dispatch, key);
    this.setState({ currentPage: 1, tabKey: key, columns: this.makeColumns(key) });
  };

  onClickClose = () => {
    this.setState({
      dataModelType: -1,
      currentData: null,
    });
  };
}
