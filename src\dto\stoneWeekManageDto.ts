
export interface IStoneWeekAwardItem {
    id: number,
    item_dic_id: number,
    item_cate_id: number,
    item_dic_name: string,
    num: number,
    max: number,
    stock_num: number,
    rank: number,
    mining_frame_level: number,
    isFrame: number,
    sort: number,
    delsign: number,
    remark: string,
}

export interface IAddStoneWeekAwardParams {
    item_dic_id: number,
    item_cate_id: number,
    num: number,
    max: number,
    stock_num?: number,
    rank: number,
    mining_frame_level?: number,
    isFrame?: number,
    sort: number,
    delsign: number,
    remark: string,
}

export interface IUpdateStoneWeekAwardParams extends IAddStoneWeekAwardParams {
    id: number,
}