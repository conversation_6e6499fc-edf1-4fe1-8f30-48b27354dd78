/*
 * @Description: 违规刷分，对局详情
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-05
 * @LastEditors: 赵宝强
 * @LastEditTime:
 */
import React, { Component } from 'react';
import { Button, Checkbox, DatePicker, Form, Input, Modal, Select } from 'antd';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import { IMainPageBanner, MerchantBanner } from '@/pages/AppScript/Banner/models/mainPageBanner';
import moment from 'moment';

const CheckboxGroup = Checkbox.Group;
const { Option } = Select;

export interface BannerEditPageState {
  selectType: number,
}
export interface BannerEditPageProps {
  isNewBannerModal: boolean,
  merchantBanner: MerchantBanner,
}

@connect(({ loading, login, mainPageBanner }: { loading: IdvaLoading; login: ILogin; mainPageBanner: IMainPageBanner; }) => ({
  isLoading: loading.models['mainPageBanner'],
  isNewBannerModal: mainPageBanner.isNewBannerModal,
  merchantBanner: mainPageBanner.merchantBanner,

}))

export default class BannerEditPage extends Component<BannerEditPageProps, BannerEditPageState> {
  constructor(props) {
    super(props);
    this.state = {
      selectType: this.props.merchantBanner.type,

    };

  }

  render (){
    const{ isNewBannerModal, merchantBanner, dispatch} = this.props;
    const { selectType } = this.state;
    const layout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
    };
    const tailLayout = {
      wrapperCol: { offset: 8, span: 16 },
    };
    const { RangePicker } = DatePicker;
    const selectBefore = (
      <Select defaultValue="http://" className="select-before">
        <Option value="https://">http://</Option>
        <Option value="http://">https://</Option>
      </Select>
    );
    const onFinish = (values) => {
      const value = {
        id: Number(merchantBanner.id),
        banner_url: merchantBanner.banner_url,
        sort_id: Number(values.sort_id),
        banner_name: values.banner_name,
        href_url: values.href_url,
        type: values.type,
        banner_starttime: values.time[0].format('YYYY-MM-DD HH:mm:ss'),
        banner_endtime: values.time[1].format('YYYY-MM-DD HH:mm:ss')

      };
      console.log(value)
      dispatch({
        type: 'mainPageBanner/updateBannerInfo',
        payload: value
      });
      // this.formRef.current.resetFields();
    };
    return (
      <Modal
        closable={true}
        maskClosable={false}
        centered={true}
        title={'序号'+merchantBanner.sort_id+'     banner信息修改'}
        onCancel={this.onCloseModel}
        footer={[]}
        visible={isNewBannerModal}
        width='800px'
      >
        <Form
          {...layout}
          name="basic"
          initialValues={{ remember: true }}
          onFinish={onFinish}
        >
          <Form.Item
            label="序号(不可重复)"
            name={'sort_id'}
            initialValue={merchantBanner.sort_id}
            rules={[{ required: false}]}

          >
            <Input />
          </Form.Item>

          <Form.Item
            label="banner名称"
            name={'banner_name'}
            initialValue={merchantBanner.banner_name}
            rules={[{ required: false}]}
          >
            <Input />
          </Form.Item>
          <Form.Item name={'type'} label="跳转类型" rules={[{ required: false }]} initialValue={merchantBanner.type}>
            <Select onChange={this.handleTypeOptionChange}>
              <Option value={1}>店铺</Option>
              <Option value={2}>剧本</Option>
              <Option value={3}>图片URL跳转</Option>
              <Option value={4}>文章</Option>
              <Option value={5}>订单</Option>
            </Select>
          </Form.Item>

            <Form.Item name={'href_url'} label="跳转地址" rules={[{ required: false }]} initialValue={merchantBanner.href_url}>
                <Input />
            </Form.Item>
          <Form.Item name="time" label="开始时间" rules={[{ required: false }]} initialValue={[moment(merchantBanner.banner_starttime), moment(merchantBanner.banner_endtime)]}>
            <RangePicker showTime={true} format="YYYY-MM-DD HH:mm:ss"/>
          </Form.Item>
          <Form.Item {...tailLayout}>
            <Button type="primary" htmlType="submit">
              提交
            </Button>

          </Form.Item>
        </Form>
      </Modal>
    );

  }



  //下拉框选择
  handleTypeOptionChange = (e) => {
    console.log(e);
    this.setState({
      selectType: e
    });
  };

  handlePageOptionChange = (e) => {
    console.log(e);
    this.setState({
      selectPage: e
    });
  };

  //关闭模态页
  onCloseModel = () => {
    // @ts-ignore
    const { dispatch } = this.props;
    dispatch({
      type: 'mainPageBanner/setIsNewBannerModal',
      payload:
        false
    });

  };



}



