/*
 * @Description: 狼人杀�?�?
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-01-31 11:01:15
 * @LastEditors: yunpeng.li <EMAIL>
 * @LastEditTime: 2024-08-26 11:41:49
 */
import React from 'react';
import { AccessRouteId } from './accessRouteCof';

//import deductionRecord from '@/pages/AppWolf/ScoreViolation/models/deductionRecord';

const AppWolfRoutes = {
  path: 'appWolf',
  name: 'appWolf',
  icon: 'http://p15.qhimg.com/t013761aef011b16c43.png',
  Routes: ['src/layouts/Authorized'],
  authority: AccessRouteId.app_wolf,
  routes: [
    // PlayerTreasure
    {
      path: 'playerTreasure',
      icon: 'pay-circle',
      name: 'playerTreasure',
      authority: [AccessRouteId.wolf_treasure_all, AccessRouteId.wolf_treasure_detail],
      routes: [
        {
          authority: AccessRouteId.wolf_treasure_all,
          path: '/appWolf/playerTreasure/all',
          name: 'all',
          component: './AppWolf/PlayerTreasure',
        },
        // { path: '/playerTreasure/bag', name: 'bag', component: './AppWolf/PlayerTreasure/Bag' },
        {
          path: '/appWolf/playerTreasure/record',
          name: 'record',
          authority: AccessRouteId.wolf_treasure_detail,
          component: './AppWolf/PlayerTreasure/Record',
        },
      ],
    },

    // PlayerStatus
    {
      path: 'playerStatus',
      icon: 'build',
      name: 'playerStatus',
      authority: [
        AccessRouteId.wolf_member_change,
        AccessRouteId.wolf_member_change_confirm,
        AccessRouteId.wolf_status_query,
        AccessRouteId.wolf_status_update,
        AccessRouteId.wolf_clear_escape,
        AccessRouteId.wolf_credit_manager,
        AccessRouteId.wolf_credit_statistics,
        AccessRouteId.wolf_idCard_query,
        AccessRouteId.wolf_ban_danmu,
        AccessRouteId.wolf_lock_account,
        AccessRouteId.wolf_collect_room,
      ],
      routes: [
        {
          path: '/appWolf/playerStatus/memberChange',
          name: 'memberChange',
          authority: AccessRouteId.wolf_member_change,
          component: './AppWolf//PlayerStatus/MemberChangeManager',
        },
        {
          path: '/appWolf/playerStatus/memberChangeConfirm',
          name: 'memberChangeConfirm',
          authority: AccessRouteId.wolf_member_change_confirm,
          component: './AppWolf//PlayerStatus/MemberChangeConfirmManager',
        },
        {
          path: '/appWolf/playerStatus/lockAccount',
          name: 'lockAccount',
          // authority: AccessRouteId.wolf_lock_account,
          component: './AppWolf//PlayerStatus/LockAccount',
        },
        {
          path: '/appWolf/playerStatus/entertainment',
          name: 'entertainment',
          // authority: AccessRouteId.wolf_lock_account,
          component: './AppWolf/PlayerStatus/Entertainment/entertainment',
        },
        {
          path: '/appWolf/playerStatus/queryStatus',
          name: 'queryStatus',
          authority: AccessRouteId.wolf_status_query,
          component: './AppWolf//PlayerStatus/QueryStatus',
        },
        {
          path: '/appWolf/playerStatus/updateStatus',
          name: 'updateStatus',
          authority: AccessRouteId.wolf_status_update,
          component: './AppWolf/PlayerStatus/UpdateStatus',
        },
        {
          path: '/appWolf/playerStatus/clearEscape',
          name: 'clearEscape',
          authority: AccessRouteId.wolf_clear_escape,
          component: './AppWolf/PlayerStatus/ClearEscape',
        },
        {
          path: '/appWolf/playerStatus/creditManager',
          name: 'credit',
          authority: AccessRouteId.wolf_credit_manager,
          component: './AppWolf/PlayerStatus/CreditManager',
        },
        {
          path: '/appWolf/playerStatus/CreditStatistics',
          name: 'creditStatistics',
          authority: AccessRouteId.wolf_credit_statistics,
          component: './AppWolf/PlayerStatus/CreditStatistics',
        },
        {
          path: '/appWolf/playerStatus/udidQuery',
          name: 'udidQuery',
          authority: AccessRouteId.wolf_udid_uery,
          component: './AppWolf/PlayerStatus/UdidQuery',
        },
        {
          path: '/appWolf/playerStatus/identidication',
          name: 'identification',
          authority: AccessRouteId.wolf_identification,
          component: './AppWolf/PlayerStatus/Identification',
        },
        {
          path: '/appWolf/playerStatus/idCardStatus',
          name: 'idCardStatus',
          authority: AccessRouteId.wolf_idCard_query,
          component: './AppWolf/PlayerStatus/IdCardStatus',
        },
        {
          path: '/appWolf/playerStatus/banDanMu',
          name: 'banDanMu',
          authority: AccessRouteId.wolf_ban_danmu,
          component: './AppWolf/PlayerStatus/BanDanMu',
        },
        {
          path: '/appWolf/playerStatus/aliUmid',
          name: 'aliUmid',
          authority: AccessRouteId.wolf_ban_danmu,
          component: './AppWolf/PlayerStatus/AliUmidList',
        },
        {
          path: '/appWolf/playerStatus/arenaArea',
          name: 'arenaArea',
          //  authority: AccessRouteId.wolf_arena,
          component: './AppWolf/PlayerStatus/ArenaArea',
        },
        {
          path: '/appWolf/playerStatus/arenaRecord',
          name: 'arenaRecord',
          //  authority: AccessRouteId.wolf_arena,
          component: './AppWolf/PlayerStatus/ArenaRecord',
        },
        {
          path: '/appWolf/playerStatus/UserCollection',
          name: 'UserCollection',
          authority: AccessRouteId.wolf_collect_room,
          component: './AppWolf/PlayerStatus/UserCollection',
        },
      ],
    },
    {
      path: 'scoreViolation',
      icon: 'table',
      name: 'scoreViolation',
      authority: [
        AccessRouteId.wolf_group_controller,
        AccessRouteId.wolf_group_blacklist,
        AccessRouteId.wolf_group_abnormal,
        AccessRouteId.wolf_group_udidInquire,
        AccessRouteId.wolf_group_scoreGroup,
      ],
      routes: [
        {
          path: '/appWolf/scoreViolation/deductionRecord', //用户扣分记录
          name: 'deductionRecord',
          authority: AccessRouteId.wolf_group_controller,
          component: './AppWolf/ScoreViolation/DeductionRecord',
        },
        {
          path: '/appWolf/scoreViolation/accountBlacklist', //小号黑名单查�?
          name: 'accountBlacklist',
          authority: AccessRouteId.wolf_group_blacklist,
          component: './AppWolf/ScoreViolation/AccountBlacklist',
        },
        {
          path: '/appWolf/scoreViolation/accountAbnormal', //账户异常查�??
          name: 'accountAbnormal',
          authority: AccessRouteId.wolf_group_abnormal,
          component: './AppWolf/ScoreViolation/AccountAbnormal',
        },
        {
          path: '/appWolf/scoreViolation/udidInquire', //udid查�??
          name: 'udidInquire',
          authority: AccessRouteId.wolf_group_udidInquire,
          component: './AppWolf/ScoreViolation/UdidInquire',
        },
        {
          path: '/appWolf/scoreViolation/scoreGroup',
          name: 'scoreGroup',
          authority: AccessRouteId.wolf_group_scoreGroup,
          component: './AppWolf/ScoreViolation/ScoreGroup',
        },
      ],
    },
    // PlayerGameRecord
    {
      path: 'gameRecord',
      icon: 'table',
      name: 'gameRecord',
      authority: [
        AccessRouteId.wolf_record_game,
        AccessRouteId.wolf_record_im,
        AccessRouteId.wolf_record_broardcast,
      ],
      routes: [
        {
          path: '/appWolf/gameRecord/list',
          name: 'list',
          authority: AccessRouteId.wolf_record_game,
          component: './AppWolf/GameRecord/GameList',
        },
        {
          path: '/appWolf/gameRecord/talkHistory',
          authority: AccessRouteId.wolf_record_im,
          name: 'talkHistory',
          component: './AppWolf/GameRecord/TalkHistory',
        },
        {
          path: '/appWolf/gameRecord/broadcast',
          authority: AccessRouteId.wolf_record_broardcast,
          name: 'broadcast',
          component: './AppWolf/GameRecord/Broadcast',
        },
        {
          path: '/appWolf/gameRecord/tnotice',
          authority: AccessRouteId.wolf_record_tnotice,
          name: 'tnotice',
          component: './AppWolf/GameRecord/Tnotice',
        },
      ],
    },
    // app管理
    {
      path: 'appManager',
      icon: 'appstore',
      name: 'appManager',
      authority: [
        AccessRouteId.wolf_ad,
        AccessRouteId.wolf_ad,
        AccessRouteId.wolf_avatar_frame,
        AccessRouteId.wolf_achievement,
        AccessRouteId.wolf_redbag,
        AccessRouteId.wolf_email,
        AccessRouteId.wolf_push,
        AccessRouteId.wolf_splash,
        AccessRouteId.wolf_props_manager,
        AccessRouteId.wolf_box_manager,
        AccessRouteId.wolf_2w_avatar,
        AccessRouteId.wolf_home_dialog,
        AccessRouteId.wolf_announce,
        AccessRouteId.wolf_leaderboard_reward_settings,
        AccessRouteId.wolf_group_versus,
        AccessRouteId.wolf_game_area,
        AccessRouteId.wolf_noble,
      ],
      routes: [
        {
          path: '/appWolf/appManager/newPlayerRobot',
          name: 'newPlayerRobot',
          authority: AccessRouteId.wolf_ad,
          component: './AppWolf/AppManager/newPlayerRobot',
        },
        {
          path: '/appWolf/appManager/longImage', //生成长图链接
          name: 'longImage',
          authority: AccessRouteId.wolf_ad,
          component: './AppWolf/AppManager/illustratedBook',
        },
        {
          path: '/appWolf/appManager/SSFrameTime', //ss头像框改时间
          name: 'SSFrameTime',
          authority: AccessRouteId.wolf_ad,
          component: './AppWolf/AppManager/SSFrameTime',
        },
        {
          path: '/appWolf/appManager/sweetnessGift', //甜蜜赠礼
          name: 'sweetnessGift',
          authority: AccessRouteId.wolf_ad,
          component: './AppWolf/AppManager/sweetnessGift',
        },
        {
          path: '/appWolf/appManager/MessageBoard', //留言板
          name: 'MessageBoard',
          authority: AccessRouteId.wolf_ad,
          component: './AppWolf/AppManager/MessageBoard',
        },
        {
          path: '/appWolf/appManager/advertising',
          name: 'advertising',
          authority: AccessRouteId.wolf_ad,
          component: './AppWolf/AppManager/Advertising',
        },
        {
          path: '/appWolf/appManager/hallPopups',
          name: 'hallPopups',
          authority: AccessRouteId.wolf_ad,
          component: './AppWolf/AppManager/HallPopups',
        },
        {
          path: '/appWolf/appManager/avatarframe',
          name: 'avatarframe',
          authority: AccessRouteId.wolf_avatar_frame,
          component: './AppWolf/AppManager/AvatarFrame',
        },
        {
          path: '/appWolf/appManager/achievement',
          name: 'achievement',
          authority: AccessRouteId.wolf_achievement,
          component: './AppWolf/AppManager/Achievement',
        },
        // {
        //   path: '/appWolf/appManager/redbag',
        //   name: 'redbag',
        //   authority: AccessRouteId.wolf_redbag,
        //   component: './AppWolf/AppManager/RedBagAty',
        // },
        {
          path: '/appWolf/appManager/wordActivity',
          name: 'redbag',
          authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/AppManager/WordActivityManager/WordActivityManager',
        },
        {
          path: '/appWolf/appManager/broadcastRedPacket',
          name: 'broadcastRedPacket',
          authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/BroadcastRedPacket/BroadcastRedPacket',
        },
        {
          path: '/appWolf/appManager/sendmail',
          name: 'sendmail',
          authority: AccessRouteId.wolf_email,
          component: './AppWolf/AppManager/SendMail',
        },
        {
          path: '/appWolf/appManager/push',
          authority: AccessRouteId.wolf_push,
          name: 'push',
          // authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/PushCreate/PushList',
        },
        {
          path: '/appWolf/appManager/openAdConfig',
          name: 'openAdConfig',
          authority: AccessRouteId.wolf_splash,
          // authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/AppManager/OpenAdConfig',
        },
        {
          path: '/appWolf/appManager/simulator',
          name: 'simulator',
          authority: AccessRouteId.wolf_simulator,
          // authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/Simulator/Simulator',
        },
        // {
        //   path: '/appWolf/appManager/score',
        //   name: 'score',
        //   authority: AccessRouteId.wolf_props_manager,
        //   // authority: AccessRouteId.wolf_redbag,
        //   component: './AppWolf/Score/Score',
        // },
        {
          path: '/appWolf/appManager/NewBannerControl',
          name: 'NewBannerControl',
          authority: AccessRouteId.wolf_home_dialog,
          component: './AppWolf/AppManager/NewBannerControl',
        },
        {
          path: '/appWolf/appManager/NewBannerControlV2',
          name: 'NewBannerControlV2',
          authority: AccessRouteId.wolf_anchor_banner_control_V2,
          component: './AppWolf/NewBannerControlV2/NewBannerControl',
        },
        {
          path: '/appWolf/appManager/homeDialog',
          name: 'homeDialog',
          authority: AccessRouteId.wolf_home_dialog,
          component: './AppWolf/HomeDialog/HomeDIalogListNew',
        },
        {
          path: '/appWolf/appManager/systemNotice', //  系统 �?�?
          name: 'systemNotice',
          authority: AccessRouteId.wolf_announce,
          // authority: ,
          component: './AppWolf/SystemNotice/SystemNoticeList',
        },
        {
          path: '/appWolf/appManager/leaderboardRewardSettings', //  排�?��?��?�励设置
          name: 'leaderboardRewardSettings',
          authority: AccessRouteId.wolf_leaderboard_reward_settings,
          // authority: ,
          component: './AppWolf/AppManager/LeaderboardRewardSettings',
        },
        {
          path: '/appWolf/appManager/groupVersus', //  �?会战
          name: 'groupVersus',
          authority: AccessRouteId.wolf_group_versus,
          // authority: ,
          component: './AppWolf/AppManager/GroupVersus',
        },
        {
          path: '/appWolf/appManager/updateContent', // 版本更新弹窗h5编辑后台
          name: 'updateContent',
          authority: AccessRouteId.wolf_tsys_bvrs,
          // authority: ,
          component: './AppWolf/UpdateContent/UpdateContentList',
        },
        {
          path: '/appWolf/appManager/anchorBanner', //  主播板块
          name: 'anchorBanner',
          authority: AccessRouteId.wolf_anchor_banner_control,
          component: './AppWolf/AnchorBanner/AnchorBannerList',
        },
        {
          path: '/appWolf/appManager/gameAreaManager', //  比赛�?
          name: 'gameArea',
          authority: AccessRouteId.wolf_game_area,
          component: './AppWolf/GameArea/GameActivityOverviewManager',
        },
        {
          path: '/appWolf/appManager/noble',
          name: 'noble',
          authority: AccessRouteId.wolf_noble,
          component: './AppWolf/NobleManager/NobleManager',
        },
        {
          path: '/appWolf/appManager/broadcast',
          name: 'broadcast',
          authority: AccessRouteId.wolf_broadcast,
          component: './AppWolf/Broadcast/BroadcastManager',
        },
        {
          path: '/appWolf/appManager/enterRoom',
          name: 'enterRoom',
          // authority: AccessRouteId.wolf_aty_talent,
          component: './AppWolf/EnterRoom/EnterRoom',
        },
        {
          path: '/appWolf/appManager/userAdvice',
          name: 'userAdvice',
          authority: AccessRouteId.app_wolf,
          component: './AppWolf/UserAdvice/userAdvice',
        },
        {
          path: '/appWolf/appManager/userMood',
          name: 'userMood',
          authority: AccessRouteId.app_wolf,
          component: './AppWolf/UserMood/userMood',
        },
        {
          path: '/appWolf/appManager/noviceGroup',
          name: 'noviceGroup',
          authority: AccessRouteId.app_wolf,
          component: './AppWolf/NoviceGroup/noviceGroup',
        },
        {
          path: '/appWolf/appManager/removePwdLimit',
          name: 'removePwdLimit',
          authority: AccessRouteId.app_wolf,
          component: './AppWolf/User/RemovePwdLimit',
        },
      ],
    },
    // 商城管理
    {
      path: 'mallManager',
      icon: 'bank',
      name: 'mallManager',
      authority: [
        AccessRouteId.wolf_mall,
        AccessRouteId.wolf_mall_item,
        AccessRouteId.wolf_mall_item_dic,
        AccessRouteId.wolf_props_manager,
        AccessRouteId.wolf_gift_bag_manager,
        AccessRouteId.wolf_box_manager,
        AccessRouteId.wolf_2w_avatar,
        AccessRouteId.wolf_props_manager,
        AccessRouteId.wolf_group_props,
      ],
      routes: [
        {
          path: '/appWolf/mallManager/mallItem',
          name: 'item',
          authority: AccessRouteId.wolf_mall_item,
          component: './AppWolf/MallManager/MallItemManager',
        },
        {
          path: '/appWolf/mallManager/mallBox',
          name: 'box',
          authority: AccessRouteId.wolf_mall_item,
          component: './AppWolf/MallManager/MallItemBoxManager',
        },
        {
          path: '/appWolf/mallManager/itemDic',
          name: 'itemDic',
          authority: AccessRouteId.wolf_mall_item_dic,
          component: './AppWolf/MallManager/ItemDicManager',
        },
        {
          path: '/appWolf/mallManager/propsManager',
          name: 'propsManager',
          authority: AccessRouteId.wolf_props_manager,
          // authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/PropsManager/PropsManager',
        },
        // {
        //   path: '/appWolf/mallManager/openManager',
        //   name: 'openManager',
        //   authority: AccessRouteId.wolf_box_manager,
        //   // authority: AccessRouteId.wolf_redbag,
        //   component: './AppWolf/GameConfig/OpenManager',
        // },
        {
          path: '/appWolf/mallManager/lotteryBox',
          name: 'lotteryBox',
          authority: AccessRouteId.wolf_box_manager,
          component: './AppWolf/LotteryBox/LotteryBoxOverviewManager',
        },
        // {
        //   path: '/appWolf/mallManager/avatarframePeriod',
        //   name: 'avatarframePeriod',
        //   authority: AccessRouteId.wolf_2w_avatar,
        //   // authority: AccessRouteId.wolf_redbag,
        //   component: './AppWolf/AvatarframePeriod/AvatarframePeriod',
        // },
        {
          path: '/appWolf/mallManager/giftBag',
          name: 'giftBagManager',
          authority: AccessRouteId.wolf_gift_bag_manager,
          // authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/GiftBag/GiftBagManager',
        },
        {
          path: '/appWolf/mallManager/groupPropsManager',
          name: 'groupPropsManager',
          authority: AccessRouteId.wolf_group_props,
          // authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/GroupPropsManager/GroupPropsManager',
        },
        {
          path: '/appWolf/mallManager/teamGiftBag',
          name: 'teamGiftBag',
          authority: AccessRouteId.wolf_group_props,
          component: './AppWolf/TeamGiftBag/TeamGiftBagManager',
        },
        {
          path: '/appWolf/mallManager/giftItems',
          name: 'giftItem',
          authority: AccessRouteId.wolf_group_props,
          component: './AppWolf/GiftItemList/GiftItemList',
        },
        {
          path: '/appWolf/mallManager/teamWeekGiftBag',
          name: 'teamWeekGiftBag',
          authority: AccessRouteId.wolf_group_props,
          component: './AppWolf/TeamWeekGiftBag/TeamWeekGiftBagList',
        },
      ],
    },
    // 2w框�?�理
    {
      path: '2W',
      icon: 'AppstoreAdd',
      name: '2W_Frame',
      authority: [AccessRouteId.wolf_2w_avatar],
      routes: [
        {
          path: '/appWolf/2W/avatarframePeriodv2',
          name: 'avatarframePeriodv2',
          authority: AccessRouteId.wolf_2w_avatar,
          // authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/AvatarframePeriodv2/AvatarframePeriodv2',
        },
        {
          path: '/appWolf/2W/2W_Coin',
          name: 'avatarframePeriodv2_manger',
          authority: AccessRouteId.wolf_2w_avatar,
          // authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/TwoW_Coin/TwoW_Coin',
        },
        {
          path: '/appWolf/2W/2W_Coin_New',
          name: 'avatarframePeriodv2_manger_new',
          authority: AccessRouteId.wolf_2w_avatar,
          // authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/TwoW_Coin/TwoW_Coin_New',
        },
        {
          path: '/appWolf/2W/PrizePoolManager',
          name: 'PrizePoolv2_manger',
          authority: AccessRouteId.wolf_2w_avatar,
          // authority: AccessRouteId.wolf_redbag,
          component: './AppWolf/TwoW_Coin/PrizePoolManager',
        },
      ],
    },

    // 道具管理
    {
      path: 'itemManager',
      icon: 'shopping',
      name: 'itemManager',
      authority: AccessRouteId.wolf_item_distribution,
      routes: [
        {
          path: '/appWolf/itemManager/itemDistribution',
          name: 'itemDistribution',
          authority: AccessRouteId.wolf_item_distribution,
          component: './AppWolf/ItemManager/ItemDistribution',
        },
      ],
    },

    // 游戏版型管理
    {
      path: 'gameBoard',
      icon: 'flag',
      name: 'gameBoard',
      authority: AccessRouteId.wolf_game_config,
      routes: [
        {
          path: '/appWolf/gameBoard/open',
          name: 'open',
          authority: AccessRouteId.wolf_game_config,
          component: './AppWolf/GameBoard/OpenManager',
        },
        {
          path: '/appWolf/gameBoard/randomRecord',
          name: 'randomRecord',
          authority: AccessRouteId.wolf_game_config,
          component: './AppWolf/GameBoard/randomRecord',
        },
      ],
    },
    // Operation
    {
      path: 'operation',
      icon: 'edit',
      name: 'operation',
      authority: AccessRouteId.wolf_operation,
      routes: [
        {
          path: '/appWolf/operation/treasure',
          name: 'treasure',
          component: './AppWolf/Operation/OpeTreasure',
        },
        {
          path: '/appWolf/operation/award',
          name: 'award',
          component: './AppWolf/Operation/OpeAward',
        },
        {
          path: '/appWolf/operation/status',
          name: 'status',
          component: './AppWolf/Operation/OpeStatus',
        },
        {
          path: '/appWolf/operation/avatarframe',
          name: 'avatarframe',
          component: './AppWolf/Operation/OpeAvatarframe',
        },
        {
          path: '/appWolf/operation/achieve',
          name: 'achieve',
          component: './AppWolf/Operation/OpeAchieve',
        },
      ],
    },
    // 巅峰狼王客服系统
    {
      path: 'wolfkingsys',
      icon: 'Book',
      name: 'wolfkingsys',
      authority: AccessRouteId.wolf_wolf_king,
      routes: [
        {
          path: '/appWolf/wolfkingsys/controlQuarter',
          name: 'controlQuarter',
          component: './AppWolf/WolfKingSys/ControlQuarter',
        },
        {
          path: '/appWolf/wolfkingsys/pointslist',
          name: 'pointslist',
          component: './AppWolf/WolfKingSys/PointsList',
        },
        {
          path: '/appWolf/wolfkingsys/funslist',
          name: 'funslist',
          component: './AppWolf/WolfKingFuns/FunsList',
        },
      ],
    },
    {
      path: 'rongCloudControl',
      icon: 'Audio',
      name: 'rongCloudControl',
      authority: AccessRouteId.wolf_rong_cloud_control,
      routes: [
        {
          path: '/appWolf/rongCloudControl/control',
          name: 'control',
          component: './AppWolf/rongCloudControl/control',
        },
      ],
    },
    {
      path: 'market',
      icon: 'dollar',
      name: 'market',
      authority: [
        AccessRouteId.wolf_makert,
        AccessRouteId.wolf_makert_list,
        AccessRouteId.wolf_market_mine,
        AccessRouteId.wolf_market_observer,
        AccessRouteId.wolf_market_transaction,
      ],
      routes: [
        {
          path: '/appWolf/Market/marketList',
          name: 'marketList',
          authority: AccessRouteId.wolf_makert_list,
          component: './AppWolf/Market/MarketList',
        },
        {
          path: '/appWolf/market/mineMarket',
          name: 'mineMarket',
          authority: AccessRouteId.wolf_market_mine,
          component: './AppWolf/Market/MineMarket',
        },
        {
          path: '/appWolf/market/marketObserver',
          name: 'marketobserver',
          authority: AccessRouteId.wolf_market_observer,
          component: './AppWolf/Market/MarketObserver',
        },
        {
          path: '/appWolf/market/transactionRecord',
          name: 'transactionRecord',
          authority: AccessRouteId.wolf_market_transaction,
          component: './AppWolf/Market/TransactionRecord',
        },
        {
          path: '/appWolf/market/hiddenFundOutput',
          name: 'hiddenFundOutput',
          // authority:AccessRouteId.wolf_market_hiddenFundOutput,
          component: './AppWolf/Market/HiddenFundOutput',
        },
        {
          path: '/appWolf/market/stonemanage',
          name: 'stonemanage',
          authority: AccessRouteId.wolf_market_transaction,
          component: './AppWolf/StoneManage/StoneManage',
        },
        {
          path: '/appWolf/market/stoneWeekManage',
          name: 'stoneWeekManage',
          authority: AccessRouteId.wolf_market_transaction,
          component: './AppWolf/StoneWeekManage/StoneWeekManage',
        },
        {
          path: '/appWolf/market/stoneSeason',
          name: 'stoneSeason',
          authority: AccessRouteId.wolf_market_transaction,
          component: './AppWolf/StoneSeason/StoneSeasonTab',
        },
        {
          path: '/appWolf/market/stoneFrameCrystal',
          name: 'stoneFrameCrystal',
          authority: AccessRouteId.wolf_market_transaction,
          component: './AppWolf/StoneFrameCrystal/StoneFrameCrystal',
        },
        {
          path: '/appWolf/market/stoneRoleSpar',
          name: 'stoneRoleSpar',
          authority: AccessRouteId.wolf_market_transaction,
          component: './AppWolf/StoneRoleSpar/StoneRoleSpar',
        },
        {
          path: '/appWolf/market/avatarmap',
          name: 'avatarmap',
          authority: AccessRouteId.wolf_market_transaction,
          component: './AppWolf/Market/AvatarMap',
        },
        {
          path: '/appWolf/market/gemrecord',
          name: 'gemrecord',
          authority: AccessRouteId.wolf_market_transaction,
          component: './AppWolf/Market/GemRecord',
        },
        {
          path: '/appWolf/market/seasonguide',
          name: 'seasonguide',
          authority: AccessRouteId.wolf_market_transaction,
          component: './AppWolf/Market/SeasonGuide',
        },
        {
          path: '/appWolf/market/newstonemanage',
          name: 'newstonemanage',
          authority: AccessRouteId.wolf_market_transaction,
          component: './AppWolf/Market/NewStoneManage/NewStoneManage',
        },
        
      ],
    },
    {
      path: 'activity',
      icon: 'Fullscreen',
      name: 'activity',
      authority: [AccessRouteId.wolf_aty_award, AccessRouteId.wolf_aty_conf],
      routes: [
        {
          path: '/appWolf/activity/conf',
          name: 'conf',
          authority: AccessRouteId.wolf_aty_conf,
          component: './AppWolf/ActivityAward/ConfManager',
        },
        {
          path: '/appWolf/activity/award',
          name: 'award',
          authority: AccessRouteId.wolf_aty_award,
          component: './AppWolf/ActivityAward/AwardManager',
        },
        {
          path: '/appWolf/activity/task',
          name: 'task',
          authority: AccessRouteId.wolf_aty_task,
          component: './AppWolf/ActivityAward/TaskManager',
        },
        {
          path: '/appWolf/activity/taskRefresh',
          name: 'taskRefresh',
          authority: AccessRouteId.wolf_aty_task,
          component: './AppWolf/ActivityAward/TaskRefreshManager',
        },
        // {
        //   path: '/appWolf/activity/anchorShow',
        //   name: 'anchorShow',
        //   component: './AppWolf/WolfKingSys/AnchorShow',
        // },
        // {
        //   path: '/appWolf/activity/talentState',
        //   name: 'talentState',
        //   component: './AppWolf/ActivityAward/TalentState',
        // },
        // {
        //   path: '/appWolf/activity/talentPlayerList',
        //   name: 'talentPlayerList',
        //   component: './AppWolf/ActivityAward/TalentPlayerList',
        // },
        // {
        //   path: '/appWolf/activity/talent',
        //   name: 'talent',
        //   authority: AccessRouteId.wolf_aty_talent,
        //   component: './AppWolf/TalentManager/TalentManager',
        // },
        // {
        //   path: '/appWolf/activity/designStar',
        //   name: 'designStar',
        //   // authority: AccessRouteId.wolf_aty_talent,
        //   component: './AppWolf/DesignStar/DesignStar',
        // },

        {
          path: '/appWolf/activity/exchangeFail',
          name: 'exchangeFail',
          // authority: AccessRouteId.wolf_aty_talent,
          component: './AppWolf/ActivityExchangeFail/List',
        },
        // {
        //   path: '/appWolf/activity/greenGala',
        //   name: 'greenGala',
        //   // authority: AccessRouteId.wolf_aty_talent,
        //   component: './AppWolf/GreenGala/GreenGala',
        // },
        // {
        //   path: '/appWolf/activity/magicFactory',
        //   name: 'magicFactory',
        //   // authority: AccessRouteId.wolf_aty_talent,
        //   component: './AppWolf/MagicFactory/MagicFactory',
        // },
      ],
    },
  ],
};

export default AppWolfRoutes;
