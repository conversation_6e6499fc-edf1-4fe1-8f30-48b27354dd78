/*
 * @Description: 流水管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-18 09:33:14
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  AutoComplete,
  Row,
  Col,
  Radio,
  Image,
  Table,
  Tag,
  Typography,
  message,
} from 'antd';
import * as styles from './ScriptEditFlowManager.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import { ColumnProps } from 'antd/lib/table';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName, getOptionList, getSelectName } from '@/utils/mallTool';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
export const PAGESIZE = 8;


const defaultPayload = { current: 1, pageCount: PAGESIZE, where: "m.status = 1" };

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  merchantSimpleList: scriptkill.merchantSimpleList,
  scriptSimpleList: scriptkill.scriptSimpleList,
  currentMerchantScriptList: scriptkill.currentMerchantScriptList,
  auditFlowList: scriptkill.auditFlowList,
  auditFlowCount: scriptkill.auditFlowCount,
  operateUserList: scriptkill.operateUserList,
  scriptEditAuditFlowList: scriptkill.scriptEditAuditFlowList,
  scriptEditAuditFlowCount: scriptkill.scriptEditAuditFlowCount,
  merchantDicList: scriptkill.merchantDicList,
}))

export default class ScriptEditFlowManager extends Component<any, any> {
  constructor(props) {
    super(props);
    
    this.state = {
      currentMerchantId: -1,
      currentScriptId: -1,
      currentUser: -1,
      currentMerchantValue: "",
      currentScriptValue: "",
      currentUserValue: "",

      currentPage: 1,
      columns: this.makeColumns(1),
    }

    this.props.dispatch({
      type: 'scriptkill/setScriptEditAuditFlowList',
      payload: []
    })
    this.props.dispatch({
      type: 'scriptkill/setScriptEditAuditFlowCount',
      payload: 0,
    });
    this.props.dispatch({
      type: 'scriptkill/getMerchantDicList',
      payload: { type: 3 }
    });
    this.dispatchScriptSimpleList();
    this.dispatchOperateUserList();
  }

  render() {
    const { isLoading, scriptEditAuditFlowList } = this.props;
    return (
      <Spin spinning={!!isLoading}>
        <PageHeaderWrapper title="检索条件" content={this.renderHeader()}>
          <Table
            scroll={{ x: 1550 }}
            columns={this.state.columns}
            dataSource={scriptEditAuditFlowList}
            loading={!!isLoading}
            bordered={true}
            rowKey={(record, index) => index.toString()}
            pagination={{  // 分页
              pageSize: PAGESIZE,
              current: this.state.currentPage,
              total: this.props.scriptEditAuditFlowCount,
              onChange: this.changePage,
            }}
          />
        </PageHeaderWrapper>
      </Spin>
    );
  }

  renderHeader() {
    const { globalPlayerId } = this.props;
    const { user_noSearch, old_usernameSearch, new_usernameSearch, statusSearch, createtimeSearch, updatetimeSearch } = this.state;
    const colMd = 2;
    const colInputMd = 6;
    return (
      <>
        <Row style={{ width: 2000, marginTop: 10 }}>
          <Col md={colMd}>
            剧本名称/ID:
          </Col>
          <Col md={colInputMd} style={{ marginTop: -5, marginLeft: -40 }}>
            <AutoComplete
              allowClear={true}
              className={styles.content}
              options={getOptionList(this.props.scriptSimpleList)}
              onSelect={this.onSelectScrpit}
              onChange={this.onChangeScrpit}
              value={this.state.currentScriptValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) =>
                option.value.indexOf(inputValue) !== -1
              }
            />
          </Col>
        </Row>
        <Row style={{ width: 2000, marginTop: 10 }}>
          <Col md={colMd}>
            审核员名称/ID:
          </Col>
          <Col md={colInputMd} style={{ marginTop: -5, marginLeft: -40 }}>
            <AutoComplete
              allowClear={true}
              className={styles.content}
              options={getOptionList(this.props.operateUserList)}
              onSelect={this.onSelectUser}
              onChange={this.onChangeUser}
              value={this.state.currentUserValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) =>
                option.value.indexOf(inputValue) !== -1
              }
            />
          </Col>
        </Row>
      </>
    );
  }

  makeColumns(type: any) {
    const columns: ColumnProps<any>[] = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 70,
        align: 'center',
      },
      {
        title: '审核员',
        dataIndex: 'nickname',
        key: 'nickname',
        width: 100,
        align: 'center',
      },
      {
        title: '剧本 ID',
        dataIndex: 'script_id',
        key: 'script_id',
        align: 'center',
      },
      {
        title: '剧本名称',
        dataIndex: 'new_name',
        key: 'new_name',
        align: 'center',
      },
      {
        title: '名称修改',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
      },
      {
        title: '描述修改(新)',
        dataIndex: 'script_desc',
        key: 'script_desc',
        align: 'center',
        width: 500,
      },
      {
        title: '发行商修改',
        dataIndex: 'publisher',
        key: 'publisher',
        align: 'center',
      },
      {
        title: '角色数量',
        dataIndex: 'role_num',
        key: 'role_num',
        align: 'center',
      },
      {
        title: '提示(新)',
        dataIndex: 'tips',
        key: 'tips',
        align: 'center',
      },
      {
        title: '热门修改',
        dataIndex: 'hot',
        key: 'hot',
        align: 'center',
        render: (val) => {
          return this.getChangeStr(val, this.getHotStr);
        }
      },
      {
        title: '困难修改',
        dataIndex: 'difficulty',
        key: 'difficulty',
        align: 'center',
      },
      {
        title: '主题修改',
        dataIndex: 'theme',
        key: 'theme',
        align: 'center',
        render: (val) => {
          return this.getChangeStr(val, this.getThemeStr);
        }
      },
      {
        title: '状态修改',
        dataIndex: 'delsign',
        key: 'delsign',
        align: 'center',
        render: (val) => {
          return this.getChangeStr(val, this.getDelsignStr);
        }
      },
      {
        title: '审核时间',
        dataIndex: 'createtime',
        align: 'center',
        width: 120,
        render: (val) => {
          return <div>{val == null ? '\\' : getTimeStr(val)}</div>;
        }
      }
    ];
    return columns;
  }

  getChangeStr = (val, strFunc) => {
    let str = "";
    if (!val) {
      return <div>{str}</div>;
    }
    const all = val.split("->");
    if (all.length <= 1) {
      str += "无->";
      const newT = all[0].split(",");
      str += strFunc(newT);
    } else {
      const oldT = all[0].split(",");
      str += strFunc(oldT);
      str += "->";
      const newT = all[1].split(",");
      str += strFunc(newT);
    }
    return <div>{str}</div>;
  }

  getHotStr = (hot) => {
    return hot == 1 ? "是" : "否";
  }

  getDelsignStr = (delsign) => {
    return delsign == 1 ? "下架" : "上架";
  }

  getThemeStr = (list) => {
    let str = "";
    for (const item of list) {
      str += getCateName(item, this.props.merchantDicList) + ",";
    }
    return str.substring(0, str.length - 1);
  }

  onChangeScrpit = (data: string) => {
    this.setState({ currentScriptValue: data });
  };

  onChangeMerchant = (data: string) => {
    this.setState({ currentMerchantValue: data });
  };

  onChangeUser = (data: string) => {
    this.setState({ currentUserValue: data });
  };

  changePage = (page) => {
    this.setState({ currentPage: page });
    const req = {
      operate_user_id: this.state.currentUser,
      script_id: this.state.currentScriptId,
      current: page,
      pageCount: PAGESIZE
    };
    this.dispatchAduitFlowList(req);
  };

  onSelectScrpit = (value) => {
    const val = value.substring(4, value.indexOf(" 名称"));
    this.setState({
      currentScriptId: val,
      currentUser: -1,
      currentScriptValue: value,
      currentUserValue: "",
      currentPage: 1,
      columns: this.makeColumns(2)
    });

    this.dispatchAduitFlowList({
      script_id: val,
      current: 1,
      pageCount: PAGESIZE
    });
  };

  onSelectUser = (value) => {
    const val = value.substring(4, value.indexOf(" 名称"));
    this.setState({
      currentMerchantId: -1,
      currentScriptId: -1,
      currentUser: val,
      currentMerchantValue: "",
      currentScriptValue: "",
      currentUserValue: value,
      currentPage: 1,
      columns: this.makeColumns(3)
    });

    this.dispatchAduitFlowList({
      operate_user_id: val,
      current: 1,
      pageCount: PAGESIZE
    });
  };

  dispatchAduitFlowList = (req) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getScriptEditAuditFlowList',
      payload: req
    }).then(() => {
    });

    dispatch({
      type: 'scriptkill/getScriptEditAuditFlowListCount',
      payload: req
    }).then(() => {
    });
  }

  dispatchScriptSimpleList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getScriptSimpleList',
    });
  };

  dispatchOperateUserList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getOperateUserList',
    });
  };
}
