export interface IStoneListItem {
  id?: number;
  name: string;
  level: number;
  season_id: number;
  pic?: string;
}
export interface IPoolListItem {
  id?: number;
  card_level: number;
  weight: number;
}
export interface ISsCardListItem {
  id: number;
  s_no: number;
  level: number;
  name: string;
  desc: string;
  img?: string;
  detail_img?: string;
  weight?: number;
}
export interface IRuleListItem {
  id?: number;
  desc: string;
  rate: number; 
  type: number; 
}
export interface IStoneRuleItem {
  delsign: number;
  desc: string;
  id: number;
  rate: number;
  rule_id: number;
  frame_id:number;
  stone_name: string;
}
export interface IStoneRuleReq {
  id: number;
  rate: number;
  rule_id: number;
}

export interface INewStoneManage {
  stoneList: IStoneListItem[];
  poolList: IPoolListItem[];
  ssCardList: ISsCardListItem[];
  ruleList: IRuleListItem[];
  stoneRuleList: IStoneRuleItem[];
}
export interface INewStoneManageResponse {}
export interface INewStoneManageRequest {}

export interface IStoneListReq {
  id?: number;
  name: string;
  level: string;
  pic?: string;
}
export interface ISeasonPoolListReq {
  id?: number;
  frame_id: number;
  card_level: number;
  weight: number;
}
