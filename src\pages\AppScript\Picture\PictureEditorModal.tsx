/*
 * @Description: 称号-编辑模态页
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2019-10-12 16:56:14
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-11-25 10:10:08
 */

import { Component } from 'react';
import { connect } from 'dva';
import { ILogin } from '@/models/login';
import { Modal, Switch, Input, Select, message, InputNumber } from 'antd';
import * as styles from './PictureEditorModal.less';
import { wfPropsGiftSourceType, wfPropsGiftType, wfPropsType } from "../../../dto/staticEnum";
import { Igift } from '@/dto/mallManager';
import { getGiftSourceStr, checkNameExist } from '@/utils/mallTool';
import { getCateName, getSelectName } from '@/utils/mallTool';
import React from 'react';
const { Option } = Select;

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  merchantSimpleList: scriptkill.merchantSimpleList,
  pictureList: scriptkill.pictureList,
  currentMerchantScriptList: scriptkill.currentMerchantScriptList,
}))

class PictureEditorModal extends React.Component<any, any> {
  constructor(props) {
    super(props);
    const { dataModelType, currentPicture } = props;
    //编辑
    if (dataModelType == 1) {
      this.state = {
        id: currentPicture.id,
        merchant_id: currentPicture.merchant_id,
        sort_id: currentPicture.sort_id,
        delsign: currentPicture.delsign
      };
    } else {
      this.state = {
        merchant_id: currentPicture.merchant_id,
        sort_id: null,
        delsign: 1,
      };
    }
  }

  render() {
    const { dataModelType, isLoading } = this.props;
    let title = '';
    let okText = '提交';
    if (dataModelType == 1) {
      title = '编辑';
      okText = '提交编辑';
    } else {
      title = '创建';
      okText = '提交创建';
    }
    return (
      <Modal
        bodyStyle={{ padding: '5px 10px 5px 10px' }}
        closable={false}
        maskClosable={false}
        confirmLoading={!!isLoading}
        centered={true}
        title={title}
        onCancel={this.onCloseModel}
        onOk={this.onClickOk}
        cancelText="取消"
        okText={okText}
        visible={dataModelType > 0 ? true : false}
      >
        <div>
          {dataModelType > 0 && this.renderEditModal()}
        </div>
      </Modal>
    );
  }

  renderEditModal = () => {
    const { dataModelType } = this.props;
    return (
      <div className={styles.modalForm}>
        <div className={styles.formItem}>
          <div className={styles.title}>排序值：</div>
          <Input
            className={styles.content}
            value={this.state.sort_id}
            onChange={this.onChangeSort}
          />
        </div>
      </div>
    );
  };

  getGiftTypeStr = () => {
    let string = ``;
    if (this.state.sex == 0) {
      string = `女`;
    } else if (this.state.sex == 1) {
      string = `男`;
    }
    return string;
  };

  onChangeName = ({ target: { value } }) => {
    this.setState({ name: value });
  };

  onChangeNum = ( value ) => {
    this.setState({ num: value });
  };

  onChangeCondition = ({ target: { value } }) => {
    this.setState({ condition: value });
  };

  onChangeDescribe = ({ target: { value } }) => {
    this.setState({ describe: value });
  };

  onChangeSort = ({ target: { value } }) => {
    this.setState({ sort_id: value });
  };

  onChangeSex = (value) => {
    this.setState({ sex: value });
  };

  onChangeSource = (value) => {
    this.setState({ source: value });
  };

  onChangeLevel = (value) => {
    this.setState({ level: value });
  };

  onChangeType = (value) => {
    this.setState({ type: value });
  };

  onChangehot = (checked: boolean) => {
    this.setState({ is_default: checked ? 1 : 0 });
  };

  onChangedelsign = (checked: boolean) => {
    this.setState({ delsign: checked ? 0 : 1 });
  };

  checkNullStr = (value) => {
    return value || value == 0 ? value : "";
  };

  handleRequest = (isCreating: boolean) => {
    const request: any = {
      id: this.state.id,
      merchant_id: this.state.merchant_id,
      sort_id: this.state.sort_id,
      delsign: this.state.delsign
    };
    return request;
  };

  //提交编辑
  handleSubmitEdit = () => {
    const { dispatch } = this.props;
    const request = this.handleRequest(false);

    console.log("request", request);
    const flag = this.checkRequest(request, false);
    if (flag) {
      dispatch({
        type: 'scriptkill/updatePicture',
        payload: request
      }).then(() => {
        this.onCloseModel();
      });
    }
  };

  handleSubmitCreate = () => {
    const { dispatch } = this.props;
    const request = this.handleRequest(true);
    console.log("request", request);
    const flag = this.checkRequest(request, true);
    if (flag) {
      dispatch({
        type: 'scriptkill/insertPicture',
        payload: request
      }).then(() => {
        this.onCloseModel();
      });
    }
  };

  checkRequest = (request: any, isCreating: boolean) => {
    if (!isCreating && (request.id == null || request.id == undefined || request.id <= 0)) {
      message.error("数据错误，请重新编辑");
      return false;
    }

    if (request.sort_id == null || request.sort_id == undefined || request.sort_id == "" || request.sort_id < 0) {
      message.error("请填写排序值");
      return false;
    }

    return true;
  }

  //关闭模态页
  onCloseModel = () => {
    this.props.onClickClose();
  };

  onClickOk = () => {
    const { dataModelType } = this.props;
    if (dataModelType == 1) {
      this.handleSubmitEdit();
    } else {
      this.handleSubmitCreate();
    }
  };
}

export default PictureEditorModal;
