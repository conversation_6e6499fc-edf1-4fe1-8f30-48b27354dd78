/*
 * @Description: 宝箱管理-编辑模态页
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2019-10-12 16:56:14
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-07 14:41:41
 */

import { Component } from 'react';
import { connect } from 'dva';
import { Igift, IuploadImg, IdocumentResourceReq } from '@/dto/mallManager';
import { ILogin } from '@/models/login';
import { UploadPath } from '@/dto/staticEnum';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { getUUID } from '@/utils/utils';
import {
  Upload,
  Row,
  Col,
  Button,
  Modal,
  message,
} from 'antd';
import AppList from '../../../pages/AppList';
import * as styles from './MomentPictureImageEditorModal.less';
import { insertDocumentResource } from "@/services/apiWerewolf.js";
import React from 'react';
import { getDayStr } from '@/utils/momentTool';
import { UploadChangeParam } from 'antd/lib/upload';
@connect(({ loading, login, merchantMoment }: { loading: IdvaLoading; login: ILogin; merchantMoment: any }) => ({
  uid: login.uid,
  isLoading: loading.models['merchantMoment'],
  momentList: merchantMoment.momentList,
  momentListCount: merchantMoment.momentListCount,
  pictureList: merchantMoment.pictureList,
}))
class MomentPictureImageEditorModal extends React.Component<any, any> {
  private picName = getUUID(12);

  constructor(props) {
    super(props);
    const { currentPicture } = props;
    console.log("currentTitle", currentPicture);
    const uploadImgList: IuploadImg[] = this.getUploadImgList(currentPicture);
    this.state = {
      id: currentPicture.id,
      moment_id: currentPicture.moment_id,
      oss_url: currentPicture.oss_url,
      sort_id: currentPicture.sort_id,
      delsign: currentPicture.delsign,
      uploadImgList,
    };
  }

  render() {
    const { imageModelType, isLoading } = this.props;

    const title = '上传资源';
    return (
      <Modal
        closable={true}
        maskClosable={false}
        confirmLoading={!!isLoading}
        centered={true}
        title={title}
        width="750px"
        onCancel={this.onCloseModel}
        onOk={this.onClickOk}
        cancelText="取消"
        okText="确定"
        visible={imageModelType > 0 ? true : false}
        footer={null}
      >
        <div>
          {this.renderStaticResource()}
          <div className={styles.submit_btn}>
            <Button className={styles.submit_btn} type="primary" onClick={this.onClickOk} loading={!!isLoading}>确定</Button>
          </div>
        </div>
      </Modal>
    );
  }

  /**
   * 静态资源上传
   */
  renderStaticResource = () => {
    return (
      <div className={styles.resources_div}>
        <Row className={styles.row_div} type="flex" justify="space-around">
          {
            this.state.uploadImgList.map((item, index) => {
              if (item.status == true) {
                return (
                  <div key={index}>
                    <Col span={4}>
                      {this.renderShowImgModel(item)}
                    </Col>
                  </div>
                );
              } else {
                return (
                  <div key={index}>
                    <Col span={4}>
                      {this.renderUploadModel(item)}
                    </Col>
                  </div>
                );
              }
            })
          }
        </Row>
      </div>
    );
  }

  /**
   * 展示图片模块
   */
  renderShowImgModel = (item: IuploadImg) => {

    if (item.fileExtension === "mp3" || item.fileExtension === "bundle") {
      return (
        <div className={styles.show_img_div}>
          <div className={styles.show_upload}>
            <div className={styles.text_div_done}>{"音频文件上传成功 路径 \r\n" + item.url}</div>
          </div>
        </div>
      );
    } else {
      return (
        <div className={styles.show_img_div}>
          <div className={styles.show_upload}>
            <img className={styles.show_upload_done} src={item.url}></img>
            <div className={styles.text_div_done}>{item.showName}</div>
          </div>
        </div>
      );
    }
  }

  /**
   * 上传模块
   */
  renderUploadModel = (item: IuploadImg) => {
    const uploadUrl = '/megaupload';
    return (
      <div className={styles.show_img_div}>
        <div className={styles.show_upload}>
          <Upload action={uploadUrl}
            headers={{ 'Access-Control-Allow-Origin': '*', 'X-Requested-With': null }}
            withCredentials={true}
            data={this.postUpdata(item)}
            listType='picture-card'
            showUploadList={false}
            className="avatar-uploader"
            onChange={(value) => this.onChange(value, item)}
            beforeUpload={this.handleBeforeUpload}
          >
            {this.renderUploadButton()}
          </Upload>
        </div>
        <div className={styles.text_div}>{item.showName}</div>
      </div>
    );
  }

  handleBeforeUpload = (file) => {
    //限制图片 格式、size、分辨率
    const isJPG = file.type === 'image/jpeg';
    // const isJPEG  = file.type === 'image/jpeg';
    const isGIF = file.type === 'image/gif';
    const isPNG = file.type === 'image/png';
    if (!(isJPG || isGIF || isPNG)) {
        Modal.error({
            title: '只能上传JPG 、JPEG 、GIF、 PNG格式的图片~'
        })
    }
    const isLt2M = file.size / 1024 / 1024 < 5;
    if (!isLt2M) {
        Modal.error({
            title: '超过5M限制 不允许上传~'
        })
    }
    return (isJPG || isGIF || isPNG ) && isLt2M ;
}

  /**
   * 上传图片时的post数据
   */
  postUpdata = (item: any) => {
    const obj = {
      type: item.type,
      name: item.name,
    }
    return obj;
  }

  /**
   * 上传图片时图片变换
   * @param info
   */
  onChange = (info: UploadChangeParam, item: IuploadImg) => {
    if (info.file.status === 'done') {

      if (item.needInsertDocument == 1) {
        this.insertDocumentRes(info, item);
      } else {
        this.showImgUpload(info, item);
      }

    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
    }
  }

  showImgUpload = (info: UploadChangeParam, item: IuploadImg) => {
    const oriName = info.file.name;
    message.success(`${oriName} 上传成功`);

    console.log("infoinfoinfo", info);

    const nameList = oriName.split(".");
    const extension = nameList.pop();

    const uploadImgList: IuploadImg[] = _.cloneDeep(this.state.uploadImgList);

    for (let i = 0; i < uploadImgList.length; i++) {
      if (item.id == uploadImgList[i].id) {
        item.status = true;
        item.id = uploadImgList[i].id;
        item.type = uploadImgList[i].type;
        item.fileExtension = extension;
        item.name = uploadImgList[i].name + "." + extension;
        item.url = uploadImgList[i].url + uploadImgList[i].name + "." + extension;
        uploadImgList[i] = item;
        break;
      }
    }
    this.setState({ uploadImgList });
  }

  /**
   * 上传按钮
   */
  renderUploadButton = () => {
    return (
      <div>
        <LegacyIcon type={this.state.isUploading ? 'loading' : 'plus'} />
        <div className="ant-upload-text">上传</div>
      </div>
    );
  }

  getAnimationImgStr = (item: IuploadImg) => {
    const string = AppList.imageGiftPackUrl + item.name + "." + item.fileExtension;
    return string
  };

  public async insertDocumentRes(info: UploadChangeParam, item: IuploadImg) {

    const request: any = {};

    request.name = item.name;
    request.extension = "." + item.fileExtension;
    request.document = "hallWebp";

    if (item.fileExtension == "png") {
      request.type = 1;
    } else {
      request.type = 0;
    }

    const response = await insertDocumentResource(request);
    if (response.err_code == 200) {
      this.showImgUpload(info, item);
    } else {
      message.error(`${info.file.name} 上传失败 请重新上传`);
    }
  }

  getUploadImgList = (currentPicture: any) => {

    const name = `${getDayStr()}/${this.props.news.oss_path_key}/${this.picName}`;

    const uploadImgList: IuploadImg[] = [];
    uploadImgList.push({
      id: 1,
      url: AppList.imageOssScriptSkillMerchantPic,
      name,
      status: false,
      type: UploadPath.ossMomentPicture,
      fileExtension: "",
      showName: "资源图",
      needInsertDocument: 0,
    });
    return uploadImgList;
  };

  //关闭模态页
  onCloseModel = () => {
    this.props.onClickClose();
  };

  onClickOk = () => {
    const { dispatch } = this.props;

    for (let index = 0; index < this.state.uploadImgList.length; index++) {
      const element = this.state.uploadImgList[index];
      if (element.status == false) {
        message.error("请上传【" + element.showName + "】");
        return;
      }
    }

    let oss_url = "";
    for (const item of this.state.uploadImgList) {
      oss_url = item.url;

    }
    const request: any = {
      id: this.state.id,
      oss_url,
      moment_id: this.state.moment_id
    };
    console.log("requestrequest", request);

    dispatch({
      type: 'merchantMoment/updateMomentPictureUrl',
      payload: request
    }).then(() => {
      this.props.dispatchPicList();
      this.onCloseModel();
    });
  };
}

export default MomentPictureImageEditorModal;
