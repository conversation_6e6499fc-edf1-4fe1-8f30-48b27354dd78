/*
 * @Description: 称号管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-19 14:58:20
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  AutoComplete,
  Row,
  Col,
  Radio,
  Image,
  Table,
  Tag,
  Typography,
  message,
  InputNumber,
  Popconfirm,
  Input
} from 'antd';
const { Text } = Typography;
const { Meta } = Card;
import { PlusOutlined, CloudUploadOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import * as styles from './UserInfoAvatarManager.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import AppList from '../../../pages/AppList';
import { ColumnProps } from 'antd/lib/table';
import { checkNotNull } from '@/utils/emptyTool';
import MerchantScriptTable from '../Merchant/MerchantScriptTable';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName, getSelectName } from '@/utils/mallTool';
import { getUserBg } from '../ScriptUtils/ScriptUtils';
import { EditOutlined, UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import { tabEnum } from './UserInfoManager';
export const PAGESIZE = 8;

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  merchantSimpleList: scriptkill.merchantSimpleList,
  scriptSimpleList: scriptkill.scriptSimpleList,
  userInfo: scriptkill.userInfo,
  merchantDicList: scriptkill.merchantDicList,
  userBgList: scriptkill.userBgList,
}))

export default class UserInfoBgManager extends Component<any, any> {
  constructor(props) {
    super(props);

  }

  render() {
    const { isLoading } = this.props;
    return (
      <Spin spinning={!!isLoading}>
        <Row gutter={[8, 8]}>
          {this.props.userBgList.map((item, index) => {
            return (
              <Col key={item.img_url} span={4}>
                <Card
                  hoverable={true}
                  // bodyStyle={{ height: 0 }}//marginTop: -50,
                  style={{ width: 200 }}
                  cover={this.getCover(item)}
                  actions={this.getActionList(item)}
                >
                  <Meta
                    title={`是否被举报: ${item.report > 0 ? "是" : "否"}`}
                    description={<div>
                      {item.report > 0 &&
                        <div>
                          {`理由: ${item.report_text ? item.report_text : "无"}`}
                        </div>
                      }
                      <div>
                        {`状态: ${item.delsign == 1 ? "下架" : "正常"}`}
                      </div>
                      <div>
                        {`使用中: ${item.is_use == 1 ? "是" : "否"}`}
                      </div>
                    </div>}
                  />
                </Card>
              </Col>

            );
          })}
        </Row>
      </Spin>
    );
  }

  getCover = (item) => {
    if (item.img_url != undefined && item.img_url != null && item.img_url != "") {
      return <Image src={getUserBg(item.img_url)} width={200} height={200} />
    } else {
      return <div style={{ width: 400, height: 120, textAlign: "center", marginTop: 120 }}>请上传图片</div>
    }
  }

  getActionList = (item) => {
    return [
      <Popconfirm
        title={`确定要下架该背景吗?`}
        onConfirm={() => { this.onClickDelete(item) }}
        okText="Yes"
        cancelText="No"
      >
        <Button type="link"><DeleteOutlined key="setting" />{"直接下架"}</Button>,
        </Popconfirm>,
    ];
  }

  onClickDelete = (item) => {
    const { dispatch } = this.props;
    console.log("item", item);

    const req = {
      operate_user_id: this.props.uid,
      id: item.id,
      report_id: item.report_id,
      userId: item.user_id,
      url: getUserBg(item.img_url),
    }
    dispatch({
      type: 'scriptkill/refuseUserBg',
      payload: req
    }).then(() => {
    });
  }

}
