/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-09 13:15:11
 * @LastEditTime: 2020-10-12 16:04:05
 * @LastEditors: jiawen.wang
 */

import React from 'react';
import { ILogin } from '@/models/login';
import { Modal, Form, Input, Steps, Button, Radio, Upload, message, Result } from 'antd';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import { IAnchorBanner } from './models/anchorBanner';
import { UploadBannerStep, IanchorListItemRes } from "@/dto/anchorBanner"

import * as styles from './AnchorBanner.less';
import { FormInstance } from 'antd/lib/form';
import AppList from '../../../pages/AppList';
import { UploadPath } from '@/dto/staticEnum';
const { Step } = Steps;

interface IAnchorBannerUpdateModalProps {
    dispatch?: Function
    onClose: Function
    showModal?: number
    uploadStep?: UploadBannerStep,
    isBaseing?: <PERSON><PERSON><PERSON>,
    isCompleteing?: <PERSON><PERSON><PERSON>,
    isNewBannerUpdateModal: boolean,
    imageName: string
    itemId: number,
    editData: IanchorListItemRes
}
interface IAnchorBannerUpdateModalState {
    baseInfo: any
    imageUrl: any
    loading: Boolean
}
@connect(({ anchorBanner, loading }: { anchorBanner: IAnchorBanner; loading: IdvaLoading }) => ({
    // isLoading: loading.models['rongCloudControl'],
    anchorBannerList: anchorBanner.anchorBannerList,
    uploadStep: anchorBanner.uploadStep,
    isBaseing: loading.effects['anchorBanner/uploadNewBannerBaseInfo'],
    isCompleteing: loading.effects['anchorBanner/uploadNewBannerBaseInfo'],
    imageName: anchorBanner.imageName,
    itemId: anchorBanner.itemId,
    editData: anchorBanner.editData
}))

class AnchorBannerUpdateModal extends React.Component<IAnchorBannerUpdateModalProps, IAnchorBannerUpdateModalState> {
    formRef = React.createRef<FormInstance>();
    constructor(props) {
        super(props)
        this.state = {
            baseInfo: '',
            imageUrl: '',
            loading: false,
        }
    }
    //渲染模态框
    render() {
        const { uploadStep, isNewBannerUpdateModal, editData } = this.props
        const title = "编辑";
        return (
            <div>
                <Modal
                    title={title}
                    onCancel={this.handleCancel}
                    footer={null}
                    visible={isNewBannerUpdateModal}
                >
                    {uploadStep == UploadBannerStep.Base && this.renderStepDefault()}

                </Modal>
            </div>
        )
    }
    //渲染基础表单页
    renderStepDefault = () => {
        const { editData } = this.props
        console.log(editData)
        const layout = {
            labelCol: { span: 5 },
            wrapperCol: { span: 18 }
        };
        const validateMessages = {
            required: '请填写${label}'
        };
        const initialValues = {
            id: editData.id,
            sort: editData.sort,
            remark: editData.remark,
            content: editData.content,
            url: editData.page_url,
            recommend_tag: editData.recommend_tag,
            show_type: editData.show_type,
            turn_to_page: editData.turn_to_page
        }
        const onFinish = (values) => {
            const { editData } = this.props

            const value = {
                id: Number(editData.id),
                sort: Number(values.sort),
                remark: values.remark,
                content: values.content,
                recommend_tag: Number(values.recommend_tag),
                show_type: Number(values.show_type),
                page_url: values.url,
                turn_to_page: Number(values.turn_to_page),
            };
            console.log(values)
            this.props.dispatch({
                type: 'anchorBanner/fetchUpdateAnchorBanner',
                payload: value
            });
            this.formRef.current.resetFields();
            this.props.onClose()
        }
        return (
            <div className={styles.step_div}>
                <Form
                    className={styles.stepForm}
                    {...layout}
                    ref={this.formRef}
                    onFinish={onFinish}
                    validateMessages={validateMessages}
                    initialValues={initialValues}>
                    <Form.Item name={'sort'} label="序号" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name={'remark'} label="主播名称" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name={'content'} label="展示文案" rules={[{ required: true }]}>
                        <Input.TextArea />
                    </Form.Item>
                    <Form.Item name="url" label="直播间url" rules={[{ required: true }]}>
                        <Input.TextArea />
                    </Form.Item>
                    <Form.Item name={'recommend_tag'} label="标签类型" rules={[{ required: true }]}>
                        <Radio.Group name="radiogroup">
                            <Radio value={1}>主播成长</Radio>
                            <Radio value={2}>推荐</Radio>
                            <Radio value={3}>招募</Radio>
                            <Radio value={4}>活动</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item name={'show_type'} label="显示类型" rules={[{ required: true }]}>
                        <Radio.Group name="radiogroup">
                            <Radio value={1}>画廊</Radio>
                            <Radio value={2}>列表</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item name={'turn_to_page'} label="跳转类型" rules={[{ required: true }]}>
                        <Radio.Group name="radiogroup">
                            <Radio value={1}>系统内置</Radio>
                            <Radio value={2}>App跳转</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <div className={styles.submit_btn}>
                        <Button type="primary" htmlType="submit" loading={!!this.props.isBaseing}>
                            修改
						</Button>
                    </div>
                </Form>
            </div>
        );
    }
    //下一步
    handleCancel = () => {
        this.props.onClose()

    }
}
export default AnchorBannerUpdateModal

