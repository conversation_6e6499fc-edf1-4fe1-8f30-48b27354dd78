/*
 * @Description: ai
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2023-08-31 11:01:15
 * @LastEditors: bo.wang
 * @LastEditTime: 2023-08-31 11:01:15
 */
import { AccessRouteId } from './accessRouteCof';

//import deductionRecord from '@/pages/AppWolf/ScoreViolation/models/deductionRecord';

const AppLeaseRoutes = {
  path: 'appLease',
  name: 'appLease',
  icon: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSNAS0ld9EOuWMXkLOBN2pwaV9qzW14K16FQA&usqp=CAU',
  Routes: ['src/layouts/Authorized'],
  authority: AccessRouteId.app_lease,
  routes: [

    {
      path: '/appLease/LeaseHistory',
      // path: 'mst',
      icon: 'api',
      name: 'history',
      authority: AccessRouteId.app_lease,
      component: './AppLease/LeaseHistory',
    }
  ],
};

export default AppLeaseRoutes;
