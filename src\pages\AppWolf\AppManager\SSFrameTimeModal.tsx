import { IVisibleBinderContentProps } from "@/componentsEasy/visibleBinder"
import { ILogin } from "@/models/login";
import { DatePicker, Form, Modal } from "antd"
import { useForm } from "antd/lib/form/Form";
import { connect } from "dva";
import moment from "moment";
import React, { useEffect } from "react"
import { ISSFrameTimeModel } from "../BroadcastRedPacket/models/ssFrameTimeModel"
import { getTimeStr } from '@/utils/momentTool';

export interface ISSFrameTimeModalProps {
    item: any,
    dispatch: any,
    searchString: string,
}


const SSFrameTimeModal: React.FC<ISSFrameTimeModalProps & IVisibleBinderContentProps> = (props) => {

    const { visible, setVisible, item,searchString, dispatch } = props;

    const [form] = useForm()

    
    useEffect(() => {
        if (item) {
            form.setFieldsValue({
                //编辑弹窗数据对应
                id: item.id,
                ss_start_time: moment(item.ss_start_time).isValid ? moment(item.ss_start_time) : undefined,
                ss_end_time: moment(item.ss_end_time).isValid ? moment(item.ss_end_time) : undefined,

            })

        }
    }, [item])

    function handleCancel() {
        setVisible(false)
        //清除表单内容
        form.resetFields()
    }

    function handleOk() {

        form.submit()

    }



    function handleFinish(values) {

        const params: any = {
            ...values,
            id: item.id,
            ss_start_time: getTimeStr(values.ss_start_time),
            ss_end_time: getTimeStr(values.ss_end_time),
            name:searchString
        }

        dispatch({
            type: 'SSFrameTime/updateSSFrameTime',
            payload: params,
        })

        handleCancel()

    }


    function render() {

        return (

            <Modal
                title={item ? `编辑ss头像框时间【ID = ${item.id}】` : '新增链接'}
                visible={visible}
                width={800}
                onCancel={handleCancel}
                onOk={handleOk}
                okText="提交"
                cancelText="取消"
            >
                <Form
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 20 }}
                    form={form}
                    name='control-hooks'
                    // initialValues={{ sum: 888 }}
                    onFinish={handleFinish}
                    size='middle'
                >
                    <Form.Item name="ss_start_time" label="开始时间" rules={[{ required: true }]}>
                        <DatePicker
                            style={{ width: 385 }}
                            showTime={true}
                            format="YYYY-MM-DD HH:mm:ss"
                        />
                    </Form.Item>
                    <Form.Item name="ss_end_time" label="结束时间" rules={[{ required: true }]}>
                        <DatePicker
                            style={{ width: 385 }}
                            showTime={true}
                            format="YYYY-MM-DD HH:mm:ss"
                        />
                    </Form.Item>
                </Form>

            </Modal>
        )
    }

    return render()
}

const mapStateToProps =
    ({
        loading,
        login,
        SSFrameTime,
    }: {
        loading: IdvaLoading;
        login: ILogin;
        SSFrameTime: ISSFrameTimeModel;
    }) => ({
        isLoading: loading.models.SSFrameTime,
        uid: login.uid,
    });


export default connect(mapStateToProps)(SSFrameTimeModal);

