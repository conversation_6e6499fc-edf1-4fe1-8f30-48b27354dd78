/*
 * @Description: 称号管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-25 09:45:15
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  AutoComplete,
  Row,
  Col,
  Radio,
  Image,
  Popconfirm,
  Avatar,
  Typography,
  message,
} from 'antd';
const { Meta } = Card;
import { EditOutlined, UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import * as styles from './PictureManager.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import AppList from '../../../pages/AppList';
import { ColumnProps } from 'antd/lib/table';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName, getOptionList } from '@/utils/mallTool';
import { checkNotNull } from '@/utils/emptyTool';
import { getErrorImg, getImageSize } from '@/utils/resUtils';
import PictureImageEditorModal from './PictureImageEditorModal';
import PictureEditorModal from './PictureEditorModal';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import Center from '@/pages/Account/Center/Applications';
export const PAGESIZE = 8;
const MB = 1000000;
const KB = 1000;

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  merchantSimpleList: scriptkill.merchantSimpleList,
  pictureList: scriptkill.pictureList,
  currentMerchantScriptList: scriptkill.currentMerchantScriptList,
}))

export default class PictureManager extends Component<any, any> {
  constructor(props) {
    super(props);

    this.state = {
      currentMerchantId: -1,
      imageModelType: -1,
      dataModelType: -1,
      currentMerchantValue: "",
      currentPicture: "",
      pictureSizeList: [],
    }
    this.dispatchClearPictureList();
    this.dispatchMerchantSimpleList();
  }
  

  render() {
    const { isLoading } = this.props;
    const { imageModelType, dataModelType, currentPicture, new_usernameSearch, statusSearch, createtimeSearch, updatetimeSearch } = this.state;
    return (
      <Spin spinning={!!isLoading}>
        <PageHeaderWrapper title="检索条件" content={this.renderHeader()}>
          <Row gutter={[8, 8]}>
            {this.props.pictureList.map((item, index) => {
              return (
                <Col key={index} span={6}>
                  <Card
                    hoverable={true}
                    // bodyStyle={{ height: 0 }}//marginTop: -50,
                    style={{ width: 400 }}
                    cover={this.getCover(item)}
                    actions={[
                      <Popconfirm
                        title={`确定要${item.delsign == 1 ? "上架" : "下架"}该图片吗?`}
                        onConfirm={() => { this.onClickDelete(item) }}
                        okText="Yes"
                        cancelText="No"
                      >
                        <Button type="link"><DeleteOutlined key="setting" />{item.delsign == 1 ? "上架" : "下架"}</Button>,
                      </Popconfirm>,
                      <Button type="link" onClick={() => { this.onClickEdit(item) }}><EditOutlined key="setting" />编辑</Button>,
                      <Button type="link" onClick={() => { this.onClickUpload(item) }}><UploadOutlined key="setting" />上传</Button>,
                    ]}
                  >
                    <Meta
                      title={"排序值: " + item.sort_id }
                      description={`状态: ${item.delsign == 1 ? "下架" : "正常"}` + " 文件大小: " + this.getShowImageSizeText(item.oss_url) }
                    />
                  </Card>
                </Col>

              );
            })}
          </Row>
        </PageHeaderWrapper>
        {imageModelType > 0 &&
          <PictureImageEditorModal
            imageModelType={imageModelType}
            currentPicture={currentPicture}
            onClickClose={this.onClickClose}
          />}
        {dataModelType > 0 &&
          <PictureEditorModal
            dataModelType={dataModelType}
            onClickClose={this.onClickClose}
            currentPicture={currentPicture}
          />}
      </Spin>
    );
  }

  renderHeader() {
    const { globalPlayerId } = this.props;
    const { user_noSearch, old_usernameSearch, new_usernameSearch, statusSearch, createtimeSearch, updatetimeSearch } = this.state;
    const colMd = 2;
    const colInputMd = 6;
    return (
      <>
        <Row style={{ width: 2000 }}>
          <Col md={colMd}>
            商铺名称/ID:
          </Col>
          <Col md={colInputMd} style={{ marginTop: -5, marginLeft: -40 }}>
            <AutoComplete
              allowClear={true}
              className={styles.content}
              options={getOptionList(this.props.merchantSimpleList)}
              onSelect={this.onSelectMerchant}
              onChange={this.onChangeMerchant}
              value={this.state.currentMerchantValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) =>
                option.value.indexOf(inputValue) !== -1
              }
            />
          </Col>
        </Row>
        <Row style={{ width: 2000 }}>
          <Col md={colInputMd} style={{ marginTop: 10 }}>
            <Button type="primary" icon={<UploadOutlined />} size="large" onClick={this.createPicture}>
              创建
            </Button>
          </Col>
        </Row>
        {/* <Row style={{ width: 1400, marginTop: 10}}>
          <Col md={colMd}>
            状态:
        </Col>
          <Col md={colInputMd} style={{ marginBottom: 12, marginLeft: 20 }}>
            <Radio.Group onChange={this.onChangeRadio} value={this.state.where}>
              {this.props.statusList.map((item, index) => {
                return (
                  <Radio value={item.whereScript}>
                    {item.name}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Col>
          <Col md={2} style={{ marginTop: -5 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={this.props.isSearching}
              onClick={this.handleSearch}
            >
              查询
          </Button>
          </Col>
        </Row> */}
      </>
    );
  }

  getCover = (item) => {
    if (item.oss_url != undefined && item.oss_url != null && item.oss_url != "") {
      return <Image src={item.oss_url} width={400} height={240} />
    } else {
      return <div style={{ width: 400, height: 120, textAlign: "center", marginTop: 120 }}>请上传图片</div>
    }
  }

  onClickDelete = (item) => {
    const { dispatch } = this.props;

    const delsign = item.delsign == 0 ? 1 : 0;
    item.delsign = delsign;
    dispatch({
      type: 'scriptkill/updatePicture',
      payload: item
    });
  }

  createPicture = () => {
    if (this.state.currentMerchantId == -1) {
      message.error("请先选择商铺");
      return;
    }

    this.setState({ dataModelType: 2, currentPicture: { merchant_id: this.state.currentMerchantId } });
  }

  public async getShowImageSize(url) {
    const response = await getImageSize(url);

    const pictureSizeList: any[] = this.state.pictureSizeList;
    pictureSizeList.push({ url, size: response });

    this.setState({ pictureSizeList });
  }

  getShowImageSizeText(url) {
    for (const item of this.state.pictureSizeList) {
      if (item.url == url) {
        let size = item.size;
        if (size > MB) {
          size = (size / MB).toFixed(2) + "MB";
        }else{
          size = (size / KB).toFixed(2) + "KB";
        }
        return size;
      }
    }
    return "";
  }

  onClickEdit = (item) => {
    this.setState({ dataModelType: 1, currentPicture: item });
  }

  onClickUpload = (item) => {
    this.setState({ imageModelType: 1, currentPicture: item });
  }

  onClickClose = () => {
    this.setState({ dataModelType: -1, imageModelType: -1, currentPicture: null });
  }

  onChangeMerchant = (data: string) => {
    this.setState({ currentMerchantValue: data });
  };

  onSelectMerchant = (value) => {
    const { dispatch } = this.props;
    const val = value.substring(4, value.indexOf(" 名称"));
    this.setState({ currentMerchantId: val, pictureSizeList: [] });
    dispatch({
      type: 'scriptkill/getPictureList',
      payload: { merchant_id: val }
    }).then(() => {
      if (this.props.pictureList != null && this.props.pictureList.length > 0) {
        for (const item of this.props.pictureList) {
          this.getShowImageSize(item.oss_url);
        }
      }
    });
  };

  dispatchMerchantSimpleList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getMerchantSimpleList',
    });
  };

  dispatchClearPictureList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/setPictureList',
      payload: [],
    });
  };

}
