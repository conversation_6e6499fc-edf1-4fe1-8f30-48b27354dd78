---
title: FooterToolbar
cols: 1
order: 6
---

A toolbar fixed at the bottom.

## Usage

It is fixed at the bottom of the content area and does not move along with the scroll bar, which is usually used for data collection and submission for long pages.

## API

Property | Description | Type | Default
---------|-------------|------|--------
children | toolbar content, align to the right | ReactNode | -
extra | extra information, align to the left | ReactNode | -