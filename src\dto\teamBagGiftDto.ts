export interface IinsertGiftBagParams {
  name: string,
  remark: string,
  max_num: number,
  coin_num: number,
  add_exp: number,
  sort: number,
  delsign: number,
}

export interface IupdateGiftBagParams extends  IinsertGiftBagParams{
  id: number
}

export interface IinsertGiftBagItemParams {
  gift_bag_id: number,
  item_dic_id: number,
  name: string,
  num: number,
  weight: number,
  percent: string,
  sort: number,
  delsign: number,
}

export interface IupdateGiftBagItemParams extends IinsertGiftBagItemParams{
  id: number
}


export interface IGiftBag {
  id: number,
  name: string,
  remark: string,
  max_num: number,
  coin_num: number,
  add_exp: number,
  sort: number,
  delsign: number,//1下架

  coin_id: number,
  coin_item_dic_id: number,
}

export interface IGiftBagItem {
  id: number,
  gift_bag_id: number,
  item_dic_id: number,
  name: string,
  num: number,
  weight: number,
  percent: string,
  sort: number,
  delsign: number,

  item_dic_name:string,
  item_cate_name:string,
  Item_cate_id:number,
  gift_bag_name:string,
}

export interface ITeamGiftItemDic {
  id: number,
  name:string,
  item_cate_id:number,
  item_cate_name:string,

  item_id:number,
}
