// tslint:disable-next-line:no-var-requires
/**
 * @name: manager数据库建模
 * @msg:
 * @param {type}
 * @return:
 */
export interface Igift {
	no: number;
	name: string;
	price: number;
	reprice: number;
	timeprice: number;
	charm: number;
	prive_remark: string;
	props_explain: string;
	MD5: string;
	datatime: string;
	img_name: string;
	gif_name: string;
	gift_type: number;
	power_value: number;
	gift_level_one_num: number;
	gift_level_two_num: number;
	gift_super: number;
	gift_check_num: number;
	nobleLevel: number;
	show: number;
	hot: number;
	priority: number;
	delsign: number;
	cateId: number;
	gift_source: number;
	box_level: number;
	min_charm: number;
	max_charm: number;
	add_in: number;
	g_give: number;
  team_exp: number;
  user_exp: number;
  }

  export interface IitemDic {
	id: number;
	item_cate_id: number;
	item_id: number;
	name: string;
	pic: string;
	icon: string;
	remark: string;
	version: number;
	delsign: number;
	time: number;
	cateId: number;
  }

  export interface IitemCate {
	id: number;
	item_catalog_id: number;
	cls: number;
	type: string;
	name: string;
	remark: string;
	once: number;
	time_limit: number;
	consume: number;
	item_table: string;
	item_user_table: string;
  }

  export interface Ianimation {
	id: number;
	name: string;
	role: number;
	type: number;
	isLight: number;
	bg: number;
	timer: number;
	price: number;
	show: number;
	buy: number;
	sort: number;
	createtime: string;
	datePath: string;
	serialNumber: string;
	alert: string;
	hot: number;
	remark: string;
	channel: number;
	delsign: number;
  }

  export interface IuploadImg {
	id : number;
	url: string;
	name: string;
	status: boolean;
	type: number;
	fileExtension: string;
	showName: string;
	needInsertDocument: number;
  }

  export interface IdocumentResourceReq {
	document: string;
	name: string;
	extension: string;
	type: number;
  }

  export interface Imaskshow {
	no: number;
	name: string;
	prive_remark: string;
	props_explain: string;
    datatime: string;
	priority: number;
	buy: number;
	delsign: number;
	show: number;
	hot: number;
	md5: string;
  }

  export interface Igroupbadge {
	id: number;
	name: string;
  }

  export interface IgroupFrame {
	id: number;
	name: string;
  }

  export interface IgroupBanner {
	id: number;
	name: string;
  }

  export interface IavatarFrame {
	id: number;
	name: string;
  }

  export interface InormalItem {
	no: number;
	name: string;
	role: number;
	unit: number;
	unit_num: number;
	price: number;
	datatime: string;
	img_name: number;
	nobleLevel: number;
	show: number;
	props_explain: string;
	priority: number;
	delsign: number;
  }

  export interface ICustomTagItem {
	id: number;
	name: string;
	img: string;
	remark: string;
	create: string;
	delsign: string;
  }

  export interface ICustomTagTextItem {
	id: number;
	font_name: string;
	color_a: string;
	color_b: string;
	color_border: string;
	remark: string;
	create: string;
	delsign: string;

	fontId: string;
	fontFileRemark: string;
  }

  export interface IKeepSakeItem {
	id: number;
	item_id: string;
	item_cate_id: string;
	name: string;
	pic: string;
	icon: string;
	remark: string;
	animation_id: string;
	type: string;
	isLight: string;
	bg: string;
	timer: string;
	price: string;
	show: string;
	buy: string;
	sort: string;
	createtime: string;
	alert: string;
	hot: string;
	channel: string;
	buff: string;
	delsign: string;

  }

  export interface IgroupProps {
	id: number;
	type: number;
	name: string;
  }

  export interface IgiftBag {
	no: number;
	name: string;
  }

  export interface Irole {
	id: number;
	name: string;
	remark: string;
	sort: number;
  }

  export enum EanimationRoleEnum {
	microphone = 1000,
	policeBadge = 999,
	backgroundSysRole = 2000,
	cpSpecial = 995,
	cpBackground = 996,
	nobilitySpecial = 994,
	systemAudio = 993,
	chatPop = 998,
	chatBackground = 997,
	roleAnimationMax = 100,
	roleVoiceMax = 200,
  }

  export enum ItemCateEnum {
	microphone = 1010,
	policeBadge = 1020,
	backgroundSysRole = 1031,
	maskshow = 1040,
	groupBadge = 1051,
	groupFrame = 1052,
	groupBanner = 1053,
	cpSpecial = 1060,
	nobilitySpecial = 1070,
	systemAudio = 1080,
	chatPop = 1091,
	chatBackground = 1092,
	cpBackground = 1093,
	avatarFrame = 2010,
	cpAvatarFrame = 2020,
	roleAnimation = 3010,
	roleVoice = 3020,
	diamondGift = 4010,
	cpGift = 4020,
	otherGift = 4030,
	normalItem = 5010,
	groupProps = 5020,
	giftbag = 6000,
  }


  export enum EbagkgroundEnum {
	notBackground = 0,
	backgroundSysBg = 1,
	backgroundSysSelf = 2
  }
