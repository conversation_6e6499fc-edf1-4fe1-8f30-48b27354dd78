
export interface ISeasonGuideItem {
    id:number
    name:string
    description:string
    start_time:string
    end_time:string
}
export interface ISeasonContentItem{
    id:number
    s_no:number,
    level:number,
    name:string,
    desc:string,
    img?:string,
    detail_img?:string,
}
export interface ISeasonGuide{
    seasonGuideList: ISeasonGuideItem[]
    seasonContentList: ISeasonContentItem[]
    
}
export interface ISeasonGuideResponse{
    name:string
    start_time:string
    end_time:string
}
export interface ISeasonGuideContentResponse{
    id:number
    level:string,
    name:string,
    desc:string,
}
export interface ISeasonGuideRequest{
  
}
