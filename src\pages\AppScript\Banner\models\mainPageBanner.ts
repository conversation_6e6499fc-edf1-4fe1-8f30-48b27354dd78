/*
 * @Description //
 * @version 1.0.0
 * @Company sdbean
 * <AUTHOR>
 * @Date
 **/

import { StandardViewModelType } from '@/app';
import {
  delBannerInfo, delOpenAd,
  getMerchantBanner, getOpenAdList, insertMainPageBanner, insertOpenAd,
  shelvesBanner, shelvesOpenAd,
  updateBannerInfo,
  updateMainPageBannerUrl, updateOpenAdImgUrl, updateOpenAdInfo,
} from '@/services/apiScriptkill';

export interface MerchantBanner {
  id: number;
  sort_id: number;
  banner_name: string;
  type: number;
  href_url: string;
  banner_starttime: string;
  banner_endtime: string;
  delsign: number;
  banner_url: string;
  create_time: string;
}

export interface OpenAd {
  id: number;
  open_ad_url: string;
  open_ad_name: string;
  type: number;
  href_url: string;
  open_ad_start_time: string;
  open_ad_end_time: string;
  delsign: number;
}

export interface IMainPageBanner {
  merchantBannerList: MerchantBanner[];
  merchantBanner: MerchantBanner;
  count: number;
  isNewBannerModal: boolean;
  isShowEditImg: boolean;
  openAdList: OpenAd[];
  currentOpenAd: OpenAd;
}

const init: IMainPageBanner = {
  merchantBannerList:[],
  count: 0,
  isNewBannerModal: false,
  openAdList:[],
  currentOpenAd:{
    id: 0,
    open_ad_url: "",
    open_ad_name: "",
    type: 0,
    href_url: "",
    open_ad_start_time: "",
    open_ad_end_time: "",
    delsign: 1,
  },
  merchantBanner:{
    id: 0,
    sort_id: 0,
    banner_name: "",
    type: 0,
    href_url: "",
    banner_starttime: "",
    banner_endtime: "",
    delsign: 1,
    banner_url: "",
    create_time: "",
  },
  isShowEditImg: false,
}

const model:StandardViewModelType = {

  namespace: 'mainPageBanner',
  state: init,
  effects: {
    * getMerchantBanner( {payload}, {call, put}) {
      const response = yield call(getMerchantBanner, payload);
      //更新基础状态
      if (response) {
        yield put({ type: 'setMerchantBanner', payload: response });
       }
      else {
        yield put({ type: 'setMerchantBanner', payload: null });
      }
    },
    *delBannerInfo( {payload}, {call, put}) {
      const response = yield call(delBannerInfo, payload);
      const resp = yield call(getMerchantBanner, payload);
      if (response) {
        yield put({ type: 'setMerchantBanner', payload: resp });
      }
    },
    *shelvesBanner( {payload}, {call, put}) {
      const response = yield call(shelvesBanner, payload);
      const resp = yield call(getMerchantBanner, payload);
      if (response) {
        yield put({ type: 'setMerchantBanner', payload: resp });
      }
    },
    *updateBannerInfo( {payload}, {call, put}) {
      const response = yield call(updateBannerInfo, payload);
      if (response) {
        yield put({ type: 'setIsNewBannerModal', payload: false });
        const response = yield call(getMerchantBanner, payload);
        //更新基础状态
        if (response) {
          yield put({ type: 'setMerchantBanner', payload: response });
        }
        else {
          yield put({ type: 'setMerchantBanner', payload: null });
        }
      }
    },
    *updateMainPageBannerUrl({payload}, {call, put}) {
      const response = yield call(updateMainPageBannerUrl, payload);
      if (response) {
        const response = yield call(getMerchantBanner, payload);
        //更新基础状态
        if (response) {
          yield put({ type: 'setMerchantBanner', payload: response });
        }
        else {
          yield put({ type: 'setMerchantBanner', payload: null });
        }
      }
    },
    *insertMainPageBanner({payload}, {call, put}) {
      const response = yield call(insertMainPageBanner, payload);
      if (response) {

        const response = yield call(getMerchantBanner, payload);
        //更新基础状态
        if (response) {
          yield put({ type: 'setMerchantBanner', payload: response });
        } else {
          yield put({ type: 'setMerchantBanner', payload: null });
        }
      }
    },
    *getOpenAdList( {payload}, {call, put}) {
      const response = yield call(getOpenAdList, payload);
      if (response) {
        yield put({ type: "setOpenAdList", payload: response});
      } else {
        yield put({ type: "setOpenAdList", payload: null});
      }
    },
    *shelvesOpenAd( {payload}, {call, put}) {
      const response = yield call(shelvesOpenAd, payload);
      if (response) {
        const response = yield call(getOpenAdList, payload);
        if (response) {
          yield put({ type: "setOpenAdList", payload: response});
        } else {
          yield put({ type: "setOpenAdList", payload: null});
        }
      }
    },
    *delOpenAd( {payload}, {call, put}) {
      const response = yield call(delOpenAd, payload);
      if (response) {
        const response = yield call(getOpenAdList, payload);
        if (response) {
          yield put({ type: "setOpenAdList", payload: response});
        } else {
          yield put({ type: "setOpenAdList", payload: null});
        }
      }
    },
    *insertOpenAd({payload}, {call, put} ) {
      const response = yield call(insertOpenAd, payload);
      if (response) {
        const response = yield call(getOpenAdList, payload);
        if (response) {
          yield put({ type: "setOpenAdList", payload: response});
        } else {
          yield put({ type: "setOpenAdList", payload: null});
        }
      }
    },
    *updateOpenAdImgUrl({payload}, {call, put} ) {
      const response = yield call(updateOpenAdImgUrl, payload);
      if (response) {
        const response = yield call(getOpenAdList, payload);
        if (response) {
          yield put({ type: "setOpenAdList", payload: response});
        } else {
          yield put({ type: "setOpenAdList", payload: null});
        }
      }
    },
    *updateOpenAdInfo({payload}, {call, put} ) {
      const response = yield call(updateOpenAdInfo, payload);
      if (response) {
        const response = yield call(getOpenAdList, payload);
        if (response) {
          yield put({ type: "setOpenAdList", payload: response});
        } else {
          yield put({ type: "setOpenAdList", payload: null});
        }
      }
    },

  },
  reducers: {
    setMerchantBanner(state: IMainPageBanner, {payload}) {
      return {...state, merchantBannerList : payload};
    },
    setIsNewBannerModal(state: IMainPageBanner, {payload}) {
      return {...state, isNewBannerModal : payload};
    },
    setBanner(state: IMainPageBanner, {payload}) {
      return {...state, merchantBanner: payload};
    },
    setIsShowEditImg(state: IMainPageBanner, {payload}) {
      return {...state, isShowEditImg: payload}
    },
    setOpenAdList(state: IMainPageBanner, {payload}) {
      return {...state, openAdList : payload}
    },
    setCurrentAd(state: IMainPageBanner, {payload}) {
      return {...state, currentOpenAd: payload}
    }
  },
};

export default model
