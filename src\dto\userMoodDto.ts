import { number } from 'prop-types';
export interface IUserMoodItem {
    id: number,
    user_id: number,
    mood: string,
    animation_id: number,
    updatetime: string,
    nickname: string,
    ban_delsign: number, //null:没永久封禁, 0:永久封禁, 1:解除永久封禁
    imprison_release_time: number, //null: 没限时封禁，other：限时封禁解除时间
}

export interface IUserMoodUpdateParams {
    id: number,
    mood: string,
    user_id?: number,
    ban?: number,
    ban_duration?: number,
    ban_reason?: string,
}