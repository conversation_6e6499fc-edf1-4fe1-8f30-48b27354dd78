/*
 * @Description: 剧本杀首页banner管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: 赵宝强
 * @LastEditTime: 2020-12-21 14:17:50
 */

import { ILogin } from '@/models/login';
import React, { Component } from 'react';
import { connect } from 'dva';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { Button, Card, message, Popconfirm, Spin, Table } from 'antd';
import { DeleteOutlined, FormOutlined, UploadOutlined } from '@ant-design/icons';
import NewBannerModal from '@/pages/AppScript/Banner/NewBannerModel';
import BannerEditPage from '@/pages/AppScript/Banner/BannerEditPage';
import * as styles from '@/pages/AppWolf/AppManager/NewBanner.less';
import { ColumnProps } from 'antd/lib/table';
import { IMainPageBanner, MerchantBanner } from '@/pages/AppScript/Banner/models/mainPageBanner';
import moment from 'moment';
import ShowDetails from '@/pages/AppWolf/ScoreViolation/ShowDetails';
import BannerImageEditModel from '@/pages/AppScript/Banner/BannerImageEditModel';

const PAGESIZE = 6;
interface IMainPageBannerState {
  isNewBannerModal: boolean,
  currentPage: number,
  isShowEditModal: boolean,
  imageModelType: number,
  isShowUploadImage: boolean,
  // clickUploadData: any,
  // imageUrl: string,
  // ossImageName: string,
  currentScript: MerchantBanner
}

export interface MainPageBannerProps {
  merchantBannerList: MerchantBanner[],
  isLoding: boolean,
  count:number,
  isShowEditModal: boolean,
  merchantBanner: MerchantBanner,
  isShowEditImg: boolean,
}
@connect(({ loading, login, mainPageBanner}: { loading: IdvaLoading; login: ILogin; mainPageBanner: IMainPageBanner}) => ({
  uid: login.uid,
  isLoading: loading.models['mainPageBanner'],
  merchantBannerList: mainPageBanner.merchantBannerList,
  count: mainPageBanner.count,
  isShowEditModal: mainPageBanner.isNewBannerModal,
  merchantBanner: mainPageBanner.merchantBanner,
  isShowEditImg: mainPageBanner.isShowEditImg,
}))

export default class MainPageBanner extends Component<MainPageBannerProps, IMainPageBannerState> {
  constructor(props) {
    super(props);
    this.state = {
      isNewBannerModal: false,
      currentPage: 1,
      currentScript: null,
      imageModelType: -1,
    }
    props.dispatch({
      type: 'mainPageBanner/getMerchantBanner'
    });
  }


  render() {
    const { merchantBannerList , count, isShowEditModal, isShowEditImg} = this.props;
    const { isNewBannerModal, currentPage} = this.state;

    // @ts-ignore
    return (
      <div>
        <Spin spinning={!!this.props.isLoading} >
          {isShowEditModal  && <BannerEditPage />}
          {isShowEditImg && <BannerImageEditModel/>}
          <PageHeaderWrapper title="首页banner图管理" content={this.renderHeader()} >
            <div style={{ marginTop: 20 }}>
              <Card>
                <Table
                  title={() => 'banner图管理'}
                  columns={this.merchantBanner1}
                  dataSource={merchantBannerList}
                  rowKey={(record, index) => index.toString()}
                  scroll={{ x: 2000 }}
                  pagination={{
                    // 分页
                    pageSize: PAGESIZE,
                    current: currentPage,
                    total: count,
                    onChange: this.changePage
                  }}
                />
              </Card>
            </div>
          </PageHeaderWrapper>
        </Spin>
        <NewBannerModal isNewBannerModal={isNewBannerModal} onClose={this.handleCancelUpModal} />

      </div>

    );
  }

  renderHeader() {
    return (
      <div>
        <Button type="primary" icon={<UploadOutlined />} size="large" onClick={this.handleClickUpload}>
          banner上传
        </Button>
      </div>
    );
  }

  //上传banner
  handleClickUpload = () => {
    console.log('点击上传');
    this.setState({ isNewBannerModal: true });
  };
  handleCloseEdit = () => {
    this.setState({ isShowEditModal: false });
  };
  onClickEdit = (data: any) => {
    // @ts-ignore
    const { dispatch } = this.props;
    dispatch({
      type: 'mainPageBanner/setIsNewBannerModal',
      payload:
         true
    });
    dispatch({
      type: 'mainPageBanner/setBanner',
      payload:
        data
    });
  }
  //关闭上传模态页
  handleCancelUpModal = () => {
    console.log('关闭上传模态页');
    this.setState({ isNewBannerModal: false });
  };
  changePage = (page) => {
    this.setState({ currentPage: page });
  };

  editImageBanner= (script) => {

    const { dispatch } = this.props;
    dispatch({
      type: 'mainPageBanner/setBanner',
      payload:
      script
    });

    dispatch({
      type: 'mainPageBanner/setIsShowEditImg',
      payload: true
    });

  };


  private merchantBanner1: ColumnProps<MerchantBanner>[] = [
      {
        title: '排序序号',
        width: 30,
        align: 'center',
        dataIndex: 'sort_id',
        fixed: true
      },
    {
      title: '是否展示',
      width: 30,
      align: 'center',
      dataIndex: 'delsign',
      fixed: true,
      render: (val) => {
        return (
          <div style={val == 0 ? { color: 'rgb(0, 200, 83)' } : { color: 'red' }}>
            {val == 0 ? '展示' : '不展示'}
          </div>
        );
      }
    },
      {
        title: '名称',
        width: 30,
        align: 'center',
        dataIndex: 'banner_name',
      },
      {
        title: '图片展示',
        width: 100,
        align: 'center',
        dataIndex: 'banner_url',
        render: (val, record) => {
          if (val != null && val != '') {
            return <img className={styles.boxImg} src={val} />;
          } else {
            return (

              <Button
                type="primary"
                icon={<UploadOutlined />}
                size="middle"
                onClick={() => {
                  this.setState({
                    isShowUploadImage: true,
                    clickUploadData: record,
                    ossImageName: `${record.id}_${new Date().getTime()}_banner`
                  });
                }}
              >
                上传图片
              </Button>
            );
          }
        }
      },

    {
      title: '操作',
      width: 60,
      // align: 'center',
      render: (val, record) => {
        let isShowShang: boolean = false
        let isShowXia: boolean = false
        if (record.delsign == 0) {
          isShowShang = false
          isShowXia = true
        } else {
          isShowShang = true
          isShowXia = false
        }
        return (
          <div>
            <div style={{marginBottom:8}} >
            <Button
              type="primary"
              onClick={() => this.onClickEdit(record)}
            >
              编辑
            </Button>
              {isShowXia &&(
                <Popconfirm
                  title="确定要下架当前banner吗?"
                  onConfirm={() => {
                    this.props.dispatch({
                      type: 'mainPageBanner/delBannerInfo',
                      payload: {
                        id: record.id,
                        callback: () => {
                          message.success('下架成功');
                        }
                      }
                    });
                    console.log('下架banner');
                  }}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button type="primary" size={'middle'} danger={true} style={{marginLeft: 10}}>
                    下架
                  </Button>
                </Popconfirm>
              )}
              {( isShowShang &&
                <Popconfirm
                  title="确定要上架当前banner吗?"
                  onConfirm={() => {
                    this.props.dispatch({
                      type: 'mainPageBanner/shelvesBanner',
                      payload: {
                        id: record.id
                      }
                    });
                  }}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button type="primary" size="middle" danger={true} style={{marginLeft: 10}}>
                    上架
                  </Button>
                </Popconfirm>
              )}
            </div>
            <div style={{marginTop:15}}>
            <Button
              type="primary"
              icon={<FormOutlined />}
              style={{width:140}}
              onClick={() => this.editImageBanner(record)}

            >
              更新图片
            </Button>
            </div>
          </div>
        );
      }
    },
      {
        title: '跳转类型',
        width: 80,
        align: 'center',
        dataIndex: 'type',
        render: (val, record) => {
          switch (record.type) {
            case 1:
              return <div>店铺</div>
            case 2:
              return <div>剧本</div>
            case 3:
              return <div>图片跳转</div>
            case 4:
              return <div>文章</div>
            case 5:
              return <div>订单</div>
            default:
              return <div>未知类型</div>
          }
        }
      },
      {
        title: '跳转地址',
        width: 50,
        align: 'center',
        dataIndex: 'href_url',
      },
      {
        title: '开始时间',
        width: 120,
        align: 'center',
        dataIndex: 'banner_starttime',
        render: (val, record) => {
          return <div>{moment(record.banner_starttime, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss')}</div>
        },
      },
      {
        title: '结束时间',
        width: 120,
        align: 'center',
        dataIndex: 'banner_endtime',
        render: (val, record) => {
          return <div>{moment(record.banner_endtime, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss')}</div>
        },
      },


    ];


}
