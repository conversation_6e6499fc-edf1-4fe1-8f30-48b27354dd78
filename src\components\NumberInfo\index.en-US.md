---
title: NumberInfo
cols: 1
order: 10
---

Often used in data cards for highlighting the business data.

## API

Property | Description | Type | Default
----|------|-----|------
title | title | ReactNode\|string | -
subTitle | subtitle | ReactNode\|string | -
total | total amount | ReactNode\|string | -
subTotal | total amount of additional information | ReactNode\|string | -
status | increase state | 'up \| down' | -
theme | state style | string | 'light'
gap | set the spacing (pixels) between numbers and descriptions | number | 8
