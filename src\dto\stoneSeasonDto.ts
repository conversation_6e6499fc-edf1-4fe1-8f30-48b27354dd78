
export interface IMiningSeasonItem {
    id: number,
    name: string,
    delsign: number,
    start_time: string,
    end_time: string,
}

export interface IMiningIllustrationItem {
    id: number,
    level: string,
    s_no: string
    name: string,
    desc: string,
    source: number,
    last_id: number,//上层id
    is_show: number,
    task_mission_id: number,
    task_type: number,
    img: string,

    delsign: number,

}

export interface IInsertStoneSeasonParams {
    name: string,
    start_time: string,
    end_time: string,
    delsign: number,
}

export interface IUpdateStoneSeasonParams extends IInsertStoneSeasonParams {
    id: number,
}

export interface IUpdateIllustrationParams {
    id: number,
    name: string,
    level: string,
    s_no: string,
    desc: string,
    source: number,
    img: string,
}