/*
 * @Description: 成就上传模态页
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-04-02 10:15:10
 * @LastEditors: hammercui
 * @LastEditTime: 2020-08-31 21:50:04
 */
import React, { Component, Fragment } from 'react';
import Result from '@/components/Result';
import * as styles from './UploadModal.less';
import {
	IuserAvatarFrame,
	IuploadAvatarFrameRequest,
	IavatarFrameImgResponse,
	IavatarFrameImgRequest,
	IavatarFrameImg,
	IuploadAchieveBaseResponse,
	IuploadAchieveBaseRequest,
	Iachievement,
	IuploadAchieveCompleteRequest
} from '@/dto/werewolf';
import { connect } from 'dva';
import { ILogin } from '@/models/login';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Upload, Button, message, Modal, Steps, Input, Select, Row, Col, Divider, Card } from 'antd';
import { UploadChangeParam } from 'antd/lib/upload';
import request from '@/utils/request';
import { Iavframe } from './models/avframe';
import {
	UploadAvFramStep,
	wfAvatarFrameType,
	wfAvatarFrameChannel,
	wfAvatarFrameDynamic,
	UploadAchieveStep
} from '@/dto/staticEnum';
import { FormComponentProps } from '@ant-design/compatible/lib/form/Form';
import { RouteComponentProps } from 'react-router-dom';
import { Iachieve } from './models/achieve';
import { UploadFile } from 'antd/lib/upload/interface';
import { Icoin2wItem } from '@/dto/wf2wCoin';
const Step = Steps.Step;
const Option = Select.Option;
const { Meta } = Card;

export interface UploadAchiModalProps extends RouteComponentProps, FormComponentProps {
	uid?: number;
	isBaseing: boolean;
	isCompleteing: boolean;
	dispatch?: Function;
	isUploadModal: boolean;
	onClose: () => void;
	step: UploadAchieveStep;
	baseInfo: Iachievement;
	selectCoin2w?: Icoin2wItem;
	onOkModal?:()=>void;
	isHeadNote:boolean;//刻字上传成就
}

export interface UploadAchiModalState {
	//isUploading: boolean;
	// isUploadDone:boolean;
	fileList: Array<UploadFile>;
	previewVisible: boolean;
	previewImage: string;
}

@connect(({ loading, login, achieve }: { loading: IdvaLoading; login: ILogin; achieve: Iachieve }) => ({
	uid: login.uid,
	baseInfo: achieve.uploadBase,
	isBaseing: loading.effects['achieve/uploadBase'],
	isCompleteing: loading.effects['achieve/fetchComplete'],
	step: achieve.uploadStep
}))
class UploadAchieveModal extends Component<UploadAchiModalProps, UploadAchiModalState> {
	constructor(props) {
		super(props);
		const fileList = [];
		if (props.baseInfo && props.baseInfo.url) {
			const defaultItem = { url: this.props.baseInfo.url };
			fileList[0] = defaultItem;
		}
		this.state = {
			//isUploading: false,
			// isUploadDone:false,
			fileList,
			previewVisible: false,
			previewImage: ''
		};
	}

	render() {
		const { isUploadModal, step } = this.props;
		const title = '新建成就';
		return (
			<Modal visible={isUploadModal} onCancel={this.handleCloseModal} title={title} footer={null} width={1200}>
				{this.renderUploadSteps()}
				{step == UploadAchieveStep.Base && this.renderStepDefault()}
				{step == UploadAchieveStep.Upload && this.renderStepUpload()}
				{step == UploadAchieveStep.Success && this.renderStepSuccess()}
			</Modal>
		);
	}

	/**
	 * 关闭界面先清理表单中的数据
	 */
	handleCloseModal = () => {
		this.props.form.resetFields();
		this.props.onClose();
	};

	/**
	 * 步骤条
	 */
	renderUploadSteps() {
		return (
			<Steps current={this.props.step}>
				<Step title="基础信息" description="填写成就基础信息" />
				<Step title="上传图片" description="上传成就对应图片" />
				<Step title="成功" description="成就上传成功" />
			</Steps>
		);
	}

	/**
	 * Step1 填写头像框信息表单
	 */
	renderStepDefault() {
		const { getFieldDecorator } = this.props.form;
		const formItemLayout = {
			labelCol: {
				span: 5
			},
			wrapperCol: {
				span: 19
			}
		};
		return (
			<div className={styles.step_div}>
				<Form layout="horizontal" className={styles.stepForm} onSubmit={this.handleSubmitStepDefault}>
					<Form.Item {...formItemLayout} label="成就名称">
						{getFieldDecorator('name', {
							rules: [
								{
									type: 'string',
									message: '请输入成就名称'
								},
								{
									required: true,
									message: '请输入成就名称'
								}
							]
						})(<Input />)}
					</Form.Item>
					<Form.Item {...formItemLayout} label="成就描述">
						{getFieldDecorator('remark', {
							rules: [
								{
									type: 'string',
									message: '请输入成就描述'
								},
								{
									required: true,
									message: '请输入成就描述'
								}
							]
						})(<Input.TextArea rows={2} />)}
					</Form.Item>
					{/* <Form.Item
            {...formItemLayout}
            label="类型"
          >
            {getFieldDecorator('type', {
              initialValue: wfAvatarFrameType.custom,
              rules: [{
                required: true, message: '类型',
              }],
            })(
              <Select>
                <Option value={wfAvatarFrameType.restrict}>限定</Option>
                <Option value={wfAvatarFrameType.props}>道具</Option>
                <Option value={wfAvatarFrameType.custom}>定制</Option>
              </Select>
            )}
          </Form.Item>
          <Form.Item
            {...formItemLayout}
            label="渠道"
          >
            {getFieldDecorator('channel', {
              initialValue: wfAvatarFrameChannel.default,
              rules: [{
                required: true, message: '渠道',
              }],
            })(
              <Select>
                <Option value={wfAvatarFrameChannel.default}>普通商城</Option>
                <Option value={wfAvatarFrameChannel.boxProps} disabled>宝箱兑换商城</Option>
                <Option value={wfAvatarFrameChannel.groupProps} disabled>公会商城</Option>
                <Option value={wfAvatarFrameChannel.cpProps} disabled>cp商城</Option>
                <Option value={wfAvatarFrameChannel.activityProps} disabled>活动</Option>
              </Select>
            )}
          </Form.Item>
          <Form.Item
            {...formItemLayout}
            label="头像框描述"
          >
            {getFieldDecorator('remark', {
              rules: [{
                type: 'string', message: '请输入头像框描述',
              }, {
                required: true, message: '请输入头像框描述',
              }],
            })(
              <Input />
            )}
          </Form.Item>
          <Form.Item
            {...formItemLayout}
            label="价格"
          >
            {getFieldDecorator('price', {
              initialValue: 0,
              rules: [
                { required: true, message: '请输入价格金额' },
                {
                  pattern: /^(\d+)((?:\.\d+)?)$/,
                  message: '请输入合法金额数字',
                },
              ],
            })(
              <Input prefix="￥" placeholder="请输入金额" />
            )}
          </Form.Item>
          <Form.Item
            {...formItemLayout}
            label="是否动态"
          >
            {getFieldDecorator('is_dynamic', {
              initialValue: wfAvatarFrameDynamic.un,
              rules: [{
                required: true, message: '是否动态',
              }],
            })(
              <Select>
                <Option value={wfAvatarFrameDynamic.un}>静态</Option>
                <Option value={wfAvatarFrameDynamic.in}>动态</Option>
              </Select>
            )}
          </Form.Item> */}
					<Form.Item>
						<div className={styles.submit_btn}>
							<Button type="primary" htmlType="submit" loading={!!this.props.isBaseing}>
								下一步
							</Button>
						</div>
					</Form.Item>
				</Form>
			</div>
		);
	}

	/**
	 * 点击了第一步提交基础信息的下一步
	 */
	handleSubmitStepDefault = e => {
		e.preventDefault();
		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const request: IuploadAchieveBaseRequest = {
					uid: this.props.uid,
					name: values.name,
					// type: values.type,
					// channel: values.channel,
					remark: values.remark
					// price: values.price,
					// is_dynamic: values.is_dynamic
				};
				this.props.dispatch({
					type: 'achieve/uploadBase',
					payload: request
				});
				//this.setState({isUploadDone:false});
			}
		});
		this.props.form.resetFields();
	};

	/**
	 * Step2 上传头像框所需图片
	 */
	renderStepUpload() {
		const { baseInfo, isCompleteing } = this.props;
		const { fileList } = this.state;
		const isUploadDone = fileList.length >= 1;
		// if (process.env.NODE_ENV === 'production') {
		//   url = 'https://werewolf.53site.com/Werewolf/CustomerService/data/uploadImgTest.php';
		// }
		return (
			<div className={styles.step_div}>
				{this.renderStaticResource()}
				{/* {baseInfo.is_dynamic == wfAvatarFrameDynamic.in && this.renderDynamicResource()} */}
				<div className={styles.submit_btn}>
					<Button
						disabled={!isUploadDone}
						type="primary"
						onClick={this.handleSubmitStepUpload}
						loading={!!isCompleteing}
					>
						下一步
					</Button>
				</div>
			</div>
		);
	}

	/**
	 * 上传按钮
	 */
	renderUploadButton = () => {
		return (
			<div>
				<LegacyIcon type={'plus'} />
				<div className="ant-upload-text">上传</div>
			</div>
		);
	};

	/**
	 * 静态资源上传
	 */
	renderStaticResource = () => {
		// const { baseImgInfo } = this.props;
		//const dataArray = baseImgInfo.dataArray.slice(0,4);
		const { baseInfo } = this.props;
		const fileName = 'achieve_' + baseInfo.id;
		return (
			<div className={styles.resources_div}>
				<Divider>静态资源</Divider>
				<Row className={styles.row_div} type="flex" justify="space-around">
					<Col span={4}>{this.renderUploadModel(fileName)}</Col>
					{/* {
            dataArray.map((item, index) => {
              return (
                <div key={index}>
                  <Col span={4}>
                  {!!item.status && this.renderShowImgModel(item.url, item.name)}
                  {!item.status && this.renderUploadModel(item.name)}
                  </Col>
                </div>
              );
            })
          } */}
				</Row>
			</div>
		);
	};

	/**
	 * 动态资源上传
	 */
	// renderDynamicResource = () => {
	//   const { baseImgInfo } = this.props;
	//   const dataArray = baseImgInfo.dataArray.slice(4,8);
	//   return (
	//     <div className={styles.resources_div}>
	//       <Divider>动态资源</Divider>
	//       <Row className={styles.row_div} type="flex" justify="space-around">
	//       {
	//           dataArray.map((item, index) => {
	//             return (
	//               <div key={index}>
	//                 <Col span={4}>
	//                 {item.status && this.renderShowImgModel(item.url, item.name)}
	//                 {!item.status && this.renderUploadModel(item.name)}
	//                 </Col>
	//               </div>
	//             );
	//           })
	//         }
	//       </Row>
	//     </div>
	//   );
	// }

	/**
	 * 展示图片模块
	 */
	// renderShowImgModel = (url, name) => {
	// 	return (
	// 		<div className={styles.show_img_div}>
	// 			<img className={styles.show_img_div} src={url} />
	// 			<div className={styles.text_div}>{name}</div>
	// 		</div>
	// 	);
	// };

	/**
	 * 上传模块
	 */
	renderUploadModel = name => {
		const uploadUrl = '/megaupload';
		const { previewVisible, previewImage, fileList } = this.state;
		return (
			<div>
				<Upload
					action={uploadUrl}
					headers={{ 'Access-Control-Allow-Origin': '*', 'X-Requested-With': null }}
					withCredentials={true}
					data={this.postUpdata(name)}
					listType="picture-card"
					fileList={fileList}
					// showUploadList={false}
					className={styles.uploadlistInline}
					beforeUpload={this.beforeUpload}
					onPreview={this.handlePreview}
					onChange={this.onChange}
				>
					{fileList.length >= 1 ? null : this.renderUploadButton()}
				</Upload>
				<Modal visible={previewVisible} footer={null} onCancel={this.handleCancel}>
					<img alt="example" style={{ width: '100%' }} src={previewImage} />
				</Modal>
				<div className={styles.text_div}>{name}</div>
			</div>
		);
	};

	//取消图片预览
	handleCancel = () => this.setState({ previewVisible: false });

	/**
	 * 上传前校验图片格式
	 */
	beforeUpload = file => {
		const isPng = file.type === 'image/png';
		if (!isPng) {
			message.error('你只能上传png格式的图片!');
		}
		const isLt2M = file.size / 1024 / 1024 < 2;
		if (!isLt2M) {
			message.error('你只能上传2M以内的图片!');
		}
		const isCan = isPng && isLt2M;
		if (!isCan) {
			this.setState({ fileList: [] });
		}
		return isCan;
	};

	/**
	 * 是否显示预览
	 */
	handlePreview = (file: UploadFile) => {
		this.setState({
			previewImage: file.url || file.thumbUrl,
			previewVisible: true
		});
	};

	/**
	 * 上传图片时图片变换
	 * @param info
	 */
	onChange = (info: UploadChangeParam) => {
		const { baseInfo } = this.props;
		console.log('上传中', info);
		if (info.file.type == 'image/png') {
			this.setState({ fileList: info.fileList });
		}
		if (info.file.status === 'done') {
			console.log('上传成功', info);
			message.success(`${info.file.name} 上传成功`);
		} else if (info.file.status === 'error') {
			console.log('上传失败', info);
			this.setState({ fileList: [] });
			message.error(`${info.file.name} 上传失败`);
		}
	};

	/**
	 * 上传图片时的post数据
	 */
	postUpdata = name => {
		const obj = {
			type: 1,
			name
		};
		return obj;
	};

	/**
	 * 点击了上传步骤的下一步
	 */
	handleSubmitStepUpload = () => {

		const { baseInfo, uid,onOkModal,isHeadNote } = this.props;
		const request: IuploadAchieveCompleteRequest = {
			achieveId: baseInfo.id,
			uid
		};
		if(this.props.selectCoin2w){
			request.selectCoin2w = this.props.selectCoin2w
		}
		if(isHeadNote){
      this.props.dispatch({
        type: 'achieve/fetchNoteComplete',
        payload: request
      }).then(()=>{
        if(onOkModal){
          onOkModal()
        }
      });
    }else{
      this.props.dispatch({
        type: 'achieve/fetchComplete',
        payload: request
      }).then(()=>{
        if(onOkModal){
          onOkModal()
        }
      });
    }

	};

	/**
	 * Step3 上传成功返回数据 界面
	 */
	renderStepSuccess() {
		const { baseInfo } = this.props;
		// const information = (
		// 	<div className={styles.information_div}>
		// 		<Card className={styles.card_div} hoverable={true}>
		// 			<Meta
		// 				avatar={
		// 					<img
		// 						className={styles.meta_img}
		// 						src={baseInfo. ? baseInfo.url : 'http://img.53site.com/Werewolf/Frame/0_player.png'}
		// 					/>
		// 				}
		// 				title={baseInfo.name}
		// 				description={baseInfo.remark}
		// 			/>
		// 		</Card>
		// 	</div>
		// );
		const actions = (
			<Fragment>
				<Button type="primary" onClick={this.handleSubmitStepSuccess}>
					再次新增
				</Button>
				<Button type="primary" onClick={this.props.onClose}>
					完成
				</Button>
			</Fragment>
		);
		return (
			<Result
				type="success"
				title="操作成功"
				description="新增成就成功"
				// extra={information}
				actions={actions}
				style={{ margin: '0 auto', maxWidth: 560, padding: '24px 0 8px' }}
			/>
		);
	}

	/**
	 * 点击了再次新增
	 */
	handleSubmitStepSuccess = () => {
		this.props.form.resetFields();
		this.props.dispatch({
			type: 'achieve/setUpStep',
			payload: UploadAchieveStep.Base
		});
	};
}

export default Form.create()(UploadAchieveModal);
