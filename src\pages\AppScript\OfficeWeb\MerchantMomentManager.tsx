import React from "react";
import * as styles from './MerchantMomentManager.less';
import PageHeaderWrapper from "@/components/PageHeaderWrapper";
import { Card, Table, Button, Popconfirm, message, Space } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { IofficeNews, IofficeNewsDel } from "@/dto/wfOfficeNews";
import { ILogin } from "@/models/login";
import { getTimeStr } from '@/utils/momentTool';
import { EditOutlined } from '@ant-design/icons';
import { connect } from "dva";
import { ColumnProps } from 'antd/lib/table';
import moment from "moment";
import MomentCreateModal from "./MomentCreateModal";
import MomentCommentModal from "./MomentCommentModal";
import MomentPictureManager from "./MomentPictureManager";
import MomentSortEditorModal from './MomentSortEditorModal';
import { newsList } from "@/services/apiWebOffice";
import { number } from "prop-types";
import { saveHtmlDraft } from "@/utils/utils";


export const PAGESIZE = 8;
const defaultPayload = { current: 1, pageCount: PAGESIZE };
@connect(({ loading, login, merchantMoment }: { loading: IdvaLoading; login: ILogin; merchantMoment: any }) => ({
  uid: login.uid,
  isLoading: loading.models['merchantMoment'],
  momentList: merchantMoment.momentList,
  momentListCount: merchantMoment.momentListCount,
}))
export default class MerchantMomentManager extends React.Component<any, any> {

  constructor(props) {
    super(props);
    this.state = {
      isEditModal: false,
      isCommentModal: false,
      isShowModal: false,
      isShowSortModal: false,
      isShowPictureModal: false,
      newsIndex: 0,
      currentPage: 1,
    }
    this.dispatchList(defaultPayload);
    this.dispatchListCount();
  }

  //列表项
  private columns: ColumnProps<IofficeNews>[] = [
    {
      title: 'id',
      dataIndex: 'id',
      align: 'center',
    },
    {
      title: '排序值(降序)',
      dataIndex: 'is_recommend',
      key: 'is_recommend',
      align: 'center',
      render: (val, record, index) => {
        return (
          <Space>
            <Space>{val ? val : "0"}</Space>
            <Space>
              <Button type="primary" icon={<EditOutlined />} shape="circle" value="small" onClick={() => this.onClickEditSort(record)}>
						  </Button>
            </Space>
          </Space>)
      }
    },
    {
      title: '标题',
      dataIndex: 'title',
      align: 'center',
    },
    {
      title: '编辑时间',
      dataIndex: 'edit_time',
      align: 'center',
      width: 170,
      render: (val) => {
        return <div>{val == null ? '\\' : getTimeStr(val)}</div>;
      }
    },
    {
      title: '话题',
      dataIndex: 'topic_name',
      align: 'center',
      width: 200,
      render: (val) => {
        return <div>{val == null ? '\\' : val}</div>;
      }
    },
    {
      title: '状态',
      dataIndex: 'delsign',
      align: 'center',
      width: 70,
      render: (val) => {
        return <div>{val == 1 ? '删除' : "正常"}</div>;
      }
    },
    {
      title: '操作',
      dataIndex: 'delsign',
      align: 'center',
      width: 300,
      render: (val, record, index) => {
        return (
          <div>
            <Button type="primary" onClick={() => this.onClickEdit(index)}>
              编辑
						</Button>

            <Popconfirm
              title={`确定要${val == 0 ? '下架' : '上架'}吗?`}
              onConfirm={() => {

                const delsignAction = record.delsign == 1 ? 0 : 1;
                if (delsignAction == 0) {
                  this.props.dispatch({
                    type: 'merchantMoment/getMomentPictureCanShowCount',
                    payload: {
                      moment_id: record.id,
                    }
                  }).then((data) => {
                    const num = data.num;
                    if (delsignAction == 0 && num <= 0) {//上架
                      message.error("上架失败，至少有一张图片是上架状态才可以上架文章！")
                      return;
                    }
                    this.updateMomentDelsign(record);
                  });
                } else {
                  this.updateMomentDelsign(record);
                }

              }}
              okText="Yes"
              cancelText="No"
            >
              <Button className={styles.marginLeft} type="primary" danger={true}>
                {val == 0 ? '下架' : '上架'}
              </Button>
            </Popconfirm>
            <Button className={styles.marginLeft} type="primary" onClick={() => this.onClickPicture(index)}>
              图片管理
						</Button>
          </div>

        );
      }
    },
    {
      title: '评论管理',
      dataIndex: 'delsign',
      align: 'center',
      width: 170,
      render: (val, record, index) => {
        return (
          <div>
            <Button type="primary" onClick={() => this.onClickComment(record)}>
              查看评论
						</Button>
          </div>
    
        );
      }
    }
  ];

  updateMomentDelsign = (record) => {
    this.props.dispatch({
      type: 'merchantMoment/updateMomentDelsign',
      payload: {
        id: record.id,
        delsign: record.delsign == 1 ? 0 : 1,
        current: this.state.currentPage,
        pageCount: PAGESIZE,
      }
    });
  }

  render() {
    const { isLoading, momentList, dispatch } = this.props;
    const { isShowSortModal, isEditModal, isShowModal, isShowPictureModal, newsIndex, isCommentModal } = this.state;
    return (
      <PageHeaderWrapper title="剧本圈" content={this.renderHeader()}>
        <Card>
          <Table
            // className={styles.tableClass}
            columns={this.columns}
            dataSource={momentList}
            loading={!!isLoading}
            rowKey={(record, index) => index.toString()}
            pagination={{  // 分页
              pageSize: PAGESIZE,
              current: this.state.currentPage,
              total: this.props.momentListCount,
              onChange: this.changePage,
            }}
          />
          {isShowModal && <MomentCreateModal
            news={(momentList && momentList.length > 0) ? momentList[newsIndex] : null}
            isEditeModal={isEditModal}
            loading={!!isLoading}
            dispatch={dispatch}
            onHandleCreate={this.onHandleCreate}
            onHandleEdit={this.onHandleEdit}
            onHandleClose={this.onHandleClose}
          />}
          {
            isShowPictureModal && <MomentPictureManager
              news={(momentList && momentList.length > 0) ? momentList[newsIndex] : null}
              loading={!!isLoading}
              dispatch={dispatch}
              onHandleClose={this.onHandleClose} />
          }
          {
            isCommentModal && <MomentCommentModal
              current={this.state.currentMoment}
              onHandleClose={this.onHandleClose} />
          }
          {
            isShowSortModal && <MomentSortEditorModal 
            current={this.state.currentMoment}
            isShowSortModal={isShowSortModal}
            onClickClose={this.onHandleClose}
            currentPage={this.state.currentPage}
            pageCount={PAGESIZE}/>
          }
        </Card>
      </PageHeaderWrapper>
    );
  }

  //渲染头部
  renderHeader() {
    return (
      <div className={styles.avframeheader}>
        <div className={styles.avswitch}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            size="large"
            loading={!!this.props.isLoading}
            onClick={this.onClickCreate}
          >
            新建
					</Button>
        </div>
      </div>
    );
  }

  // 点击创建
  onClickCreate = () => {
    this.setState({
      isShowModal: true,
      isEditModal: false,
    })
  };

  // 点击编辑
  onClickEdit = (index: number) => {
    this.setState({
      isShowModal: true,
      isEditModal: true,
      newsIndex: index
    })
  };

  onClickEditSort = (item) => {
    this.setState({
      isShowSortModal: true,
      currentMoment: item,
    })
  };


  onClickComment = (item) => {
    this.setState({
      isCommentModal: true,
      currentMoment: item,
    });

  };

  onClickPicture = (index: number) => {
    this.setState({
      isShowPictureModal: true,
      isEditModal: true,
      newsIndex: index
    })
  };

  // 点击删除
  onClickDelete = (index: number) => {
    const { dispatch, newsList } = this.props;
    const req: IofficeNewsDel = { id: newsList[index].id };
    dispatch({ type: 'officeNews/fetchDelete', payload: req });
  };

  //发送新建新闻
  onHandleCreate = (req: any) => {
    const { dispatch } = this.props;
    const that = this;
    req.current = this.state.currentPage;
    req.pageCount = PAGESIZE;
    dispatch({ type: 'merchantMoment/insertMoment', payload: req })
      .then(() => {
        that.setState({ isShowModal: false });
        saveHtmlDraft("create", "");
      })
  }

  //发送编辑新闻
  onHandleEdit = (req: IofficeNews) => {
    const { dispatch } = this.props;
    const that = this;
    req.current = this.state.currentPage;
    req.pageCount = PAGESIZE;
    dispatch({ type: 'merchantMoment/updateMoment', payload: req })
      .then(() => {
        that.setState({ isShowModal: false });
        saveHtmlDraft(req.id, "")
      })
  }

  //关闭模态页
  onHandleClose = () => {
    this.setState({
      isShowModal: false,
      isShowPictureModal: false,
      isEditModal: false,
      isCommentModal: false,
      isShowSortModal: false,
    })
  }

  changePage = (page) => {
    this.setState({ currentPage: page });
    const req = { current: page, pageCount: PAGESIZE };
    this.dispatchList(req);
  };

  dispatchList = (req) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'merchantMoment/getMomentList',
      payload: req
    });
  };

  dispatchListCount = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'merchantMoment/getMomentListCount',
    });
  };

}
