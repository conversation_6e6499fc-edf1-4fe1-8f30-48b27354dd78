//item/obtian请求入参
export interface IobtianItemReq {
    type: number;
    activityId: number;
    userList: IitemObtainUser[]
}

//
export interface IitemObtainUser {
    userId: number;
    itemDicList: IitemDic[]
}

export interface IitemDic {
    itemDicId: number;
    num: number;
}

export interface IexchangeReqPer {
    //创建时间
    createTime: string;
    //请求唯一戳
    reqSign: string;
    //请求信息实体json
    reqJson: string;
    //失败渠道 name-node_id
    channel: string;
    //失败的路由
    route: string;
    //失败原因
    errResult: string;
}

export interface IexchangeResp{
    code: number;
    message: string;
}

export interface IselFailListReq {
    user_id?: number;
    activity?: number;
    req_sign?: string;
    type?: number
}