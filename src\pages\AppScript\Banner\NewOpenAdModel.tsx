import React, { Component, Fragment } from 'react';
import * as styles from './NewBanner.less';
import { connect } from 'dva';
import { ILogin } from '@/models/login';
import '@ant-design/compatible/assets/index.css';
import { Upload, Button, Modal, Steps, Input, Select, Card, Form, DatePicker } from 'antd';
import { UploadBannerStep, INewBannerStore, IbannerBaseInfo } from '@/dto/wfBanner';
import { FormInstance } from 'antd/lib/form';

const Step = Steps.Step;
const Option = Select.Option;
const { Meta } = Card;
export interface UploadModalProps {
  uid?: number,
  dispatch?: Function,
  isNewOpenAdModal: boolean,
  onClose: () => void,
  step?: UploadBannerStep,
  isBaseing?: Boolean,
  isCompleteing?: Boolean,
  imageName?: string,
  typeList?: any[],
}
const selectBefore = (
  <Select defaultValue="http://" className="select-before">
    <Option value="https://">http://</Option>
    <Option value="http://">https://</Option>
  </Select>
);

export interface UploadModalState {
  isUploading: Boolean,
  imageUrl: string,
  baseInfo: IbannerBaseInfo,
  selectType: number,
  selectPage: number
}
@connect(({ loading, login, bannerStore }: { loading: IdvaLoading, login: ILogin, bannerStore: INewBannerStore }) => ({
  uid: login.uid,
  imageName: bannerStore.imageName,
  banner_id: bannerStore.banner_id,
  typeList: bannerStore.typeList,
  step: bannerStore.uploadStep
}))
export default class NewOpenAdModel extends Component<UploadModalProps, UploadModalState> {
  formRef = React.createRef<FormInstance>();
  constructor(props) {
    super(props);
    this.state = {
      isUploading: false,
      imageUrl: '',
      baseInfo: null,
      selectType: 0,

    };
  }



  render() {
    const { isNewOpenAdModal, step } = this.props;
    const title = step === UploadBannerStep.Upload ? '继续编辑' : '新建开屏广告';
    return (
      <Modal visible={isNewOpenAdModal} onCancel={this.handleCloseModal} title={title} footer={null} width={1200} centered={true}>
        {this.renderUploadSteps()}
        {step == UploadBannerStep.Base && this.renderStepDefault()}
      </Modal>
    );
  }

  handleCloseModal = () => {
    this.props.onClose();
  };

  /**
   * 步骤条
   */
  renderUploadSteps() {
    return (
      <Steps current={this.props.step}>
        <Step title="基础信息" description="填写开屏广告基础信息" />
      </Steps>
    );
  }
  /**
   * Step1 填写banner信息表单
   */
  renderStepDefault() {
    const layout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 18 }
    };
    const validateMessages = {
      required: '请填写${label}'
    };
    const onFinish = (values) => {
      const value = {
        open_ad_name: values.open_ad_name,
        url: values.href_url,
        type: values.type,
        start_time: values.time[0].format('YYYY-MM-DD HH:mm:ss'),
        end_time: values.time[1].format('YYYY-MM-DD HH:mm:ss')
      };
      this.props.dispatch({
        type: 'mainPageBanner/insertOpenAd',
        payload: value
      }).then(this.handleCloseModal);
    };
    const { RangePicker } = DatePicker;
    const { selectType } = this.state;
    return (
      <div className={styles.step_div}>
        <Form
          {...layout}
          className={styles.stepForm}
          onFinish={onFinish}
          validateMessages={validateMessages}
          initialValues={{
            type: 0,
            page: 0
          }}
          ref={this.formRef}
        >

          <Form.Item name={'open_ad_name'} label="开屏名称" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name={'type'} label="跳转类型" rules={[{ required: true }]}>
            <Select onChange={this.handleTypeOptionChange}>
              <Option value={1}>店铺</Option>
              <Option value={2}>剧本</Option>
              <Option value={3}>图片URL跳转</Option>
              <Option value={4}>文章</Option>
              <Option value={5}>订单</Option>
            </Select>
          </Form.Item>
          {(selectType == 3) && (
            <Form.Item name="href_url" label="网页url" rules={[{ required: false }]}>
              <div style={{ marginBottom: 16 }}>
                <Input addonBefore={selectBefore} />
              </div>
            </Form.Item>
          )}
          {(selectType == 1 || selectType == 2 || selectType == 4 || selectType == 5) && (
            <Form.Item name="href_url" label="跳转id" rules={[{ required: false }]}>
              <div style={{ marginBottom: 16 }}>
                <Input placeholder="Basic usage" />
              </div>
            </Form.Item>
          )}
          <Form.Item name="time" label="起始时间" rules={[{ required: true }]}>
            <RangePicker showTime={true} format="YYYY-MM-DD HH:mm:ss" />
          </Form.Item>

          <div className={styles.submit_btn}>
            <Button type="primary" htmlType="submit" loading={!!this.props.isBaseing}>
              确认
            </Button>
          </div>
        </Form>
      </div>
    );
  }

  //下拉框选择
  handleTypeOptionChange = (e) => {
    console.log(e);
    this.setState({
      selectType: e
    });
  };

  handlePageOptionChange = (e) => {
    console.log(e);
    this.setState({
      selectPage: e
    });
  };
}
