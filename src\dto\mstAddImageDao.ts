
export interface IMstAddImageSearchParams {
    model_id: string,
}

export interface IMstAddImageModelItem {
    task_id: string,
    model_type_id: number,
    model_id: number,
    image_url: string, 
    model_name: string, 
    vae_model_id: string,
    vae_model_name: string,
    sampling_name: string,
    sampling_id: string,
    sampling_step: string,
    cfg_scale: string,//文本强度
    clip_skip: string,
    seed: string,
    ensd: string,
    width: string,
    height: string,
    origin_prompt: string,
    negative_prompt: string,
    create_time: string,
    lora_model_id: string,
    lora_model_name: string,
    task_model_id: string,
    task_model_name: string,
}

export interface ImstAddImageSearchLikeNameParams {
    model_name: string,
    is_big_name: boolean
}

export interface MstAddImageInsertInfoParams {
    origin_prompt: string,
    negative_prompt: string,
    sampling_id: string,
    sampling_step: string,
    seed: string,
    clip_skip: string,
    ensd: string,
    cfg_scale: string,
    denoising_strength: string,
    width: string,
    height: string,
    vae_model_id: string,
    sd_use_lora: string,
    task_model_id: string,
    image_url: string,
    model_type_id: number,
}

// export interface MstAddImageUpdateInfoParams {
//     origin_prompt: string,
//     negative_prompt: string,
//     sampling_id: string,
//     sampling_step: string,
//     seed: string,
//     clip_skip: string,
//     ensd: string,
//     cfg_scale: string,
//     denoising_strength: string,
//     width: string,
//     height: string,
//     vae_model_id: string,
//     sd_use_lora: string,
//     task_model_id: string,
//     image_url: string,
// }
