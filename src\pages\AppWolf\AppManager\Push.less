@import '~antd/lib/style/themes/default.less';
@import '~@/utils/utils.less';

.targetItem {
  display: flex;
  flex-direction: row;
  // align-items: center;
  margin: 5px;
  .select {
    margin-left: 10px;
  }
  .inputFrame {
    margin-left: 20px;
    .input {
      width: 300px;
    }
  }

  // .select {
  //   display: flex;
  //   flex-direction: row;
  //   align-items: center;
  //   .switch {
  //     margin-left: 5px;
  //   }
  // }

  // .hallName {
  //   display: flex;
  //   flex-direction: row;
  //   align-items: center;
  //   margin-top: 10px;
  //   .name {
  //     margin-left: 5px;
  //     font-size: 17px;
  //     font-weight: bolder;
  //   }
  // }
}

.contentItem {
  display: flex;
  flex-direction: column;
  margin: 5px;
  .input {
    margin-top: 10px;
  }
}

.errFont {
  color: red;
  margin-top: 5px;
}
.titleFont {
  font-weight: bold;
  margin-top: 5px;
}
.send {
  margin-top: 20px;
  margin-left: 5px;
}
