/*
 * @Description: 奖励配置
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercu<PERSON>
 * @Date: 2020-09-21 15:18:22
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-16 16:46:30
 */
import request from '@/utils/request';
import { API_VERSION } from './api';

//活动列表
export async function atyList() {
  return request(`${API_VERSION}/werewolf/atyAward/atyList`, {
    method: `POST`,
  });
}

//组列表
export async function groupList(params) {
  return request(`${API_VERSION}/werewolf/atyAward/groupList`, {
    method: `POST`,
    body: params,
  });
}

//配置列表
export async function confList(params) {
  return request(`${API_VERSION}/werewolf/atyAward/confList`, {
    method: `POST`,
    body: params,
  });
}
// 新建奖励组
export async function insertAwardGroup(params) {
  return request(`${API_VERSION}/werewolf/atyAward/insertAwardGroup`, {
    method: `POST`,
    body: params,
  });
}
// 编辑奖励组
export async function updateAwardGroup(params) {
  return request(`${API_VERSION}/werewolf/atyAward/updateAwardGroup`, {
    method: `POST`,
    body: params,
  });
}

export async function insertAward(params) {
  return request(`${API_VERSION}/werewolf/atyAward/insertAward`, {
    method: `POST`,
    body: params,
  });
}

export async function getSubItemList(params) {
  return request(`${API_VERSION}/werewolf/atyAward/subItemList`, {
    method: 'POST',
    body: params,
  });
}
