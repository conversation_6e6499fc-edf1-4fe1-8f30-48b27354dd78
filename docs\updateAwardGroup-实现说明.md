# updateAwardGroup 实现说明

## 实现概述

基于现有的 `insertAwardGroup` 实现流程，成功实现了 `updateAwardGroup` 功能，完整支持奖励组的编辑操作。

## 实现步骤

### 1. API 服务层 (`src/services/apiAtyAward.js`)

添加了 `updateAwardGroup` API 函数：

```javascript
export async function updateAwardGroup(params) {
  return request(`${API_VERSION}/werewolf/atyAward/updateAwardGroup`, {
    method: `POST`,
    body: params,
  });
}
```

**特点**：
- 使用 POST 方法（与 insertAwardGroup 保持一致）
- 请求路径：`/werewolf/atyAward/updateAwardGroup`
- 参数结构与 insertAwardGroup 相同，但需要包含 `id` 字段

### 2. Model 层 (`src/pages/AppWolf/ActivityAward/models/activityAward.ts`)

#### 导入更新
```typescript
import { insertAward, insertAwardGroup, updateAwardGroup, atyList, confList, groupList } from "@/services/apiAtyAward"
import { message } from 'antd';
```

#### Effect 实现
```typescript
*updateAwardGroup({ payload }: { payload: any }, { call, put }) {
    const response: any = yield call(updateAwardGroup, payload);
    if (!response) {
        return
    }
    message.success("编辑成功！")
    yield put({ type: 'fetchAwardGroupList', payload: { activity_id: payload.activity_id } })
},
```

**实现特点**：
- 调用 `updateAwardGroup` API
- 成功后显示"编辑成功！"消息
- 自动刷新奖励组列表
- 错误处理：API 失败时直接返回

### 3. 组件层集成 (`AwardGroupEditModal.tsx`)

在组件的 `handleSubmit` 方法中已经集成了对 `updateAwardGroup` 的调用：

```typescript
handleSubmit = async () => {
    try {
        const values = await this.formRef.current?.validateFields();
        const { dispatch, dataModelType, current, item } = this.props;
        
        const isEdit = dataModelType === ModalType.EDIT;
        const request = {
            activity_id: current.id,
            group_name: values.group_name,
            group_index: values.group_index,
            ...(isEdit && item && { id: item.id }) // 编辑时需要传递ID
        };

        const actionType = isEdit 
            ? 'activityAward/updateAwardGroup'  // 使用新实现的 effect
            : 'activityAward/insertAwardGroup';

        await dispatch({
            type: actionType,
            payload: request
        });

        message.success(isEdit ? '奖励组修改成功' : '奖励组创建成功');
        this.handleCancel();
    } catch (error) {
        console.error('提交失败:', error);
    }
};
```

## 数据流程

### 编辑模式数据流
1. **用户操作** → 点击编辑按钮
2. **组件层** → `AwardGroupEditModal` 接收 `item` 参数（包含要编辑的奖励组数据）
3. **表单初始化** → 使用 `item` 数据填充表单
4. **用户提交** → 调用 `handleSubmit` 方法
5. **数据准备** → 组装请求参数（包含 `id` 字段）
6. **Dispatch** → 调用 `activityAward/updateAwardGroup`
7. **Model Effect** → 执行 `updateAwardGroup` effect
8. **API 调用** → 调用后端 `updateAwardGroup` 接口
9. **成功处理** → 显示成功消息，刷新列表
10. **UI 更新** → 关闭模态框，更新奖励组列表

### 请求参数结构

#### 新建奖励组
```javascript
{
    activity_id: 123,
    group_name: "新奖励组",
    group_index: 100
}
```

#### 编辑奖励组
```javascript
{
    id: 456,              // 奖励组ID（编辑时必需）
    activity_id: 123,
    group_name: "修改后的奖励组",
    group_index: 101
}
```

## 与 insertAwardGroup 的对比

| 特性 | insertAwardGroup | updateAwardGroup |
|------|------------------|------------------|
| API 路径 | `/insertAwardGroup` | `/updateAwardGroup` |
| HTTP 方法 | POST | POST |
| 必需参数 | `activity_id`, `group_name`, `group_index` | `id`, `activity_id`, `group_name`, `group_index` |
| 成功消息 | "新增成功！" | "编辑成功！" |
| 后续操作 | 刷新列表 | 刷新列表 |

## 错误处理

1. **API 层错误**：网络请求失败或服务器错误
2. **表单验证错误**：字段验证失败（自动显示）
3. **业务逻辑错误**：后端返回错误信息

所有错误都会在控制台记录，表单验证错误会自动显示在对应字段下方。

## 测试建议

1. **功能测试**：
   - 测试编辑现有奖励组
   - 验证表单数据正确填充
   - 确认提交后数据更新

2. **边界测试**：
   - 测试必填字段验证
   - 测试字符长度限制
   - 测试数值范围限制

3. **错误测试**：
   - 测试网络错误处理
   - 测试服务器错误处理
   - 测试重复名称处理

## 后续优化建议

1. **类型安全**：为 payload 添加更严格的类型定义
2. **错误处理**：添加更详细的错误信息显示
3. **加载状态**：添加更细粒度的加载状态管理
4. **缓存优化**：考虑添加本地缓存机制
