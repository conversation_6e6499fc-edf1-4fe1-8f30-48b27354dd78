@import '~antd/lib/style/themes/default.less';
@import '~@/utils/utils.less';

.searchError {
  color: @alert-error-icon-color;
  margin-top: 10px;
  margin-bottom: 10px;
}

.setOn {
  color: @primary-color;
}

.setOff {
  color: @alert-error-icon-color;
}

.customModalBody :global(.ant-modal-header) {
  padding: 10px;
}

.modalForm {
  display: flex;
  flex-direction: column;

  .formItem {
    display: flex;
    flex-direction: row;
    margin-top: 4px;
    justify-content: center;
    align-self: flex-start;

    .title {
      margin-top: 3px;
      width: 120px;
      font-weight: bold;
    }

    .content {
      width: 360px;
      margin-left: 15px;
    }

    .toggle {
      margin-left: 15px;
    }

    .searchButton {
      margin-top: 5px;
      margin-left: 15px;
    }

    .modalFormSub {
      display: flex;
      flex-direction: row;

      .formItemSub {
        justify-content: center;
        align-self: flex-start;
        margin-top: 5px;

        .contentButtonSub {
          margin-top: 3px;
          margin-left: 10px;
        }
      }
    }
  }
}

.tableInc {
  color: @primary-color;
}

.tableDec {
  color: magenta;
}
