 /*
 * @Description: 称号-编辑模态页
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2019-10-12 16:56:14
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-10 14:10:31
 */

import { Component } from 'react';
import { connect } from 'dva';
import { ILogin } from '@/models/login';
import { Modal, Steps, Button, Select, message, Result, Row, Image, Col, Popconfirm, Input, Spin } from 'antd';
const { TextArea } = Input;
import * as styles from './MerchantAuditModal.less';
import { wfPropsGiftSourceType, wfPropsGiftType, wfPropsType } from "../../../dto/staticEnum";
const { Step } = Steps;
import { getGiftSourceStr, checkNameExist } from '@/utils/mallTool';
import { getCateName, getSelectName } from '@/utils/mallTool';
import { checkNotNull } from '@/utils/emptyTool';
import MerchantScriptTable from './MerchantScriptTable';
import MerchantMap from './MerchantMap';
import React from 'react';
import { fromJS } from 'immutable';
const { Option } = Select;

export enum AuditStep {
  script = 0,
  image,
  map,
  success,
}

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  currentMerchantScriptList: scriptkill.currentMerchantScriptList,
}))

class MerchantAuditModal extends React.Component<any, any> {
  constructor(props) {
    super(props);
    const { dataModelType, currentMerchant, currentMerchantScriptList } = props;

    this.state = {
      id: currentMerchant.id,
      merchant_id: currentMerchant.merchant_id,
      name: currentMerchant.name,

      step: AuditStep.script,
    }
  }

  render() {
    const { dataModelType, currentMerchant, isLoading } = this.props;
    const { refuseModalType } = this.state;
    const { step } = this.state;
    let title = '';
    if (currentMerchant.name == null) {
      title = '审核商铺 ID:' + currentMerchant.id;
    } else {
      title = '审核商铺【' + currentMerchant.name + '】 ID:' + currentMerchant.id;
    }
    return (
      <>
        <Modal
          width={1650}
          bodyStyle={{ padding: '5px 10px 5px 10px' }}
          closable={true}
          maskClosable={true}
          confirmLoading={!!isLoading}
          centered={true}
          title={title}
          onCancel={this.onCloseModel}
          footer={[]}
          visible={dataModelType > 0 ? true : false}
        >
          <div>
            {this.renderSteps()}
            {step == AuditStep.script && this.renderStepScript()}
            {step == AuditStep.image && this.renderStepImage()}
            {step == AuditStep.map && this.renderMap()}
            {step == AuditStep.success && this.renderSuccess()}
          </div>
        </Modal>
        <Spin spinning={!!this.props.isLoading}>
          {refuseModalType > 0 &&
            <RefuseModal
              refuseModalType={refuseModalType}
              onCloseRefuseModel={this.onCloseRefuseModel}
              refuseMerchantAction={this.refuseMerchantAction}
              onRefuseMesChange={this.onRefuseMesChange}>
            </RefuseModal>
          }
        </Spin>
      </>
    );
  }

  renderSteps() {
    return (
      <Steps current={this.state.step} status={this.getStepStatus()}>
        <Step title="剧本信息" description="" />
        <Step title="图片预览" description="" />
        <Step title="定位" description="" />
        <Step title="确认审核" description="" />
      </Steps>
    )
  }
  getStepStatus() {
    if (this.getStepStatusAction()) {
      return "process";
    } else {
      return "error";
    }
  }
  getStepStatusAction() {
    switch (this.state.step) {
      case AuditStep.script:
        {
          if (this.props.currentMerchantScriptList == undefined ||
            this.props.currentMerchantScriptList == null ||
            this.props.currentMerchantScriptList.length <= 0) {
            return false;
          }
          let outCount = 0;
          for (const script of this.props.currentMerchantScriptList) {
            if (script.delsign == 0) {
              if (script.status != 10) {
                return false;
              }
            } else {
              outCount++;
            }
          }
          if (outCount == this.props.currentMerchantScriptList.length) {
            return false;
          }
          return true;
        }
      case AuditStep.image:
        {
          if (this.props.currentMerchant.imgList == undefined ||
            this.props.currentMerchant.imgList == null ||
            this.props.currentMerchant.imgList.length <= 0) {
            return false;
          }
          return true;
        }
      case AuditStep.map:
        {
          if (this.props.currentMerchant.latitude == undefined ||
            this.props.currentMerchant.latitude == null ||
            this.props.currentMerchant.longitude == undefined ||
            this.props.currentMerchant.longitude == null) {
            return false;
          }
          return true;
        }
      default:
        return true;
    }
  }

  renderStepScript() {
    return (
      <div className={styles.step_div}>
        <MerchantScriptTable
          scriptList={this.props.currentMerchantScriptList}
          action={false}
          type={2}
        />
        <RefuseButton refuse={this.refuseMerchant}></RefuseButton>
        <Button disabled={!this.getStepStatusAction()} className={styles.submit_btn} type="primary" onClick={() => this.stepAction(AuditStep.image)} loading={!!this.props.isCompleteing}>下一步</Button>
      </div>
    )
  }

  renderStepImage() {
    if (this.props.currentMerchant.imgList == undefined ||
      this.props.currentMerchant.imgList == null ||
      this.props.currentMerchant.imgList.length <= 0) {
      return (
        <div className={styles.step_img_div}>
          <div className={styles.text_div}>该商铺未上传图片</div>
          <div className={styles.button_div}>
            <RefuseButton refuse={this.refuseMerchant}></RefuseButton>
            <Button className={styles.submit_btn} type="primary" onClick={() => this.stepAction(AuditStep.script)} loading={!!this.props.isCompleteing}>上一步</Button>
            <Button disabled={!this.getStepStatusAction()} className={styles.submit_btn} type="primary" onClick={() => this.stepAction(AuditStep.map)} loading={!!this.props.isCompleteing}>下一步</Button>
          </div>
        </div>
      )
    }

    return (
      <div className={styles.step_div}>
        <Row gutter={[10, 10]}>
          {this.props.currentMerchant.imgList.map((item, index) => {
            return (
              <Col>
                <Image src={item} width={400} height={240} />
              </Col>
            );
          })}
        </Row>
        <RefuseButton refuse={this.refuseMerchant}></RefuseButton>
        <Button className={styles.submit_btn} type="primary" onClick={() => this.stepAction(AuditStep.script)} loading={!!this.props.isCompleteing}>上一步</Button>
        <Button disabled={!this.getStepStatusAction()} className={styles.submit_btn} type="primary" onClick={() => this.stepAction(AuditStep.map)} loading={!!this.props.isCompleteing}>下一步</Button>
      </div>
    )
  };

  renderMap() {
    if (this.props.currentMerchant.latitude == undefined ||
      this.props.currentMerchant.latitude == null ||
      this.props.currentMerchant.longitude == undefined ||
      this.props.currentMerchant.longitude == null) {
      return (
        <div className={styles.step_img_div}>
          <div className={styles.text_div}>该商铺没有地位信息</div>
          <div className={styles.button_div}>
            <RefuseButton refuse={this.refuseMerchant}></RefuseButton>
            <Button className={styles.submit_btn} type="primary" onClick={() => this.stepAction(AuditStep.image)} loading={!!this.props.isCompleteing}>上一步</Button>
            <Button disabled={!this.getStepStatusAction()} className={styles.submit_btn} type="primary" onClick={() => this.stepAction(AuditStep.success)} loading={!!this.props.isCompleteing}>下一步</Button>
          </div>
        </div>
      )
    }

    let str = "";
    if (checkNotNull(this.props.currentMerchant.provinceName)) {
      str += this.props.currentMerchant.provinceName + " ";
    }
    if (checkNotNull(this.props.currentMerchant.cityName)) {
      str += this.props.currentMerchant.cityName + " ";
    }
    if (checkNotNull(this.props.currentMerchant.address)) {
      str += this.props.currentMerchant.address;
    }

    return (
      <div className={styles.step_div}>
        <div className={styles.step_div_center}>
          <div>{str}</div>
        </div>
        <MerchantMap
          latitude={this.props.currentMerchant.latitude}
          longitude={this.props.currentMerchant.longitude}
        />
        <RefuseButton refuse={this.refuseMerchant}></RefuseButton>
        <Button className={styles.submit_btn} type="primary" onClick={() => this.stepAction(AuditStep.image)} loading={!!this.props.isCompleteing}>上一步</Button>
        <Button disabled={!this.getStepStatusAction()} className={styles.submit_btn} type="primary" onClick={() => this.stepAction(AuditStep.success)} loading={!!this.props.isCompleteing}>下一步</Button>
      </div>
    )
  }

  renderSuccess() {
    return (
      <Result
        status="warning"
        title="请点击确认该商铺通过审核"
        extra={[
          <RefuseButton refuse={this.refuseMerchant}></RefuseButton>,
          <Button className={styles.submit_btn} type="primary" onClick={() => this.stepAction(AuditStep.map)} loading={!!this.props.isCompleteing}>上一步</Button>,
          <Button type="primary" key="success" onClick={this.confirmMerchant}>确认</Button>,
        ]}
      />
    );
  }

  stepAction = (step) => {
    this.setState({ step });
  };

  confirmMerchant = () => {
    const { dispatch } = this.props;
    const request = this.handleRequest();
    const flag = this.checkRequest(request, true);
    if (flag) {
      dispatch({
        type: 'scriptkill/confirmMerchant',
        payload: request
      }).then(() => {
        this.onCloseModel();
        this.props.refreshNum(this.props.tabKey);
      });
    }
  }

  refuseMerchant = () => {
    this.setState({ refuseModalType: 1 });
    this.setState({ refuseMes: "" });
  }

  onRefuseMesChange = (str) => {
    this.setState({ refuseMes: str });
  }

  refuseMerchantAction = () => {
    const { dispatch } = this.props;
    const request = this.handleRequest();
    const flag = this.checkRequest(request, true);
    console.log("requestrequest", request);

    if (flag) {
      dispatch({
        type: 'scriptkill/refuseMerchant',
        payload: request
      }).then(() => {
        this.onCloseModel();
        this.props.refreshNum(this.props.tabKey);
      });
    }
  }

  handleRequest = () => {
    const request: any = {
      id: this.props.currentMerchant.id,
      merchant_id: this.state.merchant_id,
      current: this.props.currentPage,
      pageCount: this.props.pageSize,
      tabKey: this.props.tabKey,
      where: this.props.where,
      refuseMes: this.state.refuseMes,
      operateUserId: this.props.uid,
    };
    return request;
  };

  checkRequest = (request: any, isCreating: boolean) => {
    if (request.id == null || request.id == undefined || request.id <= 0) {
      message.error("数据错误，请重新编辑");
      return false;
    }
    if (request.current == null || request.current == undefined || request.current <= 0) {
      message.error("数据错误，请重新编辑1");
      return false;
    }
    if (request.pageCount == null || request.pageCount == undefined || request.pageCount <= 0) {
      message.error("数据错误，请重新编辑2");
      return false;
    }
    if (request.where == null || request.where == undefined) {
      message.error("数据错误，请重新编辑3");
      return false;
    }
    return true;
  }

  //关闭模态页
  onCloseModel = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/setScriptList',
      payload: []
    }).then(() => {
      this.props.onClickClose();
    });
  };

  //关闭模态页
  onCloseRefuseModel = () => {
    this.setState({ refuseModalType: 0 });
    this.setState({ refuseMes: "" });
  };
}

class RefuseButton extends React.Component<any, any> {

  render() {
    return (
      <Button className={styles.marginLeft} type="primary" danger={true}
        onClick={this.props.refuse}>
        驳回
      </Button>
    )

  }
}

class RefuseModal extends React.Component<any, any> {
  render() {

    const { refuseModalType, isLoading } = this.props;
    return (
      <Modal
        width={800}
        bodyStyle={{ padding: '5px 10px 5px 10px' }}
        closable={true}
        maskClosable={true}
        confirmLoading={!!isLoading}
        centered={true}
        title="填写驳回理由"
        onCancel={this.props.onCloseRefuseModel}
        footer={[]}
        visible={refuseModalType > 0 ? true : false}
      >
        <div>
          <TextArea showCount={true} maxLength={100}
            onChange={e => {
              this.props.onRefuseMesChange(e.target.value)
            }} />
          <Popconfirm
            title={`确定要驳回商铺审核吗?`}
            onConfirm={this.props.refuseMerchantAction}
            okText="Yes"
            cancelText="No"
          >
            <Button className={styles.marginLeft} type="primary" danger={true}>驳回</Button>
          </Popconfirm>
        </div>
      </Modal>
    )
  }
}
export default MerchantAuditModal;
