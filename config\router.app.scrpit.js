/*
 * @Description: 剧本杀路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 张路
 * @Date: 2020-11-17 11:01:15
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-01-13 18:13:08
 */
import { AccessRouteId } from './accessRouteCof';
//import deductionRecord from '@/pages/AppWolf/ScoreViolation/models/deductionRecord';

const AppWolfRoutes = {
  path: 'appScript',
  name: 'appScript',
  icon: 'http://video.53site.com/00script/icon/console_icon.png',
  Routes: ['src/layouts/Authorized'],
  authority: [
    AccessRouteId.app_script,
    AccessRouteId.script_user_info_manager,
    AccessRouteId.script_audit_list,
    AccessRouteId.script_hot_script,
    AccessRouteId.script_banner_main_page,
    AccessRouteId.script_money_manager,
  ],
  routes: [
    {
      path: 'merchant',
      icon: 'appstore',
      name: 'merchant',
      authority: [AccessRouteId.app_script],
      routes: [
        {
          path: 'merchant',
          name: 'merchant',
          authority: AccessRouteId.app_script,
          component: './AppScript/Merchant/MerchantManager',
        },
        {
          path: 'script',
          name: 'script',
          authority: AccessRouteId.app_script,
          component: './AppScript/Script/ScriptManager',
        },
        {
          path: 'picture',
          name: 'picture',
          authority: AccessRouteId.app_script,
          component: './AppScript/Picture/PictureManager',
        },
      ],
    },
    {
      path: 'newsList',
      name: 'newsList',
      authority: AccessRouteId.script_moment_list,
      icon: 'redo',
      component: './AppScript/OfficeWeb/MerchantMomentManager',
    },
    {
      path: 'ScriptRecommend',
      name: 'scriptRecommend',
      icon: 'book',
      authority: AccessRouteId.script_hot_script,
      component: './AppScript/Script/ScriptRecommend',
    },

    {
      path: 'Banner',
      icon: 'table',
      name: 'banner',
      authority: [AccessRouteId.script_banner_main_page],
      routes: [
        {
          path: 'MainPageBanner', //剧本杀首页banner管理
          name: 'mainPageBanner', //df
          authority: AccessRouteId.script_banner_main_page,
          component: './AppScript/Banner/MainPageBanner',
        },
        {
          path: 'OpenAd',
          name: 'openAd',
          authority: AccessRouteId.script_banner_main_page, //剧本杀开屏广告
          component: './AppScript/Banner/OpenAd',
        },
      ],
    },
    {
      path: 'Money',
      icon: 'table',
      name: 'money',
      authority: [AccessRouteId.script_money_manager],
      routes: [
        {
          path: 'ScriptBeansWithdrawal', //提现
          name: 'scriptBeansWithdrawal',
          authority: AccessRouteId.script_money_manager,
          component: './AppScript/Money/ScriptBeansWithdrawal',
        },
      ],
    },
    {
      path: 'user',
      icon: 'user',
      name: 'user',
      authority: [AccessRouteId.script_user_info_manager],
      routes: [
        {
          path: 'UserInfoManager',
          name: 'userInfo',
          authority: AccessRouteId.script_user_info_manager,
          component: './AppScript/UserInfoManager/UserInfoManager',
        },
        {
          path: 'reportInfoManager',
          name: 'reportInfo',
          authority: AccessRouteId.script_user_info_manager,
          component: './AppScript/UserInfoManager/ReportInfoManager',
        },
      ],
    },
    {
      path: 'flow',
      icon: 'fund',
      name: 'flow',
      authority: [AccessRouteId.script_audit_list],
      routes: [
        {
          path: 'AuditFlow',
          name: 'auditFlow',
          authority: AccessRouteId.script_audit_list,
          component: './AppScript/AuditFlow/AuditFlowManager',
        },
        {
          path: 'ScriptEditFlowManager',
          name: 'scriptFlow',
          authority: AccessRouteId.script_audit_list,
          component: './AppScript/AuditFlow/ScriptEditFlowManager',
        },
      ],
    },
    // {
    //   path: 'operation',
    //   icon: 'edit',
    //   name: 'operation',
    //   authority: AccessRouteId.script_operation_ScriptNotice,
    //   routes: [
    //     {
    //       path: '/appScript/operation/ScriptNotice',
    //       name: 'ScriptNotice',
    //       component: './AppScript/ScriptNotice/ScriptNoticeList',
    //       // component: './AppScript/Picture/PictureManager',
    //     },
    //   ],
    // },
  ],
};

export default AppWolfRoutes;
