/*
 * @Description: 后台添加热门推荐剧本
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON>yi
 * @Date: 2021-01-05 14:27:31
 * @LastEditors: zhangyi
 * @LastEditTime: 2021-01-05 14:27:31
 */

import { Component, default as React } from 'react';
import { Button, Card, Image, Spin, Table } from 'antd';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { ColumnProps } from 'antd/lib/table';
import { IscriptRecommendList } from '@/pages/AppScript/Script/models/scriptRecommendModel';
import ScriptRecommendAlert from './ScriptRecommendAlert';
import { EditOutlined } from '@ant-design/icons/lib';

export const PAGESIZE = 8;

@connect(({ loading, login, scriptRecommend, }: { loading: IdvaLoading; login: ILogin; scriptRecommend: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptRecommend'],
  scriptRecommendList: scriptRecommend.scriptRecommendList,
  visible: scriptRecommend.visible,
  isUpdate: scriptRecommend.isUpdate,
  script: scriptRecommend.script,
}))

export default class ScriptRecommend extends Component<any, any> {
  constructor(props) {
    super(props);
    const { dispatch,scriptRecommendList } = this.props;

    this.state = {
      currentPage : 1,
    }

    dispatch({
      type: 'scriptRecommend/getScriptRecommendList',
      payload: {},
    });
    dispatch({
      type: 'scriptRecommend/setScriptCount',
      payload: scriptRecommendList.length,
    });
    dispatch({
      type: 'scriptRecommend/getScriptSourceList',
      payload: {},
    });
  }

  render() {
    const { visible, isUpdate,scriptRecommendList,script,isLoading } = this.props;
    return (
      <Spin spinning={!!isLoading}>
        {visible &&
        (isUpdate == 0 || (isUpdate == 1 && script != null)) && <ScriptRecommendAlert/>}
        <PageHeaderWrapper title="近期热门剧本" content={this.renderHeader()}>
          <Card>
            <Table
              scroll={{ x: 1550 }}
              columns={this.columnsRole}
              dataSource={scriptRecommendList}
              loading={!!isLoading}
              bordered={true}
              rowKey={(record, index) => index.toString()}
              pagination={{  // 分页
                pageSize: PAGESIZE,
                current: this.state.currentPage,
                total: this.props.scriptCount,
                onChange: this.changePage,
              }}
            />
          </Card>
        </PageHeaderWrapper>
      </Spin>
    );
  }

  renderHeader() {
    return (
            <Button
              type="primary"
              htmlType="submit"
              onClick={this.onClickCreate}
            >
              添加热门剧本
          </Button>
    );
  }

  columnsRole: ColumnProps<IscriptRecommendList>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '剧本ID',
      dataIndex: 'scriptId',
      key: 'scriptId',
      align: 'center',
    },
    {
      title: '剧本名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '图片展示',
      width: 100,
      align: 'center',
      render: (record) => {
        if (record.img != null) {
          const str = record.img;
          if (str != null && str != '') {
            return (
              <Image src={str} width={80} height={80}/>
            );
          }
        }
        return <div>无资源图</div>;
      }
    },
    {
      title: '操作',
      align: 'center',
      render: (record) => {
        return (
          <Button type="link" onClick={() => { this.onClickUpdate(record) }}><EditOutlined key="setting" />编辑</Button>
        );
      }
    },
    {
      title: '剧本标签',
      dataIndex: 'theme',
      key: 'theme',
    },
    {
      title: '剧本时长',
      dataIndex: 'playTime',
      key: 'playTime',
    },
    {
      title: '剧本人数',
      dataIndex: 'num',
      key: 'num',
    },
    {
      title: '展示',
      dataIndex: 'show',
      align: 'center',
      width: 100,
      render: (val) => {
        return <div>{val == 1 ? '展示' : "不展示"}</div>;
      }
    },
    {
      title: '状态',
      dataIndex: 'delsign',
      align: 'center',
      width: 70,
      render: (val) => {
        return <div>{val == 1 ? '删除' : "正常"}</div>;
      }
    },
    {
      title: '排序权重',
      dataIndex: 'sort',
      key: 'sort',
    },
    {
      title: '剧本标签',
      dataIndex: 'sign',
      key: 'sign',
    },
    {
      title: '剧本发行方',
      dataIndex: 'publisher',
      key: 'publisher',
    },
    {
      title: '剧本发行时间',
      dataIndex: 'publisherTime',
      key: 'publisherTime',
    },
    {
      title: '剧本作者',
      dataIndex: 'author',
      key: 'author',
    },
    {
      title: '剧本跳转地址',
      dataIndex: 'url',
      key: 'url',
    }
  ];

  changePage = (page) => {
    this.setState({ currentPage: page });
  };

  addScript = () => {
    const { dispatch,scriptRecommendList } = this.props;
    dispatch({
      type: 'scriptRecommend/getScriptRecommendList',
      payload: {},
    });

    dispatch({
      type: 'scriptRecommend/setScriptCount',
      payload: scriptRecommendList.length,
    });
  };

  onClickCreate = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptRecommend/getScriptSourceList',
      payload: {},
    });
    dispatch({
      type: 'scriptRecommend/setIsUpdate',
      payload: 0,
    });
    dispatch({
      type: 'scriptRecommend/setVisible',
      payload: true,
    });
  };

  onClickUpdate = (row) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptRecommend/getScriptSourceList',
      payload: { },
    });
    dispatch({
      type: 'scriptRecommend/getScript',
      payload: { id: row.id },
    });
    dispatch({
      type: 'scriptRecommend/setIsUpdate',
      payload: 1,
    });
    dispatch({
      type: 'scriptRecommend/setVisible',
      payload: true,
    });

  };
}
