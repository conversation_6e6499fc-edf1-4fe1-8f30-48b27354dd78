
import React, { Dispatch, ReactElement, SetStateAction, useState } from "react"



export interface IVisibleBinderContentProps {
    visible: boolean,
    setVisible: Dispatch<SetStateAction<boolean>>,
}

export interface IVisibleBinderProps {
    controlElement: (visible: boolean, setVisible: Dispatch<SetStateAction<boolean>>) => ReactElement<any>,
    contentElement: (visible: boolean, setVisible: Dispatch<SetStateAction<boolean>>) => ReactElement<any>
}

const VisibleBinder: React.FC<IVisibleBinderProps> = (props) => {

    const { controlElement, contentElement } = props;
    const [visible, setVisible] = useState(false)

    return (
        <>
            {controlElement(visible, setVisible)}
            {contentElement(visible, setVisible)}
        </>
    )
}

export default VisibleBinder;