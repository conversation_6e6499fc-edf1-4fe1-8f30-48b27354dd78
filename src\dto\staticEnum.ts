import { Upload, Avatar } from 'antd';
/*
 * @Description: 静态类型和枚举类型，一般是状态吗
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-12-19 18:23:38
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-09-26 17:38:59
 */

export const treasureNams = [
  "钻石数量",
  "魅力值",
  "示票卡数量",
  "语音延时卡数量",
  "改名卡数量",
  "负分清零卡数量",
  "贴脸卡数量",
  "床密码房卡数量",
  "双倍经验卡数量",
  "美颜卡数量",
  "自曝卡数量",
  "宝箱钥匙",
  "美颜卡数量",
];
/**
 * @name:
 * @msg: 玩家状态枚举
 * @param {type}
 * @return:
 */
export enum playerStatusEnum {
  normal = 0, //正常
  banned, //封禁
  shutter, //禁闭
  escape, //逃跑
  functionBg, //背景图功能
  shutterNew //新禁闭
}
/**
 * @name:广告类型枚举
 * @msg:
 * @param {type}
 * @return:
 */
export enum adTypeEnum {
  leftTop = 0,
  leftBottom,
  rightTop,
  rightBottom,
  /**
   * 开屏广告
   */
  splash,
  /**
   * 置空
   */
  setNull
}

/**
 * @name:广告位列名
 * @msg:
 * @param {type}
 * @return:
 */
export const adTypeName = ["left_1", "left_2", "right_1", "right_2", "ad"];

/**
 * @name:
 * @msg: 资产记录
 * @param {type}
 * @return:
 */
export enum treasureRecord {
  transaction = 0, //充值记录
  tabnormal,       //消耗记录
  defaultPropsBuy, //道具商城购买
  defaultPropsPay, //道具商城使用
  groupPropsBuy, //公会商城购买
  groupPropsPay, //公会商城使用
  groupReadbag, //公会商城红包
  cpPropsBuy, //CP商城购买
  cpPropsPay, //CP商城使用
  cpPropsGive, //CP游戏内使用
  openBox, //开宝箱
  giftGive, //送礼物
  giftReceive, //接收礼物
  boxGive, //宝箱赠送
  avatarFrame, //个人头像框
  achievement, //个人成就
  backgroundAuthority,//背景板上传权限
  specialEffectBuy,//动效购买
  specialEffectGive,//动效赠送
  specialEffectReceive,//动效接收
  canonRecord,//盛典商城记录
  mallBuyRecord,//商城消费记录
  giftbagReceiveRecord,//礼包领取记录
  giftbagItemReceiveRecord,//礼包物品获得记录
  usePropsRecord,//礼包物品获得记录
  tabnormalNew,// 新钻石变更
}

/**
 * @name:
 * @msg: 苹果支付状态
 * @param {type}
 * @return:
 */
export enum transactionStatus {
  normal = 0, //正常
  abnormal, //苹果支付异常 status > 0
  retry, //苹果支付可以重试 status < 0
}

export const regionType = {
  0: "禁闭区",
  1: "永封区",
  2: "功能区",
  3: "永封功能区"
};

export const opeStatusName = {
  0: "解除禁闭、封号、功能封锁",
  1: "添加禁闭、封号、功能封禁"
};

export enum cpBuyType {
  diamond = 1, //钻石渠道购买
  other = 2 //其他渠道购买
}

export enum boxGainType {
  openBox = 0, //开宝箱
  coinChange, //兑换币兑换
  diamondBuy, //钻石购买
  giving //赠送获得
}

export enum boxOpenType {
  game = -1, //游戏内消费
  daimond = 0, //钻石抽取
  free, //免费
  first, //首抽
  qualified, //限定发放
  props //开箱道具
}

export enum boxGainEType {
  notChange = 0, //未兑换成兑换币
  change //兑换成兑换币
}

export enum giftType { //礼物记录购买渠道
  diamond = 0, //钻石
  changeCoin, //兑换币
  activeCoin //活跃币
}

export enum reportComplete {
  realTime = 0, // 场中实时举报
  endTime, // 结束后举报
  realComplete = 10, // 场中举报检测完成
  endComplete //结束后举报检测完成
}

export enum imWithDrawType {
  processed = 0, //已处理
  untreated, //未处理
  complete, //已完成
  apply //已申请
}

/**
 * 天狼头像框type枚举
 */
export enum wfAvatarFrameType {
  restrict = 0, //限定
  props, //道具
  custom //定制
}
//头像框渠道
export enum wfAvatarFrameChannel {
  default = 0, //普通商城
  boxProps, //宝箱兑换商城
  groupProps, //公会商城
  cpProps, //cp商城
  activityProps //活动
}
//头像框等级  10:S 20:A 30:B 40:C 50:D
export enum wfAvatarFrameLevel {
  SS = 5,
  S = 10,
  A = 20,
  B = 30,
  C = 40,
  D = 50
}

//宝箱
export enum wfBoxVisualType {
  entity = 0, //实物
  gift,//1:礼物
  maskshow, //2天狼秀
  frame, //3头像框
  coin, //4兑换币
  animation,//5动效
  card_item,//6荧光棒/示票卡/延时卡
  card_userprops,//7自爆卡改名卡
}

//宝箱
export enum wfGiftBagContentVisualType {
  gift = 1,//1:礼物
  userprops, //2道具
  activity_coin,//活跃币
  coin, //4兑换币
  animation,//5动效
  card_item,//6荧光棒/示票卡/延时卡
  frame,//7头像框
  diamond,
}

export enum simulatorActionType {
  init = -1,
  enterRoom = 0,
  ready,
  personalMatch,
  quitGameGate,
  reconnectGame,
  getAllSimulatorInfo,
  nightDoneAction,
  nightActSeat,
  skipSpeaking,
  roomInfo,
  matchInfo,
  enterRoomAudience,
}

//礼物
export enum wfPropsType {
  card = 1, //功能性道具
  gift,//2:游戏内礼物
  role, //3身份牌
  maskshow, //4表情包
  bullet, //5弹幕
  gift_out,//6游戏外礼物
  bag,//7礼包
  positon,//8成就展示栏位
  item,//9助力
}

//礼物来源
export enum wfPropsGiftSourceType {
  gift = 1, //普通礼物
  box = 2,//2:宝箱礼物
  group = 3, //3公会商城礼物
  activity = 4, //4活动
  cp = 5, //5 CP礼物
  // group_bag,//6公会战大礼包
  wedding = 7,//7:婚礼礼物
}

//礼物类型
export enum wfPropsGiftType {
  big = 1, //大
  mid,//2 中
  small, //3小
  throw, //4投掷类
}

//头像框类型
export enum wfFrameType {
  approve = 0, //试衣间限定显示
  achievement,//1 商城显示
  consume, //2其他
}

//头像框渠道
export enum wfFrameChannelType {
  normal = 0, //普通商城
  convert,//1 兑换商城
  group, //2公会商城
  cp,//cp商城
  activity,//活动
}

//背景板类型  （role 2000时候有用）
export enum wfAnimationType {
  not = 0, //非背景版
  system,//1 系统背景板
  selfdefine, //2自定义背景
}

//背景板渠道
export enum wfAnimationChannelType {
  props = 0, //道具商城
  group,//1 公会商城
  cp, //2 cp商城
  convert, //3 兑换商城
}

//是否在试衣间显示
export enum wfAvatarFramePreview {
  un = 0, //不显示
  in //显示
}

//是否是动态资源
export enum wfAvatarFrameDynamic {
  un = 0, //不是
  in //是
}
//资源完整性
export enum wfAvatarFrameComplete {
  un = 0, //不是
  in //是
}

/**
 * 所有天狼表的delsign字段枚举
 */
export enum wfDelsign {
  usable = 0, //可用
  unable //不可用
}

/**
 * 所有天狼表的complete字段枚举
 */
export enum wfComplete {
  un = 0, //不完整
  in //完整
}

/**
 * 上传头像框步骤
 */
export enum UploadAvFramStep {
  Base = 0,
  Upload,
  Success
}

/**
 * 上传成就框步骤
 */
export enum UploadAchieveStep {
  Base = 0,
  Upload,
  Success
}

//主播类型
export enum anchorType {
  NOT_ANCHOR = 0,
  IS_NORMAL_ANCHOR
}
//连接类型
export enum adTendType {
  INTERIOR_TEND = 0,
  OUT_TEND
}

export const shutterDic = {
  1: "1.发言贴脸2天2条封1天",
  2: "2.发言贴脸2天4条封2天",
  3: "3.发言贴脸3天6条封3天",
  4: "4.发言辱骂",
  5: "5.背景板头像贴脸",
  6: "6.头像背景板辱骂",
  7: "7.逃跑",
  8: "8.一个自然星期违规次数达到10条",
  9: "9.一个自然月违次数达到30条",
  10: "10.一个自然星期违规次数达到10条和一个自然月违次数达到30条全触发",
  11: "11.视频露脸涉政",
  13: "13.刷分行为",
  14: "14.一星期内举报场次比高于0.5",
  17: "17.恶意影响对局环境",
  18: "18.7天内被多人举报，多次发言场外，贴脸，互通身份，照镜子，刷分等行为",
  19: "19.3天内被多人举报，多次出现挂机行为",
}
export const banAbiliDic = {
  12: "12.禁用世界频道",
  15: "15.禁用自定义头像",
  16: "16.禁用自定义背景板",

}
export const banAbiliForEverDic = {
  1: "1.禁用自定义背景板",
  2: "2.禁用自定义头像",
  3: "3.禁用世界频道",
}

//上传路径配置
export enum UploadPath {
  avatarFrame = 0, // 头像框
  achieve,// 1 成就
  frameNote, // 2 刻字
  advertisement, // 3广告
  banner, // 4 banner
  box,// 5 宝箱图
  gift,// 6 礼物
  animation,// 7 动效
  giftBag,// 8 礼包
  avatarFrame20000, //9 20000头像框
  itemDic, //10 道具库
  groupBadge, //11 公会徽章
  groupFrame, //12 公会徽章框
  groupBanner, //13 公会旗帜
  maskshow, //14 天狼秀
  avatarFramePreview, //15 头像框预览图
  oss = 100, // 上传至 oss
  ossH = 101, // 上传至 oss
  ossWebOffice = 102,
  ossClothing = 103,
  ossTitle = 104,
  ossAchievement = 105,
  ossMerchantPicture = 106,
  ossMerchantScript = 107,
  ossMomentPicture = 109,
  ossNewPlayerRobot= 110,
}

//头像框等级  10:S 20:A 30:B 40:C 50:D
export const avatarFrameLevelList = 
  [
    {
      "id": wfAvatarFrameLevel.SS,
      "name": "SS"
    },
    {
      "id": wfAvatarFrameLevel.S,
      "name": "S"
    },
    {
      "id": wfAvatarFrameLevel.A,
      "name": "A"
    },
    {
      "id": wfAvatarFrameLevel.B,
      "name": "B"
    },
    {
      "id": wfAvatarFrameLevel.C,
      "name": "C"
    },
    {
      "id": wfAvatarFrameLevel.D,
      "name": "D"
    }
  ]

