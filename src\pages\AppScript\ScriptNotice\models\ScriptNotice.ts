import { IerrorMsg } from '@/dto/common';
import { message } from 'antd';
import { IScriptNoticeListRes } from '@/dto/ScriptNotice';

export interface IScriptNoticeModel {
    noticeList: IScriptNoticeListRes[]
}
const init: IScriptNoticeModel = {
    noticeList: [
        {
            id: 1,
            create_time: "2020-12-29T18:14:50.000Z",
            title: "11111",
            content: '<p>如果让我说一个我玩过最难的硬核本，那我一定会选《七个密室》，我是属于人菜瘾还大的硬核玩家。</p><p>整个剧本由7个密室组成，这个剧本在DM和玩家中一共会有6个凶手，玩家们需要做的是证明自己是凶手，找到不是凶手的那个人，和正常剧本的套路完全相反！</p><p><img src="/ueditor/php/upload/image/20201230/1609295403468306.jpg" title="1609295403468306.jpg" alt="2ef7f5bd3b14a4dc1c44ec290e3eedd - Copy.jpg"/></p><p>如果让我说一个我玩过最难的硬核本，那我一定会选《七个密室》，我是属于人菜瘾还大的硬核玩家。</p><p>整个剧本由7个密室组成，这个剧本在DM和玩家中一共会有6个凶手，玩家们需要做的是证明自己是凶手，找到不是凶手的那个人，和正常剧本的套路完全相反！</p><p><br/></p>'
        }
    ]
}

export default {
    namespace: 'scriptNotice',

    state: init,
    effects: {},
    reducers: {}
}