@import '~antd/lib/style/themes/default.less';
@import '~@/utils/utils.less';

.searchError {
  color: @alert-error-icon-color;
  margin-top: 10px;
  margin-bottom: 10px;
}

.setOn {
  color: @primary-color;
}
.setOff {
  color: @alert-error-icon-color;
}

.customModalBody :global(.ant-modal-header) {
  padding: 10px;
}

.modalForm {
  display: flex;
  flex-direction: column;
  .formItem {
    display: flex;
    flex-direction: row;
    margin-top: 4px;
    justify-content: center;
    align-self: flex-start;
    .title {
      margin-left: 15px;
      margin-top: 3px;
      width: 120px;
      font-weight: bold;
    }
    .content {
      width: 360px;
    }
    .toggle {
      margin-left: 15px;
    }
  }
}

.tableInc {
  color: @primary-color;
}
.tableDec {
  color: magenta;
}
