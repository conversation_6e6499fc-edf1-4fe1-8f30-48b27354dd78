/*
 * @Description: ai
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-01-31 11:01:15
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2022-02-28 09:31:08
 */
import React from 'react';
import { AccessRouteId } from './accessRouteCof';

//import deductionRecord from '@/pages/AppWolf/ScoreViolation/models/deductionRecord';

const AppWolfRoutes = {
  path: 'appMst',
  name: 'appMst',
  icon: 'http://werewolf-resource.53site.com/s/tag/newPlayerRobot_1693364301009.png?1693364329262',
  Routes: ['src/layouts/Authorized'],
  authority: AccessRouteId.app_mst,
  routes: [
    {
      path: '/AppMst/SetModel',
      icon: 'api',
      name: 'ModelManager',
      authority: AccessRouteId.app_mst,
      component: './AppMst/SetModel',
    },
    {
      path: '/appMst/mstAddImage',
      // path: 'mst',
      icon: 'api',
      name: 'mst',
      authority: AccessRouteId.app_mst,
      component: './AppWolf/AppManager/mstAddImage',
    },
   
  ],
};

export default AppWolfRoutes;
