/* eslint-disable dot-notation */
/* eslint-disable react/jsx-filename-extension */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/sort-comp */

import React, { Component } from 'react';
import { connect } from 'dva';
import { Modal, Switch, Input, Select, message, AutoComplete, Button, Radio, Form } from 'antd';
import * as styles from './ServerMonitorEditorModal.less';
import { getCateName, getOptionList, getSelectName } from '@/utils/mallTool';

// eslint-disable-next-line react/no-unused-prop-types
@connect(
  ({
    loading,
    login,
    apiServerMonitor,
  }: {
    loading: IdvaLoading;
    login: ILogin;
    apiServerMonitor: any;
  }) => ({
    uid: login.uid,
    isLoading: loading.models['apiServerMonitor'],
    list: apiServerMonitor.list,
    typeList: apiServerMonitor.typeList,
    userList: apiServerMonitor.userList,
    businessList: apiServerMonitor.businessList,
    serverList: apiServerMonitor.serverList,
    envList: apiServerMonitor.envList,
    formatList: apiServerMonitor.formatList,
    systemList: apiServerMonitor.systemList,
  })
)
class ServerMonitorEditorModal extends React.Component<any, any> {
  private formRef: RefObject<Form> = React.createRef();
  constructor(props) {
    super(props);
    const { dataModelType, current } = props;
    let currentUserValue;
    let currentBusinessValue;
    let currentServerValue;
    if (dataModelType == 1) {
      currentUserValue = `ID: ${current.user_id} 名称: ${current.user_name}`;
      currentBusinessValue = `ID: ${current.business_id} 名称: ${current.business_name}`;
      currentServerValue = `ID: ${current.server_id} 名称: ${current.server_name}`;
    } else {
      currentUserValue = '';
      currentBusinessValue = '';
      currentServerValue = '';
    }
    this.state = {
      currentUserValue,
      currentBusinessValue,
      currentServerValue,
    };
  }

  render() {
    const { dataModelType, current, isLoading } = this.props;
    let title = '';
    if (dataModelType == 1) {
      title = `编辑 ID【${current.id}】`;
    } else {
      title = '创建';
    }
    return (
      <Modal
        width={600}
        bodyStyle={{ padding: '5px 10px 5px 10px' }}
        closable={false}
        maskClosable={false}
        confirmLoading={!!isLoading}
        centered
        title={title}
        footer={null}
        visible={dataModelType > 0}
      >
        <div>{dataModelType > 0 && this.renderEditModal()}</div>
      </Modal>
    );
  }

  renderEditModal = () => {
    const { dataModelType, current, envList, formatList } = this.props;
    const init =
      dataModelType == 1
        ? {
            ...current,
            user_id_str: getSelectName(current.user_id, this.props.userList),
            business_id_str: getSelectName(current.business_id, this.props.businessList),
            server_id_str: getSelectName(current.server_id, this.props.serverList),
          }
        : { status: 1, type: 0 };

    const layout = {
      labelCol: { span: 7 },
      wrapperCol: { span: 16 },
    };
    const tailLayout = {
      wrapperCol: { offset: 16, span: 16 },
    };
    const bottom = '10px';

    return (
      <div>
        <Form
          {...layout}
          name="award"
          ref={this.formRef}
          initialValues={init}
          onFinish={this.onClickOk}
          onFinishFailed={this.onFinishFailed}
          style={{ marginBottom: bottom }}
        >
          <Form.Item style={{ marginBottom: bottom }} label="服务名" name="name">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="服务类型" name="type_id">
            <Radio.Group>
              {this.props.typeList.map((item, index) => (
                <Radio.Button value={item.id}>{item.name}</Radio.Button>
              ))}
            </Radio.Group>
          </Form.Item>
          <Form.Item label="业务分类" name="business_id_str">
            <AutoComplete
              allowClear
              className={styles.content}
              // eslint-disable-next-line react/destructuring-assignment
              options={getOptionList(this.props.businessList)}
              onSelect={this.onSelectBusiness}
              onChange={this.onChangeBusiness}
              // eslint-disable-next-line react/destructuring-assignment
              value={this.state.currentBusinessValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) => option.value.indexOf(inputValue) !== -1}
            />
          </Form.Item>
          <Form.Item label="服务器" name="server_id_str">
            <AutoComplete
              allowClear
              className={styles.content}
              // eslint-disable-next-line react/destructuring-assignment
              options={getOptionList(this.props.serverList)}
              onSelect={this.onSelectServer}
              onChange={this.onChangeServer}
              // eslint-disable-next-line react/destructuring-assignment
              value={this.state.currentserverValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) => option.value.indexOf(inputValue) !== -1}
            />
          </Form.Item>
          <Form.Item label="环境" name="env_id">
            <Select
              className={styles.content}
              placeholder="请选择环境"
              // defaultValue={getCateName(current.env_id, envList)}
            >
              {envList.map((item, index) => {
                return (
                  <Option key={`${item.id} + ${item.name}`} value={item.id}>
                    {item.name}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="产品版本" name="version">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="git分支" name="git_branch">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="Jenkin链接" name="jenkin_link">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="Gitlab链接" name="gitlab">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="所用端口号" name="port">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="容器名称" name="docker_name">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="容器卷路径" name="docker_volum">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="开发语言" name="language">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="程序路径" name="path">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="日志路径" name="log_path">
            <Input />
          </Form.Item>
          <Form.Item
            style={{ marginBottom: bottom }}
            label="日志检测超时时间(秒)"
            name="max_second"
          >
            <Input />
          </Form.Item>
          <Form.Item label="日志时间格式" name="time_id">
            <Select
              className={styles.content}
              placeholder="请选择日志时间格式"
              // defaultValue={getCateName(current.time_id, formatList)}
            >
              {formatList.map((item, index) => {
                return (
                  <Option key={`${item.id} + ${item.name}`} value={item.id}>
                    {item.name}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="检测命令" name="check">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="启动命令" name="start">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="重启命令" name="restart">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="自研|三方" name="self">
            <Radio.Group>
              <Radio.Button value={1}>自研</Radio.Button>
              <Radio.Button value={3}>三方</Radio.Button>
            </Radio.Group>
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="业务描述" name="description">
            <Input />
          </Form.Item>
          <Form.Item style={{ marginBottom: bottom }} label="备注" name="remark">
            <Input.TextArea />
          </Form.Item>
          <Form.Item label="负责人" name="user_id_str">
            <AutoComplete
              allowClear
              className={styles.content}
              // eslint-disable-next-line react/destructuring-assignment
              options={getOptionList(this.props.userList)}
              onSelect={this.onSelectUser}
              onChange={this.onChangeUser}
              // eslint-disable-next-line react/destructuring-assignment
              value={this.state.currentUserValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) => option.value.indexOf(inputValue) !== -1}
            />
          </Form.Item>
          <Form.Item {...tailLayout}>
            <Button onClick={this.props.onClickClose}>取消</Button>
            <Button style={{ marginLeft: 10 }} type="primary" htmlType="submit">
              提交
            </Button>
          </Form.Item>
        </Form>
      </div>
    );
  };

  onSelectBusiness = value => {
    console.log('value', value);
    this.formRef.current.setFieldsValue({
      business_id_str: value,
    });
  };

  onChangeBusiness = (data: string) => {
    this.setState({ currentBusinessValue: data });
  };

  onSelectServer = value => {
    console.log('value', value);
    this.formRef.current.setFieldsValue({
      server_id_str: value,
    });
  };

  onChangeServer = (data: string) => {
    this.setState({ currentserverValue: data });
  };

  onSelectUser = value => {
    console.log('value', value);
    this.formRef.current.setFieldsValue({
      user_id_str: value,
    });
  };

  onChangeUser = (data: string) => {
    this.setState({ currentUserValue: data });
  };

  onFinishFailed = errorInfo => {
    console.log('Failed:', errorInfo);
    // eslint-disable-next-line react/destructuring-assignment
    if (this.props.dataModelType == 1) {
      message.error('修改失败!');
    } else {
      message.error('新增失败!');
    }
  };

  onClickOk = values => {
    console.log('Success:', values);
    const { dispatch, current, dataModelType, serverList, systemList } = this.props;
    let req: any = {
      id: current.id,
      name: values.name,
      gitlab: values.gitlab,
      server_name: values.server_name,
      version: values.version,
      git_branch: values.git_branch,
      jenkin_link: values.jenkin_link,
      port: values.port,
      docker_name: values.docker_name,
      docker_volum: values.docker_volum,
      language: values.language,
      path: values.path,
      log_path: values.log_path,
      check: values.check,
      start: values.start,
      restart: values.restart,
      description: values.description,
      max_second: values.max_second,
      remark: values.remark,
      type_id: values.type_id,
      type_name: getCateName(values.type_id, this.props.typeList),
      self: values.self,
    };

    if (values.env_id && values.env_id > 0) {
      req = {
        ...req,
        env_id: values.env_id,
        env_name: getCateName(values.env_id, this.props.envList),
      };
    }

    if (values.time_id && values.time_id > 0) {
      let format = null;
      for (let i = 0; i < this.props.formatList.length; i++) {
        const element = this.props.formatList[i];
        if (element.id == values.time_id) {
          format = element;
          break;
        }
      }
      if (format) {
        req = {
          ...req,
          time_id: values.time_id,
          time_str: format.name,
          time_format: format.time_format,
          time_pattern: format.time_pattern,
        };
      }
    }

    if (values.business_id_str && values.business_id_str.length > 0) {
      const business_id = Number(
        values.business_id_str.substring(4, values.business_id_str.indexOf(' 名称'))
      );
      const business_name = values.business_id_str.substring(
        values.business_id_str.indexOf('名称') + 4,
        values.business_id_str.length
      );
      console.log('business_id', business_id);
      console.log('business_name', business_name);

      if (!business_name || business_name.length <= 0) {
        message.error('业务类型填写错误！');
        return;
      }

      req = {
        ...req,
        business_id,
        business_name,
      };
    }

    if (values.server_id_str && values.server_id_str.length > 0) {
      let selectServer;
      const server_id = Number(
        values.server_id_str.substring(4, values.server_id_str.indexOf(' 名称'))
      );
      const server_name = values.server_id_str.substring(
        values.server_id_str.indexOf('名称') + 4,
        values.server_id_str.length
      );
      console.log('server_id', server_id);
      console.log('server_name', server_name);
      for (let i = 0; i < serverList.length; i++) {
        const element = serverList[i];
        if (element.id == server_id) {
          selectServer = element;
          break;
        }
      }

      if (!server_name || server_name.length <= 0 || !selectServer) {
        message.error('业务类型填写错误！');
        return;
      }

      req = {
        ...req,
        server_id,
        server_name,
        outer_ip: selectServer.outer_ip,
        inner_ip: selectServer.inner_ip,
        system_id: selectServer.system_id,
        system_name: selectServer.system_name,
      };
    }

    if (values.user_id_str && values.user_id_str.length > 0) {
      const user_id = Number(values.user_id_str.substring(4, values.user_id_str.indexOf(' 名称')));
      const user_name = values.user_id_str.substring(
        values.user_id_str.indexOf('名称') + 4,
        values.user_id_str.length
      );
      console.log('user_id', user_id);
      console.log('user_name', user_name);

      if (!user_name || user_name.length <= 0) {
        message.error('负责人填写错误！');
        return;
      }

      req = {
        ...req,
        user_id,
        user_name,
      };
    }

    console.log('req,', req);

    if (dataModelType == 1) {
      dispatch({
        type: 'apiServerMonitor/updateServerMonitor',
        payload: req,
      }).then(() => {
        this.props.onClickClose();
        this.props.refreshView();
      });
    } else {
      dispatch({
        type: 'apiServerMonitor/insertServerMonitor',
        payload: req,
      }).then(() => {
        this.props.onClickClose();
        this.props.refreshView();
      });
    }
  };
}

export default ServerMonitorEditorModal;
