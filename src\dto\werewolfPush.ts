/*
 * @Description: 天狼推送数据建模
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-16 16:06:38
 * @LastEditors: hammercui
 * @LastEditTime: 2020-03-19 15:39:08
 */

export enum PushReceiveType {
    Users = 1, //给某些个用户发推送
    New3DUnlogin ,//三天前新注册用户一直没开局的
    Old15DUnlogin,//老用户沉默15天未登录的（老用户标准：开局10局以上）
    Old30DUnlogin,//老用户沉默30天未登录的
    Old3MLogin,//3个月登陆过的用户(默认)
}

//推送状态
export enum PushState {
    Ready=0,//准备中
    Pusing ,//推送中
    Success,//推送成功
    FailMysqlEmpty,//推送失败-mysql为空
    FailNetError,//推送失败-net为空
    FailUnKnow//推送失败-
}

export const PushTypeName = [
	'无',
	'给某些个用户发推送',
	'三天前新注册用户一直没开局的',
	'老用户沉默15天未登录的',
	'老用户沉默30天未登录的',
	'3个月登陆过的用户(默认)'
];
export const PushStateName = [
	'准备中',
	'推送中',
	'推送成功',
	'推送失败，mysql为空',
	'推送失败，net error',
	'推送失败，未知错误'
];


export class IpushShortInfo{
    timestamp: number;
    state: PushState;
}

export class IpushRecord {
    id: number;
    admin_id: number;
    content: string;
    receive?: string;
    receive_type: PushReceiveType;
    push_time: string;
    title: string;
    push_done_time?: string;
    push_state?: PushState;
    receive_plan_count?: number;
    receive_done_count?: number;
}

//请求php推送结构
export class IpushPhpReq{
    luckynumber: string; //固定npw
    alert: string; //内容
    title: string; //标题
    deviceList: string[]; //deviceToken列表
}

//统一response结构
export class Iresp{
    code: number;
    data?: any;
    msg: string;
}

export class IdeviceToken{
    deviceToken: string;
}

export class IpushDelReq {
    id: number;
}
