

//总信息
export interface IanchorShowDayReq {
    season: number; //第几季
    season_day: number
}

//每天信息
export interface IanchorShowDayResp {
    dayStatus: IanchorShowDayStatus
    scoreList: IanchorShowScore[]
}

//每天状态
export interface IanchorShowDayStatus {
    roundWinList: number[];//两局获胜阵营0未选择，1好人，2狼人
    is_votable: number
    season_day: number
    anchor_ready: number
}

//当天主播积分
export interface IanchorShowScore {
    id: number;
    anchor_name: string;
    avatar_url: string;
    room_url: string;
    score: number;
    is_king: number;
}

//请求变更竞猜状态
export interface IchangeVotableReq {
    season: number;
    season_day: number; //
    is_votable: number
}
export interface IchangeReadyReq {
    season: number;
    season_day: number; //
    anchor_ready: number
}

export interface IchangeKingReq {
    season: number;
    season_day: number; //
    king_anchor_id: number;// 狼王主播id
}

export interface IchangeScoreReq {
    season: number;
    season_day: number; //
    anchor_id: number;// 狼王主播id
}


export interface IchangeScoreReq {
    season: number;
    season_day: number; //
    anchor_id: number;// 狼王主播id
}

export interface IchangeWinCampReq {
    season: number;
    season_day: number; //
    round: number;// 第几局
    win_camp: number //获胜阵营 1神 2狼
}