/**
 * @name:
 * @description:
 * @author: fanyonghui
 * @time: 2021-08-16 13:15:42
 */

export interface IMarketTradeData {
  uid: number,
  avatarframeId: number;
  avatarframeImg: string;
  name: string;
  coinId: number;
  coinNum: string;
  marketId: string;
  historyId: string;
  fromUserName: string;
  fromUserId: number;
  fromUserIsOfficial: number;
  toUserName: string;
  toUserId: number;
  toUserIsOfficial: number;
}


 export interface IMarketTradeList{
  dataArray: IMarketTradeData[];
 }



export interface ImarketUserSaleItem {
   avatarframeId: number;
   avatarframeImg: string;
   buyTime: string;
   coinId: number;
   coinNum: number;
   displayEndTime: string;
   fromNobleIsLight : number;
   fromNobleNo:number;
   fromUserId : string;
   fromUserIsOfficial: string;
   fromUserName: string;
   historyId: string;
   marketId: number;
   name: string;
   num: number;
   toUserId: string;
   toUserIsOfficial: string;
   toUserName: string;
   uid: string;
   fromGroupName: string;
   toGroupName: string;
}

export interface ImarketUserSale {
  list:ImarketUserSaleItem[];
  allCount:number;
}


export interface ImarketUserSaleList{
  code: number;
  message:string;
   data: ImarketUserSale;
}

export interface IOfficerAccoutDesc{
  earnTotalNum: number;
  costTotalNum:number;
  accountnum:number;
}

export interface UpHeader{
  code: number;
  message:string;
}













