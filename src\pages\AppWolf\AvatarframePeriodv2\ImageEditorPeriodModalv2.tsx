/*
 * @Description: 宝箱管理-编辑模态页
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2019-11-26 16:56:14
 * @LastEditors: hammercui
 * @LastEditTime: 2020-09-03 09:34:04
 */

import {default as React} from 'react';
import { connect } from 'dva';
import {IavatarFramePeriodv2} from './models/avatarframePeriodv2';
import { ILogin } from '@/models/login';
import AppList from '../../../pages/AppList';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Upload, Row, Col, Button, Modal, message } from 'antd';
import * as styles from './ImageEditorPeriodModalv2.less';
import { UploadPath } from '@/dto/staticEnum';

export interface IavatarFramePeriodProps {
  dispatch?: Function;
	isLoading?: boolean;
  uid?: number;
  // periodList: IavatarFramePeriodv2[]
  visibleLead?: boolean;
  modelType?: number;
  imgModelType?: number;
  selectItem?: IavatarFramePeriodv2;
  // selectId: number;
}

export interface IuploadImg {
  id : number;
  url: string;
  name: string;
  status: boolean;
  type: number;
  fileExtension: string;
  showName: string;
  needInsertDocument: number;
}

@connect(({ loading, login, avatarframePeriodv2 }: { loading: IdvaLoading; login: ILogin; avatarframePeriodv2: IavatarFramePeriodProps }) => ({
	uid: login.uid,
  isLoading: loading.models['avatarframePeriodv2'],
  // periodList: avatarframePeriodv2.periodList, 
  modelType: avatarframePeriodv2.modelType,
  imgModelType: avatarframePeriodv2.imgModelType,
  selectItem:avatarframePeriodv2.selectItem,
}))

class ImageEditorPeriodModalv2 extends React.Component<IavatarFramePeriodProps> {
	constructor(props) {
		super(props);
		const {selectId, selectItem, imgModelType} = props;

		//编辑
		if (imgModelType == 1) {

      const item: IavatarFramePeriodv2 = selectItem;

      const uploadImgList : IuploadImg[] = this.getUploadImgList(item);

			this.state = {
          id: item.id,
          name: item.name,
          uploadImgList,
          pic_mob: "payment_head_portrait_" + item.id,
          pic_pc: "payment_head_portrait_for_pc_" + item.id,
          pic_word: "payment_head_portrait_word_" + item.id,
			}
		}
	}

  render() {
    const { imgModelType, isLoading } = this.props;
    const title = '上传资源【' + this.state.name + '】资源图片';
    return (
      <Modal
        closable={true}
        maskClosable={false}
        confirmLoading={!!isLoading}
        centered={true}
        title={title}
        width="750px"
        onCancel={this.onCloseModel}
        onOk={this.onClickOk}
        cancelText="取消"
        okText="关闭"
        visible={imgModelType > 0 ? true : false}
        footer={null}
      >
        <div>
          {this.renderStaticResource()}
          <div className={styles.submit_btn}>
            <Button className={styles.submit_btn} type="primary" onClick={this.onClickOk} loading={!!isLoading}>关闭</Button>
          </div>
        </div>
      </Modal>
    );
  }

  /**
   * 静态资源上传
   */
  renderStaticResource = () => {
    const NowTime:number=Date.parse(new Date().toString()); 
    console.log(NowTime);
    return (
      <div className={styles.resources_div}>
        <Row className={styles.row_div} type="flex" justify="space-around">
          {
            this.state.uploadImgList.map((item, index) => {
              if (item.status == true) {
                return (
                  <div key={index}>
                    <Col span={4}>
                      {this.renderShowImgModel(item,NowTime)}
                    </Col>
                  </div>
                );
              }else{
                return (
                  <div key={index}>
                    <Col span={4}>
                      {this.renderUploadModel(item,NowTime)}
                    </Col>
                  </div>
                );
              }})
      } 
        </Row>
      </div>
    );
  }

  /**
   * 展示图片模块
   */
  renderShowImgModel = (item : IuploadImg,NowTime:number)  => {

    if (item.fileExtension === "mp3") {
      return (
        <div className={styles.show_img_div}>
          <div className={styles.show_upload}>
            <div className={styles.text_div_done}>{"音频文件上传成功 路径 \r\n" + item.url}</div>
          </div>
        </div>
      );
    }else {
      return (
        <div className={styles.show_img_div}>
          <div className={styles.show_upload}>
            <img className={styles.show_upload_done} src={item.url}></img>
            <div className={styles.text_div_done}>{item.name + "." + item.fileExtension}</div>
            <div className={styles.text_div_done}>{item.showName}</div>
          </div>
        </div>
      );
    }
  }

  /**
   * 上传模块
   */
  renderUploadModel = (item : IuploadImg,NowTime:number) => {
    const uploadUrl = '/megaupload';
    const timeMark = NowTime;
    console.log(timeMark)
    return (
      <div className={styles.show_img_div}>
        <div className={styles.show_upload}>
          <Upload action={uploadUrl}
          headers={{ 'Access-Control-Allow-Origin': '*', 'X-Requested-With': null }}
          withCredentials={true}
          data={this.postUpdata(item.name+"_"+timeMark)}
          listType='picture-card'
          showUploadList={false}
          className="avatar-uploader"
          onChange={(value) => this.onChange(value,item,timeMark)}>
          {this.renderUploadButton()}
        </Upload>
        </div>
        <div className={styles.text_div}>{item.name}</div>
        <div className={styles.text_div}>{item.showName}</div>
      </div>
    );
  }

  /**
   * 上传图片时的post数据
   */
  postUpdata = (name) => {
    
    const obj = {
      type: 102,
      name
    }
    console.log(name);
    return obj;
  }

  /**
   * 上传图片时图片变换
   * @param info
   */
  onChange = (info: UploadChangeParam, item : IuploadImg,timeMark:number) => {
    if (info.file.status === 'done') {
      console.log(timeMark);
      this.showImgUpload(info, item,timeMark);
      this.handleSubmitEdit(item,timeMark);  
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
    }
  }

  showImgUpload = (info: UploadChangeParam, item : IuploadImg,timeMark: number) => {
    if (info.file.status === 'done' && info.file && info.file.type) {
      message.success(`${info.file.name} 上传成功`);
      const extension = info.file.type.replace("image/", "")
      console.log("infoinfo", extension);

      const uploadImgList : IuploadImg[] = _.cloneDeep(this.state.uploadImgList);
  
      for(let i = 0; i < uploadImgList.length; i++) {
        if (item.id == uploadImgList[i].id) {
          item.status = true;
          item.id = uploadImgList[i].id;
          item.type = uploadImgList[i].type;
          item.fileExtension = extension;
          item.name = uploadImgList[i].name;
          item.url = this.getAnimationImgStr(item,timeMark);
          uploadImgList[i] = item;
          break;
        }
      }
      this.setState({ uploadImgList});
    }
  }

  /**
   * 上传按钮
   */
  renderUploadButton = () => {
    return (
      <div>
        <LegacyIcon type={this.state.isUploading ? 'loading' : 'plus'} />
        <div className="ant-upload-text">上传</div>
      </div>
    );
  }

  getAnimationImgStr = (item : IuploadImg,timeMark:number) => {
    const urlstring="h5/office/";
    const string = AppList.imageAvatarPeriodUrlv2+urlstring+ item.name + "_" +timeMark+"." + item.fileExtension;
    console.log(string)
    return string
  };

  getUploadImgList = (item : IAnimationReq) => {

    let uploadImgList : IuploadImg[];
    {
      uploadImgList =
      [
        {
          id:1,
          url: "",
          name: "head_p_" + item.id,
          status: false,
          type:102,
          fileExtension:"png",
          showName:"头像框图",
          needInsertDocument:0,
        },
        {
          id:2,
          url: "",
          name: "head_p_for_pc_" + item.id,
          status: false,
          type:102,
          fileExtension:"png",
          showName:"背景图",
          needInsertDocument:0,
        },
        {
          id:3,
          url: "",
          name: "head_p_word_" + item.id,
          status: false,
          type:102,
          fileExtension:"png",
          showName:"文字描述图",
          needInsertDocument:0,
        }
      ]
    }
    return uploadImgList;

  };

  onChangeVisualNo = (value) => {
    this.setState({ type: value });
  };

  checkNullStr = (value) => {
    return value ? value : "";
  };

	//提交编辑
	handleSubmitEdit = (item : IuploadImg,timeMark:number) => {
    const { dispatch } = this.props;
    const urlstring="h5/office/"
    const request: IavatarFramePeriod = {id: this.state.id};
    const fileName =  urlstring+item.name+"_"+timeMark + "." + item.fileExtension;
    if (item.id == 1) {
      request.pic_mob = fileName;
    }else if(item.id == 2){
      request.pic_pc = fileName;
    }else{
      request.pic_word = fileName;
    }
  
		dispatch({
      type: 'avatarframePeriodv2/updatePeriodImg',
      payload: request
    });
	};

	//关闭模态页
	onCloseModel = () => {
		const { dispatch } = this.props;
		dispatch({
			type: 'avatarframePeriodv2/setImgModelType',
			payload: 0
		});
	};

	onClickOk = () => {
	  this.onCloseModel();
	};
}

export default ImageEditorPeriodModalv2;
