/*
 * @Description: 狼人杀路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-01-31 11:01:15
 * @LastEditors: hammercui
 * @LastEditTime: 2019-01-31 12:00:27
 */
import { AccessRouteId } from './accessRouteCof';

const AppLiaoRoutes = {
  path: 'appLiao',
  name: 'appLiao',
  icon: 'http://p15.qhimg.com/t010f2b2879b9b1a3e2.png',
  Routes: ['src/layouts/Authorized'],
  authority: AccessRouteId.app_liaoliao,
  routes: [
    // Drawings
    {
      path: 'drawings',
      icon: 'pay-circle',
      name: 'drawings',
      authority: AccessRouteId.liao_drawings,
      component: './AppLiao/Drawings',
    },
  ],
};

export default AppLiaoRoutes;
