/*
 * @Description:
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2020-12-24 13:27:08
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-10-20 15:20:59
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  Row,
  Col,
  Switch,
  Space,
  Table,
  Tag,
  Typography,
  Modal,
  Input,
  Popconfirm,
  AutoComplete,
  Select,
} from 'antd';
const { TextArea } = Input;
const { Text } = Typography;
import { PlusOutlined, CloudUploadOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import * as styles from './ServerMonitor.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import { ColumnProps } from 'antd/lib/table';
import { getTimeStr } from '@/utils/momentTool';
import { getGiftBagContentImgStr } from '@/utils/mallTool';
import { getCateName, getOptionList, getSelectName } from '@/utils/mallTool';
import ServerMonitorEditorModal from './ServerMonitorEditorModal';
import ServerMonitorSingleEditorModal from './ServerMonitorSingleEditorModal';
import ServerMonitorShowModal from './ServerMonitorShowModal';
import ServerMonitorDictEditorModal from './ServerMonitorDictEditorModal';
import { checkNotNull } from '@/utils/emptyTool';
import { message } from 'antd/es';
import { dispatchDictList, monitorDicEnum } from './models/apiServerMonitor';
export const PAGESIZE = 8;

@connect(
  ({
    loading,
    login,
    apiServerMonitor,
  }: {
    loading: IdvaLoading;
    login: ILogin;
    apiServerMonitor: any;
  }) => ({
    uid: login.uid,
    isLoading: loading.models['apiServerMonitor'],
    list: apiServerMonitor.list,
    typeList: apiServerMonitor.typeList,
    optionUserList: apiServerMonitor.optionUserList,
    optionBusinessList: apiServerMonitor.optionBusinessList,
    optionTypeList: apiServerMonitor.optionTypeList,
    envList: apiServerMonitor.envList,
    formatList: apiServerMonitor.formatList,
  })
)
export default class ServerMonitor extends Component<any, any> {
  private timer;
  constructor(props) {
    super(props);

    this.state = {
      dataModelType: -1,
      singleDataModelType: -1,
      showModelType: -1,
      monitorDicModelType: -1,
      optionUserId: 0,
      optionBusinessId: 0,
      optionTypeId: 0,
      serverSearch: '',
      otherSearch: '',
      selectedRowKeys: [],
      singleEditIds: [],
      currentPage: 1,
      merchantColumns: this.makeColumns(),
    };

    this.refreshView();
    dispatchDictList(props.dispatch, -1);
  }

  // componentDidMount() {
  //   this.dispatchListTimer(true);
  // }

  // componentWillUnmount() {
  //   clearTimeout(this.timer);
  // }

  render() {
    const { isLoading } = this.props;
    return <Spin spinning={!!isLoading}>{this.renderGift()}</Spin>;
  }

  renderGift() {
    const { dispatch, list, isLoading, optionUserList, optionBusinessList, optionTypeList } = this.props;
    const {
      dataModelType,
      singleDataModelType,
      monitorDicModelType,
      current,
      showModelType,
      selectedRowKeys,
      singleEditIds,
    } = this.state;
    const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
      this.setState({ selectedRowKeys: newSelectedRowKeys });
    };

    const rowSelection = {
      selectedRowKeys,
      onChange: onSelectChange,
    };

    return (
      <div>
        <Space direction="vertical">
          <Space direction="horizontal">
            <Button type="primary" onClick={() => this.onClickCreate()}>
              新增监控
            </Button>
          </Space>
          <Space direction="horizontal">
            <div style={{ width: 50 }}>服务器:</div>
            <Input value={this.state.serverSearch} onChange={this.onChangeServer} />
            <div style={{ width: 40 }}>其他:</div>
            <Input value={this.state.otherSearch} onChange={this.onChangeOther} />
            <div style={{ width: 60 }}>服务类型:</div>
            {optionTypeList && (
              <Select style={{ width: 200 }} defaultValue={0} onChange={this.onChangeOptionTypeId}>
                {optionTypeList.map((item, index) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
              </Select>
            )}
            <div style={{ width: 60 }}>业务类型:</div>
            {optionBusinessList && (
              <Select
                style={{ width: 200 }}
                defaultValue={0}
                onChange={this.onChangeOptionBusinessId}
              >
                {optionBusinessList.map((item, index) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
              </Select>
            )}
            <div style={{ width: 50 }}>负责人:</div>
            {optionUserList && (
              <Select style={{ width: 200 }} defaultValue={0} onChange={this.onChangeOptionUserId}>
                {optionUserList.map((item, index) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
              </Select>
            )}
            <Button type="primary" onClick={() => this.refreshView()}>
              搜索
            </Button>
          </Space>
          {selectedRowKeys.length > 0 && (
            <Space direction="horizontal">
              <Button type="primary" onClick={() => this.onClickSingleEdit(1)}>
                更改产品版本
              </Button>
              <Button type="primary" onClick={() => this.onClickSingleEdit(2)}>
                更改git分支
              </Button>
            </Space>
          )}
        </Space>
        <Spin spinning={!!this.props.isLoading}>
          {dataModelType > 0 && (
            <ServerMonitorEditorModal
              dataModelType={dataModelType}
              current={current}
              onClickClose={this.onClickClose}
              refreshView={this.dispatchList}
            />
          )}
          {monitorDicModelType > 0 && (
            <ServerMonitorDictEditorModal
              dataModelType={monitorDicModelType}
              onClickClose={this.onClickClose}
              refreshView={()=>dispatchDictList(dispatch, monitorDicModelType)}
            />
          )}
          {singleDataModelType > 0 && (
            <ServerMonitorSingleEditorModal
              dataModelType={singleDataModelType}
              singleEditIds={singleEditIds}
              onClickClose={this.onClickClose}
              refreshView={this.dispatchList}
            />
          )}
          {showModelType > 0 && (
            <ServerMonitorShowModal
              dataModelType={showModelType}
              current={current}
              onClickClose={this.onClickClose}
              refreshView={this.dispatchList}
            />
          )}
        </Spin>
        <Card style={{ marginTop: 10 }}>
          <Table
            scroll={{ x: 2000 }}
            rowSelection={rowSelection}
            columns={this.state.merchantColumns}
            dataSource={list}
            loading={!!isLoading}
            bordered={true}
            rowKey={(record, index) => index.toString()}
            pagination={{
              // 分页
              pageSize: PAGESIZE,
              current: this.state.currentPage,
              total: list.length,
              onChange: this.changePage,
            }}
          />
        </Card>
      </div>
    );
  }

  makeColumns() {
    const columnsMerchant: ColumnProps<any>[] = [
      // {
      //   title: 'gitlab超链接',
      //   dataIndex: 'gitlab',
      //   key: 'gitlab',
      //   align: 'center',
      //   render: (val, record, index) => {
      //     if (val) {
      //       return (
      //         <a key={val} href={val} target="_blank">
      //           链接
      //         </a>
      //       );
      //     } else {
      //       return <>/</>;
      //     }
      //   },
      // },
      // { title: 'git版本号', dataIndex: 'git_branch', key: 'git_branch', align: 'center' },
      // {
      //   title: 'Jenkin链接',
      //   dataIndex: 'jenkin_link',
      //   key: 'jenkin_link',
      //   align: 'center',
      //   render: (val, record, index) => {
      //     if (val) {
      //       return (
      //         <a key={val} href={val} target="_blank">
      //           链接
      //         </a>
      //       );
      //     } else {
      //       return <>/</>;
      //     }
      //   },
      // },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        render: (val, record, index) => {
          let component = [];
          let color;
          if (record.delsign == 0) {
            if (record.status == 1) {
              color = '#87d068';
            } else {
              color = 'red';
            }
            component.push(<Tag color={color}>服务</Tag>);
          }
          if (record.log_delsign == 0) {
            if (record.log_status == 1) {
              color = '#87d068';
            } else {
              color = 'red';
            }
            component.push(<Tag color={color}>日志</Tag>);
          }
          return component.map(item => {
            return item;
          });
        },
      },
      {
        title: '操作',
        width: 380,
        dataIndex: 'delsign',
        align: 'center',
        render: (val, record, index) => {
          return (
            <div>
              <Button type="primary" onClick={() => this.onClickEdit(record)}>
                编辑
              </Button>
              <Button
                className={styles.marginLeft}
                type="primary"
                onClick={() => this.onClickShow(record)}
              >
                详情
              </Button>
              <Switch
                className={styles.marginLeft}
                checkedChildren="服务"
                unCheckedChildren="服务"
                checked={record.delsign == 0}
                loading={this.props.isLoading}
                onChange={() => this.dispatchUpdateDelsign(record)}
              />
              <Switch
                className={styles.marginLeft}
                checkedChildren="日志"
                unCheckedChildren="日志"
                checked={record.log_delsign == 0}
                loading={this.props.isLoading}
                onChange={() => this.dispatchUpdateLogDelsign(record)}
              />
              {/* <Popconfirm
                title={`确定要${val == 0 ? '取消监控' : '监控'}当前服务吗?`}
                onConfirm={() => {
                  this.dispatchUpdateDelsign(record);
                }}
                okText="是"
                cancelText="否"
              >
                <Button className={styles.marginLeft} type="primary" danger={true}>
                  {val == 0 ? '取消监控' : '监控'}
                </Button>
                <Switch
                  disabled={true}
                  className={styles.marginLeft}
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                  checked={val == 0}
                />
              </Popconfirm> */}
            </div>
          );
        },
      },
      { title: '服务名', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '服务类型',
        dataIndex: 'type_id',
        key: 'type_id',
        align: 'center',
        render: (val, record, index) => {
          return <div>{getCateName(val, this.props.typeList)}</div>;
        },
      },
      { title: '业务类型', dataIndex: 'business_name', key: 'business_name', align: 'center' },
      { title: '产品版本', dataIndex: 'version', key: 'version', align: 'center' },
      // { title: '服务器名称', dataIndex: 'server_name', key: 'server_name', align: 'center' },
      // { title: '服务器外网IP', dataIndex: 'outer_ip', key: 'outer_ip', align: 'center' },
      // { title: '服务器内网IP', dataIndex: 'inner_ip', key: 'inner_ip', align: 'center' },
      { title: '所用端口号', dataIndex: 'port', key: 'port', align: 'center' },
      { title: '环境', dataIndex: 'env_name', key: 'env_name', align: 'center' },
      // { title: '容器名称', dataIndex: 'docker_name', key: 'docker_name', align: 'center' },
      // { title: '容器卷路径', dataIndex: 'docker_volum', key: 'docker_volum', align: 'center' },
      { title: '开发语言', dataIndex: 'language', key: 'language', align: 'center' },
      // { title: '程序路径', dataIndex: 'path', key: 'path', align: 'center' },
      // { title: '日志路径', dataIndex: 'log_path', key: 'log_path', align: 'center' },
      // { title: '检测命令', dataIndex: 'check', key: 'check', align: 'center' },
      // { title: '启动命令', dataIndex: 'start', key: 'start', align: 'center' },
      // { title: '重启命令', dataIndex: 'restart', key: 'restart', align: 'center' },
      {
        title: '自研|三方',
        dataIndex: 'self',
        key: 'self',
        align: 'center',
        render: (val, record, index) => {
          let str = '';
          if (val == 1) {
            str = '自研';
          } else {
            str = '三方';
          }
          return <div>{str}</div>;
        },
      },
      { title: '描述', dataIndex: 'description', key: 'description', align: 'center' },
      { title: '负责人', dataIndex: 'user_name', key: 'user_name', align: 'center' },
      // { title: '备注', dataIndex: 'remark', key: 'remark', align: 'center' },
      // {
      //   title: '监控',
      //   dataIndex: 'delsign',
      //   key: 'delsign',
      //   align: 'center',
      //   render: (val, record, index) => {
      //     let str = '';
      //     if (record.delsign == 0) {
      //       str = '监控中';
      //     } else {
      //       str = '失效';
      //     }
      //     return <div>{str}</div>;
      //   },
      // },
    ];
    return columnsMerchant;
  }

  // dispatchListTimer = (start) => {
  //   clearInterval(this.timer)
  //   if (start) {
  //     this.timer = setInterval(() => {
  //       this.dispatchList();
  //     }, 15 * 1000)
  //   } else {
  //     clearInterval(this.timer)    // 取消定时器
  //   }
  // };

  refreshView = () => {
    const { serverSearch, otherSearch, optionUserId, optionBusinessId, optionTypeId } = this.state;
    const req = {
      serverSearch,
      otherSearch,
      optionUserId,
      optionBusinessId,
      optionTypeId,
    };
    this.dispatchList(req);
  };

  dispatchList = req => {
    const { dispatch } = this.props;
    dispatch({
      type: 'apiServerMonitor/getServerMonitorList',
      payload: req,
    });
  };

  dispatchUpdateDelsign = record => {
    const { dispatch } = this.props;
    dispatch({
      type: 'apiServerMonitor/updateServerMonitorDelsign',
      payload: {
        id: record.id,
        delsign: record.delsign == 0 ? 1 : 0,
      },
    }).then(result => {
      this.refreshView();
    });
  };

  dispatchUpdateLogDelsign = record => {
    const { dispatch } = this.props;
    // if (record.log_delsign == 1) {
    //   if (!record.log_path) {
    //     message.error("请填写日志路径")
    //     return;
    //   }
    //   if (!record.time_id) {
    //     message.error("请选择日志时间格式")
    //     return;
    //   }
    //   if (!record.max_second) {
    //     message.error("请填写日志检测超时时间")
    //     return;
    //   }
    // }
    dispatch({
      type: 'apiServerMonitor/updateServerMonitorLogDelsign',
      payload: {
        id: record.id,
        delsign: record.log_delsign == 0 ? 1 : 0,
      },
    }).then(result => {
      this.refreshView();
    });
  };

  onChangeServer = ({ target: { value } }) => {
    this.setState({ serverSearch: value });
  };

  onChangeOther = ({ target: { value } }) => {
    this.setState({ otherSearch: value });
  };

  onClickEdit = (item: any) => {
    this.setState({ dataModelType: 1, current: item });
  };

  onClickShow = (item: any) => {
    this.setState({ showModelType: 1, current: item });
  };

  changePage = page => {
    this.setState({ selectedRowKeys: [], currentPage: page });
  };

  onChangeOptionTypeId = value => {
    this.setState({ optionTypeId: value });
  };

  onChangeOptionBusinessId = value => {
    this.setState({ optionBusinessId: value });
  };

  onChangeOptionUserId = value => {
    this.setState({ optionUserId: value });
  };

  onClickSingleEdit = type => {
    const { selectedRowKeys, currentPage } = this.state;
    const { list } = this.props;
    if (selectedRowKeys && selectedRowKeys.length > 0) {
      let ids = [];
      for (const index of selectedRowKeys) {
        let rIndex = Number((currentPage - 1) * PAGESIZE) + Number(index);
        if (rIndex < list.length) {
          ids.push(list[rIndex].id);
        }
      }
      this.setState({ singleEditIds: ids, singleDataModelType: type });
    }
  };

  onClickCreate = () => {
    this.setState({ dataModelType: 2, current: {} });
  };

  onClickServerCreate = (type) => {
    this.setState({ monitorDicModelType: type, current: {} });
  };

  onClickClose = () => {
    this.setState({
      dataModelType: -1,
      showModelType: -1,
      singleDataModelType: -1,
      monitorDicModelType: -1,
      current: null,
      singleEditIds: [],
      selectedRowKeys: [],
    });
  };
}
