export interface ISweetnessGiftItem {
    id1: number
    id2: number
    id3: number
    id4: number
    type:number
    time:number
    item_dic_id:number
    num:number
    y: number
    m: number
    cp_award_bg: string
    award_img1: string
    award_img2: string
    award_img3: string
    award_img4: string
    del_flag: number
}

export interface ISweetnessGiftSearchParams {
    sum: string,
    isUserId: string,
}

export interface ISweetnessGiftInsertParams {
    time: string
    goodsMap: {}

}

export interface ISweetnessGiftUpdateParams  {
    time: string
    del_flag:number
}

export interface ISweetnessGiftUpdateTypeParams {
    id: number,
    admin_id: number,
    type: number,
}

export interface ISweetnessGiftDelParams {
    id: number
}