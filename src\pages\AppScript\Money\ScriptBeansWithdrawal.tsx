import React, { Component } from 'react';
import { <PERSON>Complete, Button, Card, Col, DatePicker, message, Row, Space, Spin, Table } from 'antd';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { IWithdrawal, MerchantOrderBill } from '@/pages/AppScript/Money/models/withdrawal';
import { connect } from 'dva';
import { ILogin } from '@/models/login';
import { ColumnProps } from 'antd/lib/table';
import * as styles from '@/pages/AppScript/Merchant/MerchantManager.less';
import { getOptionList } from '@/utils/mallTool';
import { tabEnum } from '@/pages/AppScript/Merchant/MerchantManager';
import moment from 'moment';


const PAGESIZE = 10;

const defaultPayload = { tabKey: tabEnum.old, current: 1, pageCount: PAGESIZE, where: 'm.status = 1' };

export interface ScriptBeansWithdrawalProps {
  merchantOrderBillList: MerchantOrderBill,
  count: number,
  merchantId: number,
  money: number,
}

@connect(({ loading, login, withdrawal, scriptkill }: { loading: IdvaLoading; login: ILogin; withdrawal: IWithdrawal, scriptkill: Iscript }) => ({
  uid: login.uid,
  isLoading: loading.models['withdrawal'],
  merchantOrderBillList: withdrawal.merchantOrderBillList,
  count: withdrawal.count,
  merchantId: withdrawal.merchantId,
  merchantSimpleList: scriptkill.merchantSimpleList,
  money: withdrawal.money,
}))

export default class ScriptBeansWithdrawal extends Component<ScriptBeansWithdrawalProps, any> {

  constructor(props) {
    super(props);
    this.state = {
      currentPage: 1, where: 'm.status = 1',
      tabKey: tabEnum.old,
      currentMerchantValue: '',
      currentMerchantId: null,
      year: new Date().getUTCFullYear(),
      month: new Date().getMonth(),
      userId: 0,
    };
    this.dispatchMerchantList(defaultPayload);
    this.dispatchMerchantListCount(defaultPayload);
    this.dispatchMerchantSimpleList();
  }

  render() {

    return (
      <Spin spinning={!!this.props.isLoading}>
        <PageHeaderWrapper title={'商家剧本豆提现'} content={this.renderHeader()}>
          <Card>{this.merchantOrderBill()}</Card>
        </PageHeaderWrapper>
      </Spin>
    );
  }


  merchantOrderBill() {
    const { merchantOrderBillList, count } = this.props;
    const { currentPage, currentMerchantId } = this.state;
    // @ts-ignore
    return (
      <div>
        <Table
          title={() => '商家ID：' + currentMerchantId + '     商家账单查询'}
          columns={this.merchantOrderBillTable}
          dataSource={merchantOrderBillList}
          rowKey={(record, index) => index.toString()}
          scroll={{ x: 2000 }}
          pagination={{
            // 分页
            pageSize: PAGESIZE,
            current: currentPage,
            total: count,
            onChange: this.changePage,
          }}
        />
      </div>
    );
  }

  changePage = (page) => {
    this.setState({ currentPage: page });
  };

  //渲染头部
  renderHeader() {
    const colMd = 2;
    const colInputMd = 10;
    const { money } = this.props;
    return (
      <div>
        <Row style={{ width: 2000, marginTop: 20 }}>
          <Col md={colMd}>
            商铺名称/ID:
          </Col>
          <Col md={colInputMd} style={{ marginTop: -5, marginLeft: -80, width: 500 }}>
            <AutoComplete
              allowClear={true}
              className={styles.content}
              options={getOptionList(this.props.merchantSimpleList)}
              onSelect={this.onSelectMerchant}
              onChange={this.onChangeMerchant}
              onBlur={this.onSelectMerchantOrderBill}
              value={this.state.currentMerchantValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) =>
                option.value.indexOf(inputValue) !== -1
              }
              style={{ width: 300 }}
            />
          </Col>
        </Row>
        <Col md={colMd} style={{ marginTop: 20 }}>
          日期选择：
        </Col>
        <Col md={colInputMd} style={{ marginTop: -25, marginLeft: 103, width: 1000 }}>
          <Space direction="vertical">
            <DatePicker defaultValue={moment(new Date(), 'YYYY-MM')} onChange={this.onChangeTime} showToday={true} showTime={true}
                        picker="month" placeholder={'请选择月份'} style={{ width: 300 }}/>
          </Space>
        </Col>
        <Col md={colMd} style={{ marginTop: 20 }}>
          <label>可提现剧本豆余额：</label>
        </Col>
        <Col md={colInputMd} style={{ marginTop: -25, marginLeft: 103, width: 1500 }}>
          <label style={{ width: 150, marginLeft: 20 }}>{money}</label>
          <Button type="primary" style={{ width: 100, marginLeft: 20 }}
                  onClick={this.onSelectMerchantOrderBill}>
            查询
          </Button>
          <Button type="primary" danger={true} style={{ width: 100, marginLeft: 20 }}
                  onClick={this.onWithdrawal}>
            提现
          </Button>
        </Col>
      </div>
    );
  }

  onWithdrawal = () => {
    const { currentMerchantId, year, month } = this.state;
    const { dispatch } = this.props;
    const data = {
      merchantId: currentMerchantId,
    };
    this.props.dispatch({
      type: 'withdrawal/getWithdrawalMoney',
      payload: {
        data,
      },
    });
    // this.refreshHtml();
    setTimeout(function() {
      const data = {
        userId: 0,
        merchantId: currentMerchantId,
        type: 0,
        year,
        month,
        pageInfo: {
          lastId: 0,
          limit: 0,
        },
      };
      dispatch({
        type: 'withdrawal/getMerchantOrderBill',
        payload: {
          data,
        },
      });
    }, 1000);
  };
  // //刷新当前页面
  // refreshHtml = () => {
  //
  //
  // }

  onSelectMerchantOrderBill = () => {
    const { dispatch } = this.props;
    if (this.state.currentMerchantId != null || this.state.currentMerchantId != ''){
      const data = {
        userId: 0,
        merchantId: this.state.currentMerchantId,
        type: 0,
        year: this.state.year,
        month: this.state.month,
        pageInfo: {
          lastId: 0,
          limit: 0,
        },
      };
      //发送请求
      dispatch({
        type: 'withdrawal/getMerchantOrderBill',
        payload: {
          data,
        },
      });
    }
  };

  dispatchMerchantSimpleList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getMerchantSimpleList',
    });
  };

  onChangeTime = (date, dateString) => {
    console.log(date, dateString);
    const year: string = dateString.substring(0, 4);
    const month: string = dateString.substring(5, 7);
    this.setState({ year, month: parseInt(month) });
  };

  onChangeMerchant = (data: string) => {
    this.setState({ currentMerchantValue: data });
  };

  onSelectMerchant = (value) => {
    const { where } = this.state;

    const val = value.substring(4, value.indexOf(' 名称'));
    this.setState({ currentMerchantId: val });
    this.setState({ currentMerchantValue: value });
    this.setState({ where: '' });
    this.setState({ currentPage: 1 });
    const req = { tabKey: this.state.tabKey, current: 1, pageCount: PAGESIZE, where: '', currentMerchantId: val };
    this.dispatchMerchantList(req);
    this.dispatchMerchantListCount(req);
  };

  dispatchMerchantList = (req) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getMerchantList',
      payload: req,
    });
  };

  dispatchMerchantListCount = (req) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getMerchantListCount',
      payload: req,
    });
  };

  handleSearchList = (value: number) => {
    // @ts-ignore
    const { dispatch } = this.props;

    if (value == null || value == '') {
      message.error('请输入正确的商家名');
      return;
    }
    //发送请求
    dispatch({
      type: 'withdrawal/getMerchantOrderBill',
      payload: {
        value,
      },
    });


  };

  private merchantOrderBillTable: ColumnProps<MerchantOrderBill>[] = [
    {
      title: '订单ID',
      dataIndex: 'orderId',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '订单收入/退款',
      dataIndex: 'title',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '付款金额',
      dataIndex: 'showPrice',
      align: 'center',
      render: (val, record) => {
        if (val.substring(0, 1) == '+') {
          return <div><font color={'#00DB00'}>{val}</font></div>;
        } else if (record.status == 30) {
          return <div><font color={'#2828FF'}>{val}</font></div>;
        } else {
          // @ts-ignore
          return <div><font color={'#FF0000'}>{val}</font></div>;
        }
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      render: (val) => {
        switch (val) {
          case 1:
            return <div><font color={'#00DB00'}>已支付</font></div>;
            break;
          case 2:
            return <div>取消</div>;
            break;
          case 10:
            return <div><font color={'#FF0000'}>退款</font></div>;
            break;
          case 20:
            return <div>已到账，未提现</div>;
            break;
          case 30:
            return <div><font color={'#2828FF'}>已提现</font></div>;
            break;
        }
      },
    },
    {
      title: '抽成',
      dataIndex: 'extractNum',
      align: 'center',
    },
    {
      title: '订单创建时间',
      dataIndex: 'createTime',
      align: 'center',
      render: (val) => {
        return <div>{moment(val, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss')}</div>;
      },
    },
    {
      title: '上车用户ID',
      dataIndex: 'userId',
      align: 'center',
    },
    {
      title: '实际支付人',
      dataIndex: 'payUserId',
      align: 'center',
    },
    {
      title: '退款账单ID',
      dataIndex: 'relateBillId',
      align: 'center',
      render: (val) => {
        if (val == null) {
          return <div>暂无</div>;

        }
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      align: 'center',
      render: (val) => {
        switch (val) {
          case 1:
            return <div>充值订单</div>;
            break;
          case 2:
            return <div>支付订单</div>;
            break;
          default:
            return <div>支付订单</div>;
        }
      },
    },

  ];
}
