/*
 * @Description: 后台添加热门推荐剧本弹窗
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2021-01-05 14:27:31
 * @LastEditors: zhangyi
 * @LastEditTime: 2021-01-05 14:27:31
 */

import { Component, default as React, RefObject } from 'react';
import { IofficeNews } from '@/dto/wfOfficeNews';
import { AutoComplete, Button, Form, Input, InputNumber, message, Modal, Space, Switch } from 'antd';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import * as styles from '@/pages/AppWolf/ItemManager/CreateItem.less';
import { getOptionList } from '@/utils/mallTool';
import ScriptMomentCreateModal from './ScriptMomentCreateModal';
import { saveHtmlDraft } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons/lib';

export const PAGESIZE = 8;

@connect(({ loading, login, scriptRecommend }: { loading: IdvaLoading; login: ILogin; scriptRecommend: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptRecommend'],
  visible: scriptRecommend.visible,
  isUpdate: scriptRecommend.isUpdate,
  script: scriptRecommend.script,
  scriptSourceList: scriptRecommend.scriptSourceList,
}))

export default class ScriptRecommend extends Component<any, any> {
  private formRef: RefObject<Form> = React.createRef();
  private init;
  constructor(props) {
    super(props);
    this.state = {
      currentMerchantId: this.props.script != null ? this.props.script.scriptId : -1,
      currentMerchantValue: '',
      isEditModal: false,
      isShowModal: false,
      ossPathKey: this.props.script != null ? this.props.script.oss_path_key : '',
    };
  }

  render() {
    const { dispatch, isLoading, visible, script, isUpdate } = this.props;
    const { isEditModal, isShowModal } = this.state;
    const title = isUpdate == 1 ? '更新热门剧本' : '创建热门剧本';
    const okText = isUpdate == 1 ? '更新' : '创建';
    if (script != null) {
      script.scriptInfo = this.onChangeInfo(script.scriptId);
    }
    this.init = isUpdate == 1 ? script : {};
    const layout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
    };
    const tailLayout = {
      wrapperCol: { offset: 4, span: 16 },
    };
    return (
      <Modal
        className={styles.modalSelf}
        closable={true}
        maskClosable={false}
        confirmLoading={!!isLoading}
        centered={true}
        title={title}
        onCancel={this.onCloseModel}
        footer={[]}
        visible={visible}
        width='1000px'
      >
        <div>
          {isShowModal && <ScriptMomentCreateModal
            news={(isUpdate == 1) ? this.init : null}
            isEditeModal={isEditModal}
            loading={!!isLoading}
            dispatch={dispatch}
            onHandleCreate={this.onHandleCreate}
            onHandleEdit={this.onHandleEdit}
            onHandleClose={this.onHandleClose}
          />}
          <Form
            {...layout}
            ref={this.formRef}
            name="scriptInfoForm"
            initialValues={this.init}
            onFinish={this.onFinish}
            onFinishFailed={this.onFinishFailed}
          >
            <Form.Item
              label="剧本信息"
              name="scriptInfo"
              rules={[{ required: true, message: '选择剧本信息' }]}
            >
              <AutoComplete
                allowClear={true}
                className={styles.content}
                options={getOptionList(this.props.scriptSourceList)}
                onSelect={this.onSelectMerchant}
                onChange={this.onChangeMerchant}
                value={this.state.currentMerchantValue}
                placeholder="请输入选择"
                filterOption={(inputValue, option) =>
                  option.value.indexOf(inputValue) !== -1
                }
              />
            </Form.Item>
            <Form.Item
              label="剧本名称"
              name="name"
              rules={[{ required: true, message: '输入剧本名称' }]}
            >
              <Input/>
            </Form.Item>
            <Form.Item
              label="剧本题材"
              name="theme"
              rules={[{ required: true, message: '输入剧本题材' }]}
            >
              <Input/>
            </Form.Item>
            <Form.Item
              label="剧本标签"
              name="sign"
              rules={[{ required: false }]}
            >
              <Input placeholder={'城市限定'}/>
            </Form.Item>
            <Form.Item
              label="剧本时长"
              name="playTime"
              rules={[{ required: true, message: '输入剧本时长' }]}
            >
              <Input/>
            </Form.Item>
            <Form.Item
              label="剧本人数"
              name="num"
              rules={[{ required: true, message: '输入剧本人数' }]}
            >
              <Input/>
            </Form.Item>
            <Form.Item
              label="剧本跳转地址"
              name="url"
              rules={[{ required: true, message: '输入剧本跳转地址' }]}
            >
              <Input disabled={true}/>
            </Form.Item>

            <Form.Item
              label="剧本发行方"
              name="publisher"
              rules={[{ required: true, message: '输入剧本发行方' }]}
            >
              <Input/>
            </Form.Item>
            <Form.Item
              label="剧本发行时间"
              name="publisherTime"
              rules={[{ required: true, message: '输入剧本发行时间' }]}
            >
              <Input placeholder={'2020-01-01'}/>
            </Form.Item>
            <Form.Item
              label="剧本作者"
              name="author"
              rules={[{ required: true, message: '输入剧本作者' }]}
            >
              <Input/>
            </Form.Item>
            <Form.Item
              label="排序权重"
              name="sort"
              rules={[{ required: true, message: '输入排序权重' }]}
            >
              <InputNumber/>
            </Form.Item>
            <Form.Item
              label="展示"
              name="show"
            >
              <Switch
                checkedChildren="展示"
                unCheckedChildren="不展示"
                defaultChecked={this.init.show}
              />
            </Form.Item>
            <Form.Item {...tailLayout}>
              <Space>
                <Button type="primary" htmlType="submit">
                  {okText}
                </Button>
                {isUpdate == 0 && <Button
                  type="primary"
                  icon={<PlusOutlined/>}
                  onClick={this.onClickCreate}
                >
                  新建剧本跳转页
                </Button>}
                {isUpdate == 1 && <Button type="primary" onClick={() => this.onClickEdit()}>
                  编辑剧本跳转页
                </Button>}
              </Space>
            </Form.Item>
          </Form>
        </div>
      </Modal>
    );
  }

  //关闭模态页
  onCloseModel = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptRecommend/setVisible',
      payload: false,
    });
    dispatch({
      type: 'scriptRecommend/setScript',
      payload: null,
    });
  };

  onFinishFailed = errorInfo => {
    console.log('Failed:', errorInfo);
    if (this.props.isUpdate == 1) {
      message.error('更新失败');
    } else {
      message.error('添加失败');
    }
  };

  onFinish = (values) => {
    console.log('Success:', values);
    const { dispatch, isUpdate, script } = this.props;
    if (isUpdate === 1) {
      const req = {
        id: script.id,
        scriptId: this.state.currentMerchantId,
        name: values.name,
        theme: values.theme,
        playTime: values.playTime,
        num: values.num,
        url: values.url,
        sign: values.sign,
        publisher: values.publisher,
        publisherTime: values.publisherTime,
        author: values.author,
        sort: values.sort,
        show: values.show ? 1 : 0,
      };
      dispatch({
        type: 'scriptRecommend/updateScriptRecommend',
        payload: req,
      });
    } else {
      const req = {
        scriptId: this.state.currentMerchantId,
        name: values.name,
        theme: values.theme,
        playTime: values.playTime,
        num: values.num,
        url: values.url,
        sign: values.sign,
        publisher: values.publisher,
        publisherTime: values.publisherTime,
        author: values.author,
        sort: values.sort,
        show: values.show ? 1 : 0,
        oss_path_key: this.state.ossPathKey,
      };
      dispatch({
        type: 'scriptRecommend/insertScriptRecommend',
        payload: req,
      });
    }
    this.onCloseModel();
  };

  onSelectMerchant = (value) => {
    const val = value.substring(4, value.indexOf(' 名称'));
    this.setState({ currentMerchantId: val });
  };

  onChangeInfo = (value) => {
    const { scriptSourceList } = this.props;
    for (const script of scriptSourceList) {
      if (script.id == value) {
        return 'ID: ' + script.id + ' 名称: ' + script.name;
      }
    }
    return '';
  };

  onChangeMerchant = (data: string) => {
    this.setState({ currentMerchantValue: data });
  };

  //发送新建新闻
  onHandleCreate = (req: any) => {
    const that = this;
    that.setState({ isShowModal: false, ossPathKey: req.oss_path_key });
    this.formRef.current.setFieldsValue({
      url: req.material_url,
    });
    saveHtmlDraft('create', '');
  };

  //发送编辑新闻
  onHandleEdit = (req: IofficeNews) => {
    const that = this;
    this.init.contentBody = req.contentBody;
    that.setState({ isShowModal: false, ossPathKey: req.oss_path_key });
    this.formRef.current.setFieldsValue({
      url: req.material_url,
    });
    saveHtmlDraft(req.id, '');
  };

  //关闭模态页
  onHandleClose = () => {
    this.setState({
      isShowModal: false,
      isEditModal: false,
    });
  };

  // 点击创建
  onClickCreate = () => {
    this.setState({
      isShowModal: true,
      isEditModal: false,
    });
  };
  // 点击编辑
  onClickEdit = () => {
    this.setState({
      isShowModal: true,
      isEditModal: true,
    });
  };
}
