import { IVisibleBinderContentProps } from "@/componentsEasy/visibleBinder"
import {
    ISweetnessGiftInsertParams,
    ISweetnessGiftItem,
    ISweetnessGiftUpdateParams
} from "@/dto/sweetnessGiftDto";
import { ILogin } from "@/models/login";
import { Form, DatePicker, Modal, Select, Space, Divider, Button, AutoComplete,message } from "antd"
import { useForm } from "antd/lib/form/Form";
import { connect } from "dva";
import React, { useEffect, useState } from "react"
import AppList from '../../AppList';
import { itemDistribution, ItemList, ItemTypeList } from '../ItemManager/models/ItemDistribution';
import { getTimeYMStr } from '@/utils/momentTool';
import moment from "moment";
import { bool } from "prop-types";

export interface ISweetnessGiftModalProps {
    item: ISweetnessGiftItem,
    dispatch: any,
    items: ISweetnessGiftItem[],
    itemTypeList: ItemTypeList[];
    typeList: any,
    itemList: ItemList[];
}




const SweetnessGiftAddModal: React.FC<ISweetnessGiftModalProps & IVisibleBinderContentProps> = (props) => {

    const { visible, setVisible, item, dispatch, itemTypeList,items, itemList } = props;



    const [formRef] = useForm()

    const [cateList, setCateList] = useState([-1,-1,-1,-1,-1,-1,-1,-1]);
    const [goodsMap, setGoodsMap] = useState({});
    const [optionList1, setOptionList1] = useState([]);
    const [optionList2, setOptionList2] = useState([]);
    const [optionList3, setOptionList3] = useState([]);
    const [optionList4, setOptionList4] = useState([]);
    const [optionList5, setOptionList5] = useState([]);
    const [optionList6, setOptionList6] = useState([]);
    const [optionList7, setOptionList7] = useState([]);
    const [optionList8, setOptionList8] = useState([]);


    const [count, setCount] = useState(0);
    const [fieldKey, setFieldKey] = useState(0);
    const [remainingCount, setRemainingCount] = useState(4);

    
    useEffect(() => {

        dispatch({
            type: 'itemDistribution/getItemTypeList',
            payload: {},
        });


        dispatch({
            type: 'itemDistribution/getItemList',
            payload: {},
        });

    }, [])

    useEffect(() => {
        if (item) {

            var newGoodsMap2 = getItemInfo();

            formRef.setFieldsValue({
                //编辑弹窗数据对应
                id1: item.id1,
                id2: item.id2,
                id3: item.id3,
                id4: item.id4,
                goodsMap: newGoodsMap2,
                time: moment(item.y+"-"+item.m,'YYYY-MM').isValid ? moment(item.y+"-"+item.m,'YYYY-MM') : undefined
            })
            if(item.id1 != undefined){
                setRemainingCount(3);
            }

            if(item.id2 != undefined){
                setRemainingCount(2);
            }

            if(item.id3 != undefined){
                setRemainingCount(1);
            }

            if(item.id4 != undefined){
                setRemainingCount(0);
            }
        }


    }, [item, visible])

    function handleCancel() {
        setVisible(false)
        //清除表单内容
        formRef.resetFields()

        // const [cateList, setCateList] = useState([-1,-1,-1,-1,-1,-1,-1,-1]);
        // const [goodsMap, setGoodsMap] = useState({});
        // const [optionList1, setOptionList1] = useState([]);
        // const [optionList2, setOptionList2] = useState([]);
        // const [optionList3, setOptionList3] = useState([]);
        // const [optionList4, setOptionList4] = useState([]);
        // const [optionList5, setOptionList5] = useState([]);
        // const [optionList6, setOptionList6] = useState([]);
        // const [optionList7, setOptionList7] = useState([]);
        // const [optionList8, setOptionList8] = useState([]);
    
    
        // const [count, setCount] = useState(0);
        // const [fieldKey, setFieldKey] = useState(0);
        // const [remainingCount, setRemainingCount] = useState(4);

        setCateList([-1,-1,-1,-1,-1,-1,-1,-1]);
        setGoodsMap({});

        setOptionList1([]);
        setOptionList2([]);
        setOptionList3([]);
        setOptionList4([]);
        setOptionList5([]);
        setOptionList6([]);
        setOptionList7([]);
        setOptionList8([]);

        setCount(0);
        setFieldKey(0);
        setRemainingCount(4);

    }

    function handleOk() {

        formRef.submit()

    }



    function getOptionList(cateId) {


        const optionList: any = [];
        for (let index = itemList.length-1; index >= 0; index--) {
            const element = itemList[index];
            if (element.item_cate_id == cateId) {
                element.value = 'ID: ' + element.item_dic_id + ' 名称: ' + element.name;
                optionList.push(element);
            }
        }

        return optionList;
    };

    function getOptionListMenu() {
        var optionList: any = [];
        for (let index = 0; index < itemTypeList.length; index++) {
            var element = itemTypeList[index];
            element.value = 'ID: ' + element.item_cate_id + ' 名称: ' + element.name;
            optionList.push(element);
        }

        return optionList;

    }
    function getOptionListKey(fieldKey) {


        if(fieldKey == 1){
            return optionList1;

        }else if(fieldKey == 2){
            return optionList2;

        }else if(fieldKey == 3){
            return optionList3;

        }else if(fieldKey == 4){
            return optionList4;

        }else if(fieldKey == 5){
            return optionList5;

        }else if(fieldKey == 6){
            return optionList6;

        }else if(fieldKey == 7){
            return optionList7;

        }else if(fieldKey == 8){
            return optionList8;

        }else{
            return [];
        }

    };

    function onChangeFirstMenu(val,fieldKey) {
        // this.setState({ optionList: getOptionList(val) });

        // const cList: any = [];
        for (let index = 0; index < cateList.length; index++) {
            // const element = cateList[index];
            if(fieldKey-1 == index){
                // cList.push(val);
                cateList[index]=val;
            }else{
                // cList.push(element);
            }
        }
        // console.log('value = '+ cList);

        // setCateList(cList);

        console.log("+++===="+JSON.stringify(cateList));

        if(fieldKey == 1){
            setOptionList1(getOptionList(val));

        }else if(fieldKey == 2){
            setOptionList2(getOptionList(val));

        }else if(fieldKey == 3){
            setOptionList3(getOptionList(val));

        }else if(fieldKey == 4){
            setOptionList4(getOptionList(val));

        }else if(fieldKey == 5){
            setOptionList5(getOptionList(val));

        }else if(fieldKey == 6){
            setOptionList6(getOptionList(val));

        }else if(fieldKey == 7){
            setOptionList6(getOptionList(val));

        }else if(fieldKey == 8){
            setOptionList7(getOptionList(val));

        }

        
       

        for (let index = 0; index < itemTypeList.length; index++) {
            const element = itemTypeList[index];
            if (element.item_cate_id == val) {
                // console.log('奖励'+fieldKey+':类别：'+JSON.stringify(element));
                goodsMap['type'+fieldKey]=element;
                goodsMap['goods'+fieldKey]={};
            }
        }
        console.log('goodsMap1:'+JSON.stringify(goodsMap));


    };

    function onChangeWeight(value,fieldKey) {
        const val = value.substring(4, value.indexOf(' 名称'));
        // this.setState({ item_dic_id: val });
        // this.setState({ item_cate_id: getItemCateIdFromItemDicList(val) });
        // setItem_dic_id(val);
        // setItem_cate_id(getItemCateIdFromItemDicList(val));

        for (let index = 0; index < getOptionList(cateList[fieldKey-1]).length; index++) {
            const element = getOptionList(cateList[fieldKey-1])[index];
            const elementId = element.item_dic_id;
            console.log('elementId:'+elementId+"    val:"+val+" fieldKey:"+(fieldKey-1));

            if (elementId == val) {
                // console.log('奖励'+fieldKey+':道具：'+JSON.stringify(element));
                goodsMap['goods'+fieldKey]=element;

            }
        }
        console.log('goodsMap2:'+JSON.stringify(goodsMap));


    };

    // function getItemCateIdFromItemDicList(itemDicId) {
    //     for (let index = 0; index < itemList.length; index++) {
    //         const element = itemList[index];
    //         if (element.item_dic_id == itemDicId) {
    //             return element.item_cate_id;
    //         }
    //     }
    //     return -1;
    // };

    function getItemInfo() {
       
        //把数据复制到newGoodsMap，并把goodsMap置空
        var newGoodsMap = {};

        for (var key in goodsMap) {  
            if(goodsMap[key]["item_cate_id"] != undefined){
                newGoodsMap[key] = goodsMap[key];
                goodsMap[key] = {};
            } 
        }

        //把字典里有用的数据整理到字典里，对应的type 1 2 3 4,type、goods最多到4
        let index = 1;
        let goodsIndex = 0;
        for (var key in newGoodsMap) {  
            if(newGoodsMap[key]["item_cate_id"] != undefined){
               if(key.indexOf("type") != -1){
                    goodsMap["type"+index] = newGoodsMap[key];
                    goodsIndex = 0;
               }else{
                    goodsMap["goods"+index] = newGoodsMap[key];
                    goodsIndex=1;
            }
            if(goodsIndex == 1){
                index++;
            }

            } 
        }

        //把goodsMap里面的多余的type4 type5 goods4 goods5之类value置空
        var newGoodsMap2 = {};

        for (var key in goodsMap) {  
            if(goodsMap[key]["item_cate_id"] != undefined){
                newGoodsMap2[key] = goodsMap[key];
                goodsMap[key] = {};
            } 
        }


        //把newGoodsMap2复制到goodsMap并去除{}
        for (var key in newGoodsMap2) {  
            if(newGoodsMap2[key]["item_cate_id"] != undefined){
                goodsMap[key] = newGoodsMap2[key];
            } 
        }

        return newGoodsMap2;
    };

    function handleFinish(values) {

        var newGoodsMap2 = getItemInfo();
        var havaAdd = count < remainingCount && fieldKey < cateList.length && item == null;

        if(havaAdd && newGoodsMap2["type1"] == null){
            message.error('请添加奖励');
            return;
        }


        if(havaAdd && newGoodsMap2["type1"]){

            if(newGoodsMap2["goods1"] == null ){
                message.error('请选择奖励1类别');
                return;
            }

            if(newGoodsMap2["goods1"]["item_dic_id"] == undefined){
                message.error('请重新选择奖励1道具');
                return;
            }
        }

        if(havaAdd && newGoodsMap2["type2"]){

            if(newGoodsMap2["goods2"] == null ){
                message.error('请选择奖励2类别');
                return;
            }

            if(newGoodsMap2["goods2"]["item_dic_id"] == undefined){
                message.error('请重新选择奖励2道具');
                return;
            }
        }

        if(havaAdd && newGoodsMap2["type3"]){

            if(newGoodsMap2["goods3"] == null ){
                message.error('请选择奖励3类别');
                return;
            }

            if(newGoodsMap2["goods3"]["item_dic_id"] == undefined){
                message.error('请重新选择奖励3道具');
                return;
            }
        }

        if(havaAdd && newGoodsMap2["type4"]){


            if(newGoodsMap2["goods4"] == null ){
                message.error('请选择奖励4类别');
                return;
            }

            if(newGoodsMap2["goods4"]["item_dic_id"] == undefined){
                message.error('请重新选择奖励4道具');
                return;
            }
        }


        if (item) {
            //编辑
            const params: ISweetnessGiftUpdateParams = {
                ...values,
                id1: item.id1,
                id2: item.id2,
                id3: item.id3,
                id4: item.id4,
                time: getTimeYMStr(values.time),
                goodsMap:newGoodsMap2
            }

            dispatch({
                type: 'sweetnessGift/updateItem',
                payload: params,
            })
        } else {
            const params: ISweetnessGiftInsertParams = {
                ...values,
                time: getTimeYMStr(values.time),
                goodsMap:newGoodsMap2
            }


            var isHave = false;

            for (const item of items) {

                if(params['time'] == (item.y + "-" + item.m) || params['time'] == (item.y + "-0" + item.m)){
                    isHave = true;
                }
            }

           if(isHave){
                message.error("不可添加重复月份");
                return;
           }

            dispatch({
                type: 'sweetnessGift/insertItem',
                payload: params,
            });
        }
        handleCancel()

    }

    function render() {

        return (

            
            <Modal
                title={item ? `编辑甜蜜赠礼【ID = 
                ${(item.id1 == undefined ? "" : (item.id1 + ",")) +
                (item.id2 == undefined ? "" : (item.id2 + ",")) +
                (item.id3 == undefined ? "" : (item.id3 + ",")) +
                (item.id4 == undefined ? "" : item.id4)}】` 
                : '新增甜蜜赠礼'}
                visible={visible}
                width={800}
                onCancel={handleCancel}
                onOk={handleOk}
                okText="提交"
                cancelText="取消"
            >
               
                <Form
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 20 }}
                    form={formRef}
                    name='control-hooks'
                    // initialValues={{ sum: 888 }}
                    onFinish={handleFinish}
                    size='middle'
                >
                    <Form.Item name="time" label="时间" rules={[{ required: true }]}>
                        <DatePicker
                            style={{ width: 265 }}
                            // showTime={true}
                            format="YYYY-MM"
                            picker="month"
                        />

                    </Form.Item>
                    <div style={{ width: '100%', marginLeft: 220, marginBottom: 10 }}>
                            时间不能重复添加或者编辑
                    </div>
                    <Form.List name={["cate", "mall"]}>
                        {(fields, { add, remove }) => (
                            <>
                                {fields.map(field => (

                                    <Space direction="vertical"
                                        style={{ width: '100%', marginLeft: 125, marginBottom: 10 }}
                                        key={field.key} align="start">

                                        <Space style={{ marginLeft: 70 }}>
                                            <Divider style={{ color: 'rgba(0, 74, 80, 1)' }}>{"奖励"+(field.key+1)}</Divider>
                                        </Space>

                                        <Form.Item
                                            label="道具类别"
                                            // name='cate'
                                            rules={[{ required: true, message: '选择道具类别' }]}
                                        >
                                            {/* <Select
                                                style={{ width: 300, marginLeft: 90 }}
                                                onChange={
                                                    (val) => {
                                                        onChangeFirstMenu(val,(field.key+1));
                                                    }
                                                }
                                                showSearch={true}
                                                placeholder="请选择道具类别"
                                            >
                                                {itemTypeList.map((item, index) => (
                                                    <Select.Option key={index} value={item.item_cate_id}>
                                                        {item.item_cate_id} {item.name}
                                                    </Select.Option>
                                                ))}
                                            </Select> */}
                                            <AutoComplete
                                                // allowClear={true}
                                                style={{ width: 300, marginLeft: 90 }}
                                                options={getOptionListMenu()}
                                                onChange={

                                                    (val) => {
                                                        console.log("--------getOptionListMenu1:"+val);
                                                        const id = val.substring(4, val.indexOf(' 名称'));
                                                        onChangeFirstMenu(id,(field.key+1));
                                                    }
                                                }
                                                onClick={() => {
                                                    (val) => {
                                                        console.log("--------getOptionListMenu2:"+val);
                                                        const id = val.substring(4, val.indexOf(' 名称'));
                                                        onChangeFirstMenu(id,(field.key+1));
                                                    }
                                                }}
                                                placeholder="请选择道具"
                                                filterOption={(inputValue, option) =>
                                                    option.value.indexOf(inputValue) !== -1
                                                }
                                            />
                                        </Form.Item>

                                        <Form.Item
                                            label="道具"
                                            // name='mall'
                                            rules={[{ required: true, message: '选择道具' }]}
                                        >
                                            <AutoComplete
                                                allowClear={true}
                                                style={{ width: 300, marginLeft: 90 }}
                                                // className={styles.content}
                                                options={getOptionListKey(field.key+1)}
                                                onSelect={
                                                
                                                    (val) => {
                                                        onChangeWeight(val,field.key+1);
                                                    }
                                                }
                                                onClick={() => {

                                                    console.log("--------field.key:"+field.key+"    cateList[field.key]:"+cateList[field.key]);

                                                    if (cateList[field.key] != -1) {
                                                        //    this.setState({ optionList: this.getOptionList(data['cate']) });


                                                        if(field.key == 0){
                                                            setOptionList1(getOptionList(cateList[field.key]));

                                                        }else if(field.key == 1){
                                                            setOptionList2(getOptionList(cateList[field.key]));

                                                        }else if(field.key == 2){
                                                            setOptionList3(getOptionList(cateList[field.key]));

                                                        }else if(field.key == 3){
                                                            setOptionList4(getOptionList(cateList[field.key]));
                                                        }else if(field.key == 4){
                                                            setOptionList5(getOptionList(cateList[field.key]));
                                                        }else if(field.key == 5){
                                                            setOptionList6(getOptionList(cateList[field.key]));
                                                        }else if(field.key == 6){
                                                            setOptionList7(getOptionList(cateList[field.key]));
                                                        }else if(field.key == 7){
                                                            setOptionList8(getOptionList(cateList[field.key]));
                                                        }

                                                    }
                                                }}
                                                placeholder="请选择道具"
                                                filterOption={(inputValue, option) =>
                                                    option.value.indexOf(inputValue) !== -1
                                                }
                                            />
                                        </Form.Item>

                                        <Button danger onClick={
                                            () => {
                                               
                                                goodsMap['type'+(field.key+1)]={};
                                                goodsMap['goods'+(field.key+1)]={};

                                                remove(field.name);
                                                setCount(count-1);

                                                console.log('goodsMap:'+JSON.stringify(goodsMap)+" field.key:"+field.key);
                                            }
                                        }
                                            style={{ width: 100, marginLeft: 90, marginBottom: 10, marginTop: 10 }}
                                            block>
                                            删除奖励
                                        </Button>

                                    </Space>
                                ))}
                                {count < remainingCount && fieldKey < cateList.length && 
                                <Form.Item style={{ width: 360, marginLeft: 215 }}>
                                    <Button type="default" onClick={
                                        () => {
                                            add();
                                            setCount(count+1);
                                            setFieldKey(fieldKey+1);

                                            console.log('fieldKey:'+fieldKey);

                                        }
                                        } block>
                                        添加奖励
                                    </Button>
                                </Form.Item>}
                                {fieldKey >= cateList.length && 
                                    <div  style={{ width: 360, marginLeft: 215 }}>
                                        删除次数太多,请刷新重试后继续添加
                                    </div>
                                }
                            </>
                        )}
                    </Form.List>
                    <div style={{ width: '100%', marginLeft: 210, marginBottom: 10 }}>
                        {"奖励添加完毕点击提交一起上传"}
                    </div>

                </Form>


            </Modal>
        )
    }

    return render()
}

const mapStateToProps =
    ({
        loading,
        login,
        sweetnessGift,
        bannerStore,

        broadcastSend,
        itemDistribution,

    }: {
        loading: IdvaLoading;
        login: ILogin;
        sweetnessGift: any;


        broadcastSend: any;
        bannerStore: any;
        global: any
        itemDistribution: itemDistribution;

    }) => ({
        isLoading: loading.models.sweetnessGift,
        uid: login.uid,

        // isLoading: loading.models.broadcastSend,
        broadcastList: broadcastSend.broadcastList,
        itemTypeList: itemDistribution.itemTypeList,
        items: sweetnessGift.items,
        typeList: bannerStore.typeList,
        itemList: itemDistribution.itemList,

    });



export default connect(mapStateToProps)(SweetnessGiftAddModal);

