/*
 * @Description: 称号管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-03 14:27:31
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  AutoComplete,
  Row,
  Col,
  Radio,
  Image,
  Table,
  Tag,
  Typography,
  message,
} from 'antd';
const { Text } = Typography;
import { PlusOutlined, CloudUploadOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import * as styles from './ScriptManager.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import AppList from '../../../pages/AppList';
import { ColumnProps } from 'antd/lib/table';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName, getOptionList } from '@/utils/mallTool';
import { checkNotNull } from '@/utils/emptyTool';
import MerchantScriptTable from '../Merchant/MerchantScriptTable';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
export const PAGESIZE = 8;

const defaultPayload = { current: 1, pageCount: PAGESIZE, where: "m.status = 1" };

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  merchantSimpleList: scriptkill.merchantSimpleList,
  scriptSimpleList: scriptkill.scriptSimpleList,
  currentMerchantScriptList: scriptkill.currentMerchantScriptList,
  merchantDicList: scriptkill.merchantDicList,
}))

export default class ScriptManager extends Component<any, any> {
  constructor(props) {
    super(props);

    this.state = {
      currentMerchantId: -1,
      currentScriptId: -1,
      currentMerchantValue: "",
      currentScriptValue: "",
    }

    this.props.dispatch({
      type: 'scriptkill/setScriptList',
      payload: []
    });
    this.props.dispatch({
      type: 'scriptkill/getMerchantDicList',
      payload: {type: 3}
    });
    this.dispatchMerchantSimpleList();
    this.dispatchScriptSimpleList();
  }

  render() {
    const { isLoading } = this.props;
    return (
      <Spin spinning={!!isLoading}>
        <PageHeaderWrapper title="检索条件" content={this.renderHeader()}>
          <MerchantScriptTable
            scriptList={this.props.currentMerchantScriptList}
            action={true}
            type={this.state.currentType}
            currentMerchantId={this.state.currentMerchantId}
            currentScriptId={this.state.currentScriptId}
            id={this.state.currentScriptId}
            where={this.state.where}
          />
        </PageHeaderWrapper>
      </Spin>
    );
  }

  renderHeader() {
    const { globalPlayerId } = this.props;
    const { user_noSearch, old_usernameSearch, new_usernameSearch, statusSearch, createtimeSearch, updatetimeSearch } = this.state;
    const colMd = 2;
    const colInputMd = 6;
    return (
      <>
        <Row style={{ width: 2000 }}>
          <Col md={colMd}>
            商铺名称/ID:
          </Col>
          <Col md={colInputMd} style={{ marginTop: -5, marginLeft: -40 }}>
            <AutoComplete
              allowClear={true}
              className={styles.content}
              options={getOptionList(this.props.merchantSimpleList)}
              onSelect={this.onSelectMerchant}
              onChange={this.onChangeMerchant}
              value={this.state.currentMerchantValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) =>
                option.value.indexOf(inputValue) !== -1
              }
            />
          </Col>
        </Row>
        <Row style={{ width: 2000, marginTop: 10 }}>
          <Col md={colMd}>
            剧本名称/ID:
          </Col>
          <Col md={colInputMd} style={{ marginTop: -5, marginLeft: -40 }}>
            <AutoComplete
              allowClear={true}
              className={styles.content}
              options={getOptionList(this.props.scriptSimpleList)}
              onSelect={this.onSelectScrpit}
              onChange={this.onChangeScrpit}
              value={this.state.currentScriptValue}
              placeholder="请输入选择"
              filterOption={(inputValue, option) =>
                option.value.indexOf(inputValue) !== -1
              }
            />
          </Col>
        </Row>
        <Row style={{ width: 1400, marginTop: 10}}>
          <Col md={colMd}>
            状态:
        </Col>
          <Col md={colInputMd} style={{ marginBottom: 12, marginLeft: 20 }}>
            <Radio.Group onChange={this.onChangeRadio} value={this.state.where}>
              {this.props.statusList.map((item, index) => {
                return (
                  <Radio value={item.whereScript}>
                    {item.name}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Col>
          <Col md={2} style={{ marginTop: -5 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={this.props.isSearching}
              onClick={this.handleSearch}
            >
              查询
          </Button>
          </Col>
        </Row>
      </>
    );
  }

  onChangeRadio = e => {
    this.setState({ where: e.target.value });
  };

  onChangeScrpit = (data: string) => {
    this.setState({ currentScriptValue: data });
  };

  onChangeMerchant = (data: string) => {
    this.setState({ currentMerchantValue: data });
  };

  getStatusId(where: any) {
    for (const item of this.props.statusList) {
      if (item.whereScript == where) {
        return item.id;
      }
    }
    return 1;
  }

  handleSearch = () => {
    const { dispatch } = this.props;
    if (!checkNotNull(this.state.where)) {
      message.error("请先选择状态");
      return ;
    }
    const req = { where: this.state.where };
    this.setState({ currentMerchantValue: "" });
    this.setState({ currentScriptValue: "" });
    this.setState({ currentType: this.getStatusId(this.state.where) })
    dispatch({
      type: 'scriptkill/getScriptList',
      payload: req
    }).then(() => {
    });
  };

  onSelectMerchant = (value) => {
    const { dispatch } = this.props;

    const val = value.substring(4, value.indexOf(" 名称"));
    this.setState({ currentMerchantId: val });
    this.setState({ currentScriptId: -1 });
    this.setState({ currentMerchantValue: value });
    this.setState({ currentScriptValue: "" });
    this.setState({ where: "" });
    this.setState({ currentType: 2 });
    dispatch({
      type: 'scriptkill/getScriptList',
      payload: { merchant_id: val }
    }).then(() => {
    });
  };

  onSelectScrpit = (value, option) => {
    console.log("optionoption", option);
    
    const { dispatch } = this.props;

    const val = value.substring(4, value.indexOf(" 名称"));
    this.setState({ currentScriptId: val });
    this.setState({ currentMerchantId: -1 });
    this.setState({ currentScriptValue: value });
    this.setState({ currentMerchantValue: "" });
    this.setState({ where: "" });
    this.setState({ currentType: 2 });
    dispatch({
      type: 'scriptkill/getScriptList',
      payload: { id: val }
    }).then(() => {
    });
  };

  dispatchMerchantSimpleList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getMerchantSimpleList',
    });
  };

  dispatchScriptSimpleList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getScriptSimpleList',
    });
  };

}
