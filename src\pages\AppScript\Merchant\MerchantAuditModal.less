@import '~antd/lib/style/themes/default.less';
@import '~@/utils/utils.less';

.searchError {
  color: @alert-error-icon-color;
  margin-top: 10px;
  margin-bottom: 10px;
}

.setOn {
  color: @primary-color;
}

.setOff {
  color: @alert-error-icon-color;
}

.customModalBody :global(.ant-modal-header) {
  padding: 10px;
}

.modalForm {
  display: flex;
  flex-direction: column;

  .formItem {
    display: flex;
    flex-direction: row;
    margin-top: 4px;
    justify-content: center;
    align-self: flex-start;

    .title {
      margin-top: 3px;
      width: 120px;
      font-weight: bold;
    }

    .content {
      width: 360px;
      margin-left: 15px;
    }

    .toggle {
      margin-left: 15px;
    }
  }
}

.step_div {
  margin: 20px auto;
  text-align: right;

  .submit_btn {
    margin-top: 20px;
    margin-right: 10px;
  }

  img {
    object-fit: cover !important;
  }
}

.step_div_center {
  text-align: center;
  margin-bottom: 10px;
}

.step_img_div {
  margin: 20px auto;
  text-align: center;

  .text_div {
    height: 300px;
  }

  .button_div {
    text-align: right;
  }

  .submit_btn {
    margin-top: 20px;
    margin-right: 10px;
  }
}

.selectImg {
  width: 100px;
}

.tableInc {
  color: @primary-color;
}

.marginLeft {
  margin-right: 10px;
}

.tableDec {
  color: magenta;
}
