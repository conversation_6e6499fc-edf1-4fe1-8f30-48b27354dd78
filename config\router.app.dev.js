/*
 * @Description: 开发者
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-08-22 15:26:08
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-10-18 15:02:43
 */

import { AccessRouteId } from './accessRouteCof';

const AppDevRoutes = {
  path: 'appDev',
  name: 'appDev',
  icon: 'http://coder.53site.com/WerewolfJP/Res/code-fork.png',
  Routes: ['src/layouts/Authorized'],
  authority: AccessRouteId.app_dev,
  routes: [
    // 接口文档
    {
      path: 'apiDocs',
      icon: 'api',
      name: 'apiDocs',
      authority: AccessRouteId.dev_swagger,
      component: './AppliDev/ApiDocs',
    },
    {
      path: 'serverMonitor',
      icon: 'cloud-server',
      name: 'serverMonitor',
      authority: AccessRouteId.dev_server_monitor,
      component: './AppliDev/ServerMonitor',
    },
    {
      path: 'serverMonitorDict',
      icon: 'gold',
      name: 'serverMonitorDict',
      authority: AccessRouteId.dev_server_monitor,
      component: './AppliDev/ServerMonitorDictManager',
    },
    
  ],
};

export default AppDevRoutes;
