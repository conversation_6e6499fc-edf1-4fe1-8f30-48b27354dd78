/*
 * @Description: 2w框订单管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-08-31 14:57:13
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-10-11 16:33:40
 */

export enum Coin2wStatus {
    COIN_2W_CREATED = 0,
    COIN_2W_PREPARE = 1,//资源准备中
    COIN_2W_DONE = 2,//资源已发放
    COIN_2W_NULL = 3,//无定制
}

export interface Icoin2wItem {
   id: number;
   user_id: number;
   to_user_id: number;
   create_time: string;
   status: Coin2wStatus;
   avatar_frame_period_id: string;
   select_avatar_frame_id: number;
   get_avatar_frame_id: number;
   note_command: string;
   trade_no: string;
   note_id: number;
   achievement_id: number;
   phone: string;
   nickname: string;
   period_name: string;
   author: string;
   send_time: string;
   uid: string;

}

export interface Icoin2wListReq{
    status: number;
}

export interface Icoin2wUpdateInfoReq{
    status: Coin2wStatus;
    id: number;
    to_user_id: number;
    avatar_frame_period_id: number;
    note_command: string;
    period_name?: string;
    uid?: string;
}

export interface Icoin2wUploadNoteSuccReq{
    id: number;
    note_id: number;
}