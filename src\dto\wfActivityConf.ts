/*
 * @Description: 天狼-运营活动配置中心
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-30 14:32:31
 * @LastEditors: hammercui
 * @LastEditTime: 2020-09-29 16:21:30
 */

export interface IatyInfo {
    id: number;
    name: number;
    start_time: string;
    end_time: string;
    end_data_time: string
    sql_admin: string
}

//请求更新auto配置
export interface IsetAutoConfReq {
    uid: number;
    activityId: number;
    env: string;
    key: string;
    value: IautoConfItem;
}

//请求更新奖励配置
export interface IsetAwardConfReq {
    uid: number;
    activityId: number;
    env: string;
    awardId: number;
    value: IawardConfItem;
}

export interface IgetAllConfReq {
    activityId: number;
    env: string
}

// 奖池中奖励
export interface IpoolAwardItem {
    props_id: number;
    props_name: string;
    props_type: number;
    type_name?: string;
}

export interface IautoConfItem {
    key?:string;
    desc: string;
    val: string;
}

export interface IawardConfItem {
    id: number;//奖励id
    propsType: number;
    targetNum?: number;//奖励条件
    weight?: number; //权重
    props: IawardItem[]; //奖励列表
}

export interface IawardItem {
    propsId: number;
    propsType: number;
    num: number;
}
