@import '~antd/lib/style/themes/default.less';
@import '~@/utils/utils.less';

.searchError {
  color: @alert-error-icon-color;
  margin-top: 10px;
  margin-bottom: 10px;
}

.setOn {
  color: @primary-color;
}
.setOff {
  color: @alert-error-icon-color;
}

.modalForm {
  display: flex;
  flex-direction: column;
  .formItem {
    display: flex;
    flex-direction: row;
    margin-top: 30px;
    justify-content: center;
    align-self: flex-start;
    .title {
      width: 120px;
      font-weight: bold;
    }
    .content {
      width: 320px;
      margin-left: 15px;
    }
    .toggle {
      margin-left: 15px;
    }
  }
}

.tableInc {
  color: @primary-color;
}
.tableDec {
  color: magenta;
}

.show_img_div {
  width: 350px;
  height: 160px;
  text-align: center;
}

.show_upload {
  margin-left: 5px;
  display: inline-block;
}

.show_upload_done {
  margin-left: 5px;
  width: 102px;
  height: 102px;
  display: inline-block;
}

.text_div_done {
  width: 350px;
  height: 20px;
  text-align: center;
  font-size: 12px;
  color: black;
}

.submit_btn {
  text-align: center;
}

.text_div {
  width: 350px;
  height: 20px;
  text-align: center;
  font-size: 12px;
  color: red;
}
