import { Modal, Spin, Comment, Tooltip, Avatar, List, But<PERSON>, Popconfirm } from 'antd';

import React, { Component } from 'react';
import moment from 'moment';
import { getTimeMoment, getTimeStr, getDayStr } from '@/utils/momentTool';
import * as styles from './MomentCommentModal.less';
import { connect } from "dva";
import map from '@/components/Login/map';
import { urlToList } from '@/components/_utils/pathTools';
const USERID = 100;
@connect(({ loading, login, merchantMoment }: { loading: IdvaLoading; login: ILogin; merchantMoment: any }) => ({
  uid: login.uid,
  isLoading: loading.models['merchantMoment'],
  momentList: merchantMoment.momentList,
  momentListCount: merchantMoment.momentListCount,
  commentData: merchantMoment.commentData,
}))

class MomentCommentModal extends Component<any, any> {

  constructor(props) {
    super(props);
    // this.state = {
    // }

    // eslint-disable-next-line react/destructuring-assignment
    this.getCommentList(this.makeMomentReq(this.props.current));
  }

  // eslint-disable-next-line react/sort-comp
  render() {
    const { onHandleClose, current, isLoading, commentData } = this.props;
    const title = `剧本圈【${current.title}】的评论`;
    const hasComment = commentData && commentData.commentList && commentData.commentList.length > 0;
    return (
      // eslint-disable-next-line react/jsx-filename-extension
      <Modal
        visible={true}
        centered={true}
        title={title}
        onCancel={onHandleClose}
        okText="关闭"
        width={1000}
        footer={[]}
      >
        <Spin spinning={isLoading}>
          <div className={styles.createModal}>
            <div className={styles.news_form} />
            {
              !hasComment &&
              <div className={styles.noneDivContent}>还没有评论</div>
            }
            {
              hasComment && this.renderComment()
            }
          </div>
        </Spin>
      </Modal>
    );
  }

  renderComment = () => {
    const { onHandleClose, current, isLoading, commentData } = this.props;
    const commentList = commentData.commentList;
    const pageInfo = commentData.pageInfo;
    return (
      <>
        <List
          className="comment-list"
          itemLayout="horizontal"
          dataSource={commentList}
          renderItem={item => (
            <li>
              <Comment
                actions={this.renderDeleButton(item)}
                author={<a>{item.commentUser.name}</a>}
                avatar={
                  <Avatar
                    src={this.getUserUrl(item.commentUser.headicon)}
                    alt={item.commentUser.name}
                  />
                }
                content={
                  <p>
                    {item.content}
                  </p>
                }
                datetime={
                  <Tooltip title={getTimeStr(item.commentTime)}>
                    <span>{getTimeStr(item.commentTime)}</span>
                  </Tooltip>
                }
              >
                {this.renderSubComment(item)}
              </Comment>
            </li>
          )}
        />
        {this.renderPage(pageInfo)}
      </>
    );
  };

  renderSubComment = (item) => {
    const subCommentList = item.subCommentList
    const pageInfo = item.pageInfo;
    if (!subCommentList || subCommentList.length <= 0) {
      return <></>;
    }
    return (
      <>
        <List
          className="comment-list"
          itemLayout="horizontal"
          dataSource={subCommentList}
          renderItem={item => (
            <li>
              <Comment
                actions={this.renderDeleButton(item)}
                author={<a>{item.commentUser.name}</a>}
                avatar={
                  <Avatar
                    src={this.getUserUrl(item.commentUser.headicon)}
                    alt={item.commentUser.name}
                  />
                }
                content={
                  <p>
                    {this.getSubContent(item)}
                  </p>
                }
                datetime={
                  <Tooltip title={getTimeStr(item.commentTime)}>
                    <span>{getTimeStr(item.commentTime)}</span>
                  </Tooltip>
                }
              />
            </li>
          )}
        />
        {this.renderSubPage(item)}
      </>
    )
  }

  renderDeleButton = (item) => {
    return [
      <span>
        <span className="comment-action">操作</span>
      </span>,
      <Popconfirm
        title="确定要此条评论吗?"
        onConfirm={() => {
          const req = {
            commentId: item.commentId,
            userId: USERID
          }
          // eslint-disable-next-line react/destructuring-assignment
          this.props.dispatch({
            type: 'merchantMoment/deleteComment',
            payload: {
              data: req,
            }
          });
        }}
        okText="Yes"
        cancelText="No"
      >
        <Button type="link" danger={true}>
          删除
        </Button>
      </Popconfirm>
    ]
  }

  renderPage = (pageInfo) => {
    if (pageInfo.hasNext == false) {
      return <></>;
    }
    return (
      <>
        <Button
          type="link"
          size="small"
          // eslint-disable-next-line react/destructuring-assignment
          loading={!!this.props.isLoading}
          onClick={() => { this.onClickPage(pageInfo) }}
        >
          显示更多
        </Button>
      </>
    )
  }

  renderSubPage = (item) => {
    const pageInfo = item.pageInfo;
    if (pageInfo.hasNext == false) {
      return <></>;
    }
    return (
      <>
        <Button
          type="link"
          size="small"
          loading={!!this.props.isLoading}
          onClick={() => { this.onClickSubPage(item) }}
        >
          显示更多
        </Button>
      </>
    )
  }


  onClickPage = (pageInfo) => {
    const req = {
      commentParentId: 0,
      // eslint-disable-next-line react/destructuring-assignment
      momentId: this.props.current.id,
      pageInfo,
      userId: USERID
    }
    const { dispatch } = this.props;
    console.log("item", req);
    dispatch({
      type: 'merchantMoment/getCommentListPage',
      payload: {
        data: req
      }
    });
  }


  onClickSubPage = (item) => {
    const req = {
      commentParentId: item.commentId,
      // eslint-disable-next-line react/destructuring-assignment
      momentId: this.props.current.id,
      pageInfo: { ...item.pageInfo },
      userId: USERID
    }
    const { dispatch } = this.props;
    console.log("item", req);
    dispatch({
      type: 'merchantMoment/getCommentListSubPage',
      payload: {
        data: req
      }
    });
  }

  getUserUrl = (str) => {
    if (str.includes('http://')) {
      return str;
    }
    if (str.includes('https://')) {
      return str.replace('https', 'http');
    }
    if (str.includes("role_")) {
      const url = `http://img.53site.com/ScriptKill/Role/${str}`;
      if (url.includes(".png")) {
        return url;
      }
      return `${url}.png`;

    }
    const url = `http://img.53site.com//ScriptKill/Avatar/${str}`;
    if (url.includes(".png")) {
      return url;
    }
    return `${url}.png`;
  }

  getSubContent = (item) => {
    if (item.commentStructureType == 3) {
      return `回复${item.commentReplyUser.name} : ${item.content}`;
    }
    return item.content;
  }

  // eslint-disable-next-line arrow-body-style
  makeMomentReq = (item) => {
    return {
      commentParentId: 0,
      momentId: item.id,
      pageInfo: {
        lastIdStr: 0,
      },
      userId: USERID
    }
  }

  getCommentList = (req) => {

    const { dispatch } = this.props;
    console.log("item", req);
    dispatch({
      type: 'merchantMoment/getCommentList',
      payload: {
        data: req
      }
    });
  }
}

export default MomentCommentModal;
