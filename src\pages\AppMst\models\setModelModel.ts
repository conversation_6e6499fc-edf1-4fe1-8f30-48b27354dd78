import { ISetModelSearchItem,ISetModelMenuItem,ISetModelInfoItem } from "../dto/setModelDataDto"
import { searchModelInfo,searchFromModelList,addPic,delPic,modelTypeList,classificationTypeList,classificationTypeMyList,addClassificationType,delClassificationType,baseModelList,saveModel } from "../services/apiSetModel";

export interface ISetModelModel {
    searchModelInfo: ISetModelInfoItem[],
    searchFromModelList: ISetModelSearchItem[],
    modelTypeList: any[],
    classificationTypeList: any[],
    classificationTypeMyList: any[],
    baseModelList: any[],
}
const initialState: ISetModelModel = {
    searchModelInfo: [],
    searchFromModelList: [],
    modelTypeList: [],
    classificationTypeList: [],
    classificationTypeMyList: [],
    baseModelList: [],
}
export default {
    namespace: 'setModelModel',
    state: initialState,
    effects: {

        *searchModelInfo({ payload }, { call, put }) {
            var response = yield call(searchModelInfo, payload);
            yield put({ type: 'setSearchModelInfo', payload: response });
        },

        *searchFromModelList({ payload }, { call, put }) {
            var response = yield call(searchFromModelList, payload);
            yield put({ type: 'setSearchFromModelList', payload: response });
        },
        
        *delPic({ payload }, { call, put }) {
            var response = yield call(delPic, payload);
            yield put({ type: 'setDelPic'});

        },

        *addPic({ payload }, { call, put }) {
            var response = yield call(addPic, payload);
            yield put({ type: 'setAddPic' });
        },

        *modelTypeList({ payload }, { call, put }) {
            var response = yield call(modelTypeList, payload);
            yield put({ type: 'setModelTypeList', payload: response });
        },

        *classificationTypeList({ payload }, { call, put }) {
            var response = yield call(classificationTypeList, payload);
            yield put({ type: 'setClassificationTypeList', payload: response });
        },
        *classificationTypeMyList({ payload }, { call, put }) {
            var response = yield call(classificationTypeMyList, payload);
            yield put({ type: 'setClassificationTypeMyList', payload: response });
        },
        *addClassificationType({ payload }, { call, put }) {
            var response = yield call(addClassificationType, payload);
            yield put({ type: 'setAddClassificationType', payload: response });

            var response2 = yield call(classificationTypeList, {model_id:payload.model_id,model_cate_id:payload.model_cate_id,type:1});
            yield put({ type: 'setClassificationTypeList', payload: response2 });

            // var response3 = yield call(classificationTypeMyList, {model_id:payload.model_id,model_cate_id:payload.model_cate_id,type:2});
            // yield put({ type: 'setClassificationTypeMyList', payload: response3 });
        },
        *delClassificationType({ payload }, { call, put }) {
            var response = yield call(delClassificationType, payload);
            yield put({ type: 'setDelClassificationType', payload: response });

            var response2 = yield call(classificationTypeList, {model_id:payload.model_id,model_cate_id:payload.model_cate_id,type:1});
            yield put({ type: 'setClassificationTypeList', payload: response2 });

            var response3 = yield call(classificationTypeMyList, {model_id:payload.model_id,model_cate_id:payload.model_cate_id,type:2});
            yield put({ type: 'setClassificationTypeMyList', payload: response3 });

        },
        *baseModelList({ payload }, { call, put }) {
            var response = yield call(baseModelList, payload);
            yield put({ type: 'setBaseModelList', payload: response });
        },

        *saveModel({ payload }, { call, put }) {
            var response = yield call(saveModel, payload);
            yield put({ type: 'setSaveModel' });

            var response2 = yield call(searchModelInfo, payload);
            yield put({ type: 'setSearchModelInfo', payload: response2 });
        },
        
    },
    reducers: {

        setSearchModelInfo(state: ISetModelInfoItem, { payload }: { payload: ISetModelInfoItem[] }): any {
            return { ...state, searchModelInfo: payload }
        },

        setSearchFromModelList(state: ISetModelSearchItem, { payload }: { payload: ISetModelSearchItem[] }): any {

            for (const item of payload) {
                item.status = 'done';
            }

            return { ...state, searchFromModelList: payload }
        },
        setDelPic(state: ISetModelSearchItem) {
            return { ...state }
        },

        setAddPic(state: ISetModelSearchItem) {
            return { ...state }
        },

        setModelTypeList(state: ISetModelMenuItem, { payload }: { payload: ISetModelMenuItem[] }): any {
            return { ...state, modelTypeList: payload }
        },

        setClassificationTypeList(state: ISetModelMenuItem, { payload }: { payload: ISetModelMenuItem[] }): any {
            return { ...state, classificationTypeList: payload }
        },
        setClassificationTypeMyList(state: ISetModelMenuItem, { payload }: { payload: ISetModelMenuItem[] }): any {
            return { ...state, classificationTypeMyList: payload }
        },
        setAddClassificationType(state: any) {
            return { ...state }
        },
        setDelClassificationType(state: any) {
            return { ...state }
        },
        setBaseModelList(state: ISetModelMenuItem, { payload }: { payload: ISetModelMenuItem[] }): any {
            return { ...state, baseModelList: payload }
        },

        setSaveModel(state: ISetModelSearchItem) {
            return { ...state }
        },

    }
}