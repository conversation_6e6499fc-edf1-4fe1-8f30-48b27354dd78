@import '~antd/lib/style/themes/default.less';
@import '~@/utils/utils.less';

.searchError {
  color: @alert-error-icon-color;
  margin-top: 10px;
  margin-bottom: 10px;
}

.marginLeft {
  margin-top: -10px;
  margin-bottom: 10px;
}

.marginCenter {
  text-align: center;
  margin-left: 840px;
}

.marginButton {
  margin-left: 10px;
  margin-right: 10px;
}

.setOn {
  color: @primary-color;
  // margin-top: 10px;
  // margin-bottom: 10px;
}
.setOff {
  color: @alert-error-icon-color;
  // margin-top: 10px;
  // margin-bottom: 10px;
}

.avframeheader {
  display: flex;
  flex-direction: column;
  .avswitch {
    margin-top: 12px;
    .dync {
      margin-right: 20px;
    }
    .complete {
      margin-right: 10px;
    }
  }
}
.content {
  width: 500px;
  margin-left: 15px;
}
.row {
  margin: 10px;
}
.page {
  text-align: right;
  margin-top: 40px;
  margin-bottom: 20px;
  margin-right: 10px;
}
.noData {
  margin-bottom: 20px;
}
.col {
  margin-bottom: 15px;
  height: 200px;
}
.avatarImg {
  width: 85.5px;
  height: 85.5px;
}
.boxImgPop {
  margin: 0 auto;
  height: 600px;
}

.boxImg {
  width: 70px;
  height: 70px;
}

.searchModal {
  margin: 5px;
  .table {
    margin-top: 10px;
  }
}

.divScroll {
  overflow-x: scroll;
  overflow-y: hidden;
}

.tableCardItem {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 3000px;
  .itemSmall {
    width: 60px;
  }
  .itemBig {
    width: 180px;
  }
  .itemTitle {
    // flex:1;
    width: 3000px;
  }
  .itemOne {
    // flex:1;
    width: 120px;
  }
  .itemStateOn {
    color: green;
    width: 120px;
  }
  .itemStateOff {
    color: red;
    width: 120px;
  }
  .actions {
    display: flex;
    flex-direction: column;
    .actionEdit {
      margin-top: 10px;
    }
  }
}

.tableInc {
  color: @primary-color;
}
.tableDec {
  color: magenta;
}
