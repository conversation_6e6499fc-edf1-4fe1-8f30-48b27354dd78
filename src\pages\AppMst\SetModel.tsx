import React, { useEffect, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Modal, Upload, Space, Divider, Button, Input, Select, Form, Tag, notification, Image, Result } from 'antd';
import { RcFile, UploadProps } from 'antd/es/upload';
import { UploadFile } from 'antd/es/upload/interface';
import PageHeaderWrapper from "@/components/PageHeaderWrapper"
import { useForm } from "antd/lib/form/Form";
import { ISetModelInfoItem, ISetModelSearchItem } from "./dto/setModelDataDto"
import { connect } from "dva";
import { ILogin } from "@/models/login";
import { ISetModelModel } from "./models/setModelModel"

const Option = Select.Option;

const getBase64 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = (error) => reject(error);
    });

interface ISetModelProps {
    dispatch: any,
    searchModelInfo: ISetModelInfoItem[],
    searchFromModelList: ISetModelSearchItem[],
    modelTypeList: any[],
    classificationTypeList: any[],
    classificationTypeMyList: any[],
    baseModelList: any[],
}

const SetModel: React.FC<ISetModelProps> = (props) => {


    const { dispatch, searchModelInfo, searchFromModelList, modelTypeList, classificationTypeList, classificationTypeMyList, baseModelList } = props;
    const [form] = useForm();

    const [previewUrl, setPreviewUrl] = useState('');
    const [searchString, setSearchString] = useState('');
    const [clickSearch, setClickSearch] = useState(false);
    const [tags, setTags] = useState([]);

    const [visible, setVisible] = useState(true);

    const name = 'admin_' + Date.now() + Math.ceil(Math.random() * 10);
    var now = new Date();
    var year = now.getFullYear();
    let month = now.getMonth() + 1 < 10 ? '0' + (now.getMonth() + 1) : now.getMonth() + 1;
    let day = now.getDate() < 10 ? '0' + (now.getDate()) : now.getDate();

    const [postData, setPostData] = useState({ bucket: 'easyai-resource', ossPath: 'image/' + year + '-' + month + '-' + day + '/', name });

    const [showImages, setShowImages] = useState([]);


    useEffect(() => {

        var fileList = [];

        for (const item of searchFromModelList) {
            const file = { uid: '', status: '', url: '' };
            file.uid = item.uid;
            file.status = item.status;
            file.url = item.url;

            fileList.push(file);
        }
        setShowImages(fileList);

        // if (searchModelInfo) {
        //     form.setFieldsValue({
        //         model_type_id: searchModelInfo.length>0?searchModelInfo[0].model_type_id:0,
        //         model_type_name: searchModelInfo.length>0?searchModelInfo[0].model_type_name:'',
        //         model_base_id: searchModelInfo.length>0?searchModelInfo[0].model_base_id:0,
        //         model_base_name: searchModelInfo.length>0?searchModelInfo[0].model_base_name:'',
        //         is_public: searchModelInfo.length>0?searchModelInfo[0].is_public:0,
        //         is_recommend: searchModelInfo.length>0?searchModelInfo[0].is_recommend:0,
        //     })
        // }


        if (modelTypeList.length <= 0) {
            dispatch({
                type: 'setModelModel/modelTypeList',
            });
        }

        if (baseModelList.length <= 0) {
            dispatch({
                type: 'setModelModel/baseModelList',
            });
        }

        setTags([]);


    }, [searchFromModelList, classificationTypeMyList])

    const handlePreview = async (file: UploadFile) => {

        if(file.url){
            setPreviewUrl(file.url);
        }else{
            setPreviewUrl(file.thumbUrl);
        }
        setVisible(true);

    };

    const handleChange: UploadProps['onChange'] = ({ fileList: newFileList, file: changeFile }) => {
        setShowImages(newFileList);


        if (changeFile.status == 'removed') {
            dispatch({
                type: 'setModelModel/delPic',
                payload: { uid: changeFile.uid },
            });
        } else if (changeFile.status == 'done') {

            dispatch({
                type: 'setModelModel/addPic',
                payload: { model_id: searchString, image_url: postData.ossPath + postData.name },
            });


            var name = 'admin_' + Date.now() + Math.ceil(Math.random() * 10);
            setPostData({ bucket: 'easyai-resource', ossPath: 'image/' + year + '-' + month + '-' + day + '/', name });
        }

        // dispatch({
        //     type: 'setModelModel/classificationTypeMyList',
        //     payload: { model_id: searchString },
        // });

    }

    const uploadButton = (
        <div>
            <PlusOutlined />
            <div style={{ marginTop: 8 }}>Upload</div>
        </div>
    );


    function handleFinish(values) {

    }

    const openNotification = () => {
        const key = `open${Date.now()}`;
        const btn = (
            <Button type="primary" size="small" onClick={() => notification.close(key)}>
                关闭
            </Button>
        );
        notification.open({
            message: '请输入model_id',
            description:
                'English:Please enter the model_id you want to query',
            btn,
            key,
            onClose: close,
        });
    };

    function searchData() {

        if (searchString == null || searchString.length == 0) {
            // message.warning("请输入model_id");
            openNotification();
            return;
        }



        dispatch({
            type: 'setModelModel/searchFromModelList',
            payload: { model_id: searchString },
        });

        dispatch({
            type: 'setModelModel/searchModelInfo',
            payload: { model_id: searchString },
        });

        dispatch({
            type: 'setModelModel/classificationTypeList',
            payload: { model_id: searchString, type: 1 },
        });

        dispatch({
            type: 'setModelModel/classificationTypeMyList',
            payload: { model_id: searchString, type: 2 },
        });

        setClickSearch(true);


    }


    function saveData() {


    }

    function handleModelTypeChange(value: string) {
        console.log(`handleModelTypeChange to ${value}`);

        dispatch({
            type: 'setModelModel/saveModel',
            payload: { type: 1, id: value, model_id: searchString },
        });

    }

    function handleClassificationTypeChange(value: string) {
        console.log(`handleClassificationTypeChange to ${value}`);
        let list: any[] = [...tags];

        var isHave = false;

        for (const item of tags) {
            if (item.system_dic_id == classificationTypeList[value].system_dic_id) {
                isHave = true;
            }
        }

        for (const item of classificationTypeMyList) {
            if (item.system_dic_id == classificationTypeList[value].system_dic_id) {
                isHave = true;
            }
        }

        console.log(`handleClassificationTypeChange to ${isHave}`);

        if (!isHave) {
            list.push(classificationTypeList[value]);

            setTags([...list]);

            dispatch({
                type: 'setModelModel/addClassificationType',
                payload: { model_id: searchString, model_cate_id: classificationTypeList[value].system_dic_id },
            });

        }


    }

    const handleClose = (index) => {

        dispatch({
            type: 'setModelModel/delClassificationType',
            payload: {
                model_id: searchString,
                model_cate_id: classificationTypeMyList[index].system_dic_id,
                type: 2
            },
        });

    };

    const handleSelectClose = (index) => {

        dispatch({
            type: 'setModelModel/delClassificationType',
            payload: {
                model_id: searchString,
                model_cate_id: tags[index].system_dic_id,
                type: 2
            },
        });

    };

    function handleBaseModelTypeChange(value: string) {
        console.log(`handleBaseModelTypeChange to ${value}`);

        dispatch({
            type: 'setModelModel/saveModel',
            payload: { type: 2, id: value, model_id: searchString },
        });

    }

    // function handleMainModelTypeChange(value: string) {        
    //     console.log(`handleMainModelTypeChange to ${value}`);

    // }

    function handleLookTypeChange(value: string) {
        console.log(`handleLookTypeChange to ${value}`);

        dispatch({
            type: 'setModelModel/saveModel',
            payload: { type: 3, id: value, model_id: searchString },
        });

    }

    function onRecommendChange(value: string) {
        console.log(`onRecommendChange to ${value}`);

        dispatch({
            type: 'setModelModel/saveModel',
            payload: { type: 4, id: value, model_id: searchString },
        });

    }

    return (

        <PageHeaderWrapper title='模型管理'>

            <>
                <div style={{ display: 'none' }}>
                    <Image preview={{ visible, onVisibleChange: vis => setVisible(vis) }}
                        src={previewUrl}
                    >
                    </Image>
                </div>

                <Space split={<Divider type="horizontal" />}>
                    <Input placeholder="可以搜索model_id"
                        onChange={e => {
                            setSearchString(e.target.value);
                            console.log(e.target.value);
                            if (e.target.value === '') {
                                setClickSearch(false);
                            }
                        }}
                        onPressEnter={e => {
                            searchData();
                        }}
                        bordered={true} colorTextPlaceholder='#1677ff'
                        style={{ width: 324, height: 40 }} />
                    <Button type="primary" onClick={
                        () => searchData()
                    }>
                        搜索
                    </Button>
                </Space>
                {
                    (searchModelInfo.length <= 0 && clickSearch && <Result
                        status="warning"
                        title="查询失败，暂无该数据，请确认model_id重新尝试"
                    />)
                }
                {
                    (searchModelInfo.length > 0 && clickSearch && searchString.length > 0) && <div>
                        <Upload
                            action="/megauploadOssCN"
                            listType="picture-card"
                            fileList={showImages}
                            data={postData}
                            onPreview={handlePreview}
                            onChange={handleChange}
                        >
                            {uploadButton}
                        </Upload>

                    </div>

                }
                {(searchModelInfo.length > 0 && clickSearch && searchString.length > 0) && <div>
                    <Form
                        labelCol={{ span: 0 }}
                        wrapperCol={{ span: 0 }}
                        form={form}
                        name='control-hooks'
                        onFinish={handleFinish}
                        size='middle'
                        style={{ width: 300 }}
                    // initialValues={{
                    //     model_type_id: searchModelInfo.length>0?searchModelInfo[0].model_type_id:0,
                    //     model_type_name: searchModelInfo.length>0?searchModelInfo[0].model_type_name:'',
                    //     model_base_id: searchModelInfo.length>0?searchModelInfo[0].model_base_id:0,
                    //     model_base_name: searchModelInfo.length>0?searchModelInfo[0].model_base_name:'',
                    //     is_public: searchModelInfo.length>0?searchModelInfo[0].is_public:0,
                    //     is_recommend: searchModelInfo.length>0?searchModelInfo[0].is_recommend:0,
                    //   }}
                    >

                        <Form.Item name='' label="类型" rules={[{ required: true }]}>
                            <Select value={searchModelInfo.length > 0 ? searchModelInfo[0].model_type_name : 0} style={{ width: 324, height: 40 }} placeholder="请选择类型" onChange={handleModelTypeChange}>
                                {modelTypeList.map((item, index) => {
                                    return <Option key={index.toString()} value={item.system_dic_id}>{item.dic_name}</Option>
                                })}
                            </Select>
                            <Tag>
                                {/* {searchModelInfo.length>0?searchModelInfo[0].model_type_name:0} */}
                            </Tag>
                        </Form.Item>

                        <Form.Item name='' label="分类" rules={[{ required: true }]}>
                            <Space size={[0, 8]} wrap>

                                <Select value={classificationTypeList.length > 0 ? '请选择分类' : '暂无可选分类'}
                                    style={{ width: 324, height: 40 }}
                                    placeholder="请选择分类" onChange={handleClassificationTypeChange}
                                    disabled={classificationTypeList.length <= 0}
                                >
                                    {classificationTypeList.map((item, index) => {
                                        return <Option key={item.system_dic_id} value={index}>{item.dic_name}</Option>
                                    })}
                                </Select>
                            </Space>

                            <Space size={[0, 80]} wrap>
                                <Space split={<Divider type="vertical" />}>

                                    <Space>

                                        {classificationTypeMyList.map((item, index) => {

                                            return <Tag closable={true}
                                                onClose={(e) => { e.preventDefault(), handleClose(index) }}
                                            >
                                                {item.dic_name}
                                            </Tag>
                                        })}
                                        {/* </Space>
                                    <br></br>
                                    <Space> */}
                                        {tags.map((item, index) => {

                                            return <Tag closable={true} color="red"
                                                onClose={(e) => { e.preventDefault(), handleSelectClose(index) }}
                                            >
                                                {item.dic_name}
                                            </Tag>
                                        })}
                                    </Space>
                                </Space>

                            </Space>
                        </Form.Item>

                        <Form.Item name='' label="基础模型" rules={[{ required: true }]}>
                            <Select value={searchModelInfo.length > 0 ? searchModelInfo[0].model_base_name : 0} style={{ width: 324, height: 40 }} placeholder="请选择基础模型" onChange={handleBaseModelTypeChange}>
                                {baseModelList.map((item, index) => {
                                    return <Option key={index.toString()} value={item.system_dic_id}>{item.dic_name}</Option>
                                })}
                            </Select>
                            <Tag>
                                {/* {searchModelInfo.length>0?searchModelInfo[0].model_base_name:0} */}
                            </Tag>
                        </Form.Item>

                        {/* <Form.Item name='msg_type4' label="主模型" rules={[{ required: true }]}>
                            <Select placeholder="请选择主模型" allowClear onChange={handleMainModelTypeChange}>
                                <Option value={0}>文本</Option>
                                <Option value={1}>图片</Option>
                            </Select>
                        </Form.Item> */}

                        <Form.Item name='is_public' label="可见" rules={[{ required: true }]}>
                            <Select value={searchModelInfo.length > 0 ? (searchModelInfo[0].is_public == 1 ? '公共' : '我的') : 0} style={{ width: 324, height: 40 }} placeholder="请选择可见范围(我的、公共)" onChange={handleLookTypeChange}>
                                <Option value={0}>我的</Option>
                                <Option value={1}>公共</Option>
                            </Select>
                            <Tag>
                                {/* {searchModelInfo.length>0?(searchModelInfo[0].is_public==1?'公共':'我的'):0} */}
                            </Tag>
                        </Form.Item>

                        <Form.Item name='is_recommend' label="推荐" rules={[{ required: true }]}>
                            {/* <Switch checkedChildren="推荐" unCheckedChildren="不推荐" onChange={onSwitchChange} defaultChecked /> */}
                            <Select value={searchModelInfo.length > 0 ? (searchModelInfo[0].is_recommend == 1 ? '推荐' : '不推荐') : 0} style={{ width: 324, height: 40 }} placeholder="请选择是否推荐" onChange={onRecommendChange}>
                                <Option value={0}>不推荐</Option>
                                <Option value={1}>推荐</Option>
                            </Select>
                            <Tag>
                                {/* {searchModelInfo.length>0?(searchModelInfo[0].is_recommend==1?'推荐':'不推荐'):0} */}
                            </Tag>
                        </Form.Item>

                    </Form>

                    {/* <Button type="primary" onClick={
                        () => saveData()
                    }>
                        保存
                    </Button> */}
                </div>}
            </>

        </PageHeaderWrapper>

    );
};

const mapStateToProps =
    ({
        loading,
        login,
        setModelModel,
    }: {
        loading: IdvaLoading;
        login: ILogin;
        setModelModel: ISetModelModel;
    }) => ({
        isLoading: loading.models.mstAddImage,
        uid: login.uid,
        searchModelInfo: setModelModel.searchModelInfo,
        searchFromModelList: setModelModel.searchFromModelList,
        modelTypeList: setModelModel.modelTypeList,
        classificationTypeList: setModelModel.classificationTypeList,
        classificationTypeMyList: setModelModel.classificationTypeMyList,
        baseModelList: setModelModel.baseModelList,
    });


export default connect(mapStateToProps)(SetModel);
