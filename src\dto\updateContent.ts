/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-15 15:45:48
 * @LastEditTime: 2020-10-15 16:39:29
 * @LastEditors: jiawen.wang
 */
export interface IupdateContentItemRes {
    id: number
    content: string
    start_time: string
    end_time: string
    create_time: string
}
export interface IupdateContentReq {
    id: number
    content: string
    start_time: string
    end_time: string
}
export interface IeditUpdateContent {
    id: number;
    content: string
    start_time: string
    end_time: string
}