import { IactivityAwardConf, IactivityAwardGroup, IactivityIndex, IgetAwardConfReq, IgetAwardGroupReq } from '../../../../dto/wfActivityAward';
/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-09-21 15:31:50
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-16 17:39:09
 */
import { insertAward, insertAwardGroup, updateAwardGroup, atyList, confList, groupList } from "@/services/apiAtyAward"
import { setAutoConf, autoConfList, getAtyTaskConf, setAtyTaskConf, taskList, getAtyTaskRefreshConf, setAtyTaskRefreshConf } from "@/services/apiAtyConf"
import { IautoConfItem, IgetAllConfReq, IsetAutoConfReq } from '../../../../dto/wfActivityConf';
import { message } from 'antd';

export interface IactivityAward {
    activityList: IactivityIndex[]
    groupList: IactivityAwardGroup[]
    confList: IactivityAwardConf[] //key=awardId,value=IactivityAwardConf[]
    selectAtyItem: IactivityIndex
    selectGroupItem: IactivityAwardGroup
    autoConfigList: IautoConfItem[]
    taskConfList: IautoConfItem[]
    taskRefreshConfList: IautoConfItem[]
    taskList: []
}

const init: IactivityAward = {
    activityList: [],
    groupList: [],
    confList: [],
    selectAtyItem: null,
    selectGroupItem: null,
    autoConfigList: null,
    taskConfList: null,
    taskRefreshConfList: null,
    taskList: []

}
export default {
    namespace: 'activityAward',
    state: init,
    effects: {
        //成就列表
        *fetchAtyList(payload, { call, put, select }) {
            const response: IactivityIndex[] = yield call(atyList, payload);
            if (!response) {
                return
            }
            //更新基础状态
            yield put({ type: 'setAtyList', payload: response });
            yield put({ type: 'setSelectAtyId', payload: null })
            const selectAtyItem = yield select(state => state.activityAward.selectAtyItem)
            if (selectAtyItem == null) {
                yield put({ type: 'fetchAwardGroupList', payload: { activity_id: response[0].id } })
            } else {
                yield put({ type: 'fetchAwardGroupList', payload: { activity_id: selectAtyItem.id } })
            }

        },

        *insertAwardGroup({ payload }: { payload: any }, { call, put }) {
            const response: any = yield call(insertAwardGroup, payload);
            if (!response) {
                return
            }
            message.success("新增成功！")
            yield put({ type: 'fetchAwardGroupList', payload: { activity_id: payload.activity_id } })
        },

        *updateAwardGroup({ payload }: { payload: any }, { call, put }) {
            const response: any = yield call(updateAwardGroup, payload);
            if (!response) {
                return
            }
            message.success("编辑成功！")
            yield put({ type: 'fetchAwardGroupList', payload: { activity_id: payload.activity_id } })
        },

        //奖励组列表
        *fetchAwardGroupList({ payload }: { payload: IgetAwardGroupReq }, { call, put, select }) {
            const response: IactivityAwardGroup[] = yield call(groupList, payload);
            yield put({ type: 'setAwardGroup', payload: response });

            //2
            const selectAtyItem: IactivityIndex = yield select(state => state.activityAward.selectAtyItem)
            if (selectAtyItem == null) {
                return;
            }
            const selectGroupItem: IactivityAwardGroup = yield select(state => state.activityAward.selectGroupItem)
            const req: IgetAwardConfReq = { activity_id: selectAtyItem.id, award_group_id: 1 }
            if (selectGroupItem == null) {
                req.award_group_id = response[0].id
            } else {
                req.award_group_id = selectGroupItem.id
            }
            yield put({ type: 'fetchAwardConfList', payload: req });
        },

        *insertAward({ payload }: { payload: any }, { call, put }) {
            const response: any = yield call(insertAward, payload);
            if (!response) {
                return
            }
            yield put({ type: 'fetchAwardConfList', payload });
        },

        //奖励配置列表
        *fetchAwardConfList({ payload }: { payload: IgetAwardConfReq }, { call, put }) {
            const response: IactivityAwardConf[] = yield call(confList, payload);
            yield put({ type: 'setAwardConf', payload: response });
        },

        //请求全部配置
        *fetchAutoConfMap({ payload }: { payload: IgetAllConfReq }, { call, put }) {
            const response: Map<string, IautoConfItem>[] = yield call(autoConfList, payload);
            yield put({ type: 'setAutoConfMap', payload: response });
        },
        //更新配置
        *fetchSetAutoConf({ payload }: { payload: IsetAutoConfReq }, { call, put }) {
            yield call(setAutoConf, payload);
        },

        //请求任务配置
        *fetchGetAtyTaskConf({ payload }: { payload: IsetAutoConfReq }, { call, put }) {
            const response = yield call(getAtyTaskConf, payload)
            if (response) {
                yield put({ type: 'setTaskConf', payload: response })
            }
        },
        *fetchGetAtyTaskRefreshConf({ payload }: { payload: IsetAutoConfReq }, { call, put }) {
            const response = yield call(getAtyTaskRefreshConf, payload)
            if (response) {
                yield put({ type: 'setTaskRefreshConf', payload: response })
            }
        },


        *fetchSetAtyTaskConf({ payload }: { payload: IsetAutoConfReq }, { call, put }) {
            yield call(setAtyTaskConf, payload)
        },

        //更改任务刷新配置
        *fetchSetAtyTaskRefreshConf({ payload }: { payload: IsetAutoConfReq }, { call, put }) {
            yield call(setAtyTaskRefreshConf, payload)
            yield put({ type: 'fetchGetAtyTaskRefreshConf', payload })
        },

        //请求任务列表
        *fetchTaskList({ payload }: { payload: IsetAutoConfReq }, { call, put }) {
            const response = yield call(taskList, payload)
            if (response) {
                yield put({ type: 'setTaskList', payload: response })
            }
        }
    },


    reducers: {
        //活动列表
        setAtyList(state: IactivityAward, { payload }: { payload: IactivityIndex[] }): IactivityAward {
            return {
                ...state,
                activityList: payload,
            };
        },

        //设置奖励组
        setAwardGroup(state: IactivityAward, { payload }: { payload: IactivityAwardGroup[] }): IactivityAward {
            if (!payload || payload.length === 0) {
                return state;
            }
            if (state.selectGroupItem == null) {
                return {
                    ...state,
                    groupList: payload,
                    selectGroupItem: payload[0]
                };
            }
            return {
                ...state,
                groupList: payload,
            };
        },

        //设置奖励组
        setAwardConf(state: IactivityAward, { payload }: { payload: IactivityAwardConf[] }): IactivityAward {
            if (!payload) {
                return {
                    ...state,
                    confList: [],
                };
            }
            const awardIdMap: IactivityAwardConf[] = []
            //遍历payload
            for (const item of payload) {
                const newItem = { ...item }
                if (awardIdMap[newItem.award_id]) {
                    awardIdMap[newItem.award_id].itemContent.push(newItem)
                } else {
                    newItem.itemContent = [{ ...item }]
                    awardIdMap[item.award_id] = newItem
                }
            }
            //生成map
            const awardConfList: IactivityAwardConf[] = []
            for (const item of awardIdMap) {
                if (item) {
                    awardConfList.push(item)
                }
            }
            return {
                ...state,
                confList: awardConfList,
            };
        },

        setSelectAtyId(state: IactivityAward, { payload }: { payload: IactivityIndex }): IactivityAward {
            if (!payload) {
                return {
                    ...state,
                    selectAtyItem: state.activityList[0],
                };
            }
            return {
                ...state,
                selectAtyItem: payload,
                selectGroupItem: null,
            };
        },

        setSelectGroup(state: IactivityAward, { payload }: { payload: IactivityAwardGroup }): IactivityAward {
            return {
                ...state,
                selectGroupItem: payload,
            };
        },

        //设置自由配置
        setAutoConfMap(state: IactivityAward, { payload }: { payload: IautoConfItem[] }): IactivityAward {
            if (!payload) {
                return state;
            }
            return { ...state, autoConfigList: payload };
        },

        //设置任务配置
        setTaskConf(state: IactivityAward, { payload }: { payload: IautoConfItem[] }): IactivityAward {
            if (!payload) {
                return state;
            }
            return { ...state, taskConfList: payload };
        },

        //设置任务配置
        setTaskRefreshConf(state: IactivityAward, { payload }: { payload: IautoConfItem[] }): IactivityAward {
            if (!payload) {
                return state;
            }
            return { ...state, taskRefreshConfList: payload };
        },

        //设置任务列表
        setTaskList(state: IactivityAward, { payload }): IactivityAward {
            if (!payload) {
                return state;
            }
            return { ...state, taskList: payload };
        },
    }
};
