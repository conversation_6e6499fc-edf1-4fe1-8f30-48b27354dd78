import { IhomeDialogType } from './wfHomeDIalog';
/**
 * 上传banner图步骤
 */
export enum UploadBannerStep {
  Base = 0,
  Upload,
  Hot,
  Success
}
//上传banner基本信息请求
export interface IuploadBannerBaseInfo {
  banner_name: string;
  url: string;
  type: number;
  page: number;
  start_time: string;
  end_time: string;
}
//上传banner图片请求
export interface IuploadBannerBaseImgInfo {
  banner_img: string;
  id: number;
}
//请求banner列表
export interface IbannerListRequest {
  start: number;
  offset: number;
}

export interface INewBannerStore {
  bannerListResponse: IbannerListResponse,
  uploadStep: UploadBannerStep,
  imageName: string,
  banner_id: any,
  typeList: any[],
  turnToPageMap: Map<number, IhomeDialogType>,
  start: number,
  offset: number,
  count: number,
  bannerList: any[],
  editData: any,
  hotImageName: string,
  getBetaBannerList: any[]
}

//响应banner列表
export interface IbannerListResponse {
  start: number;
  offset: number;
  count: number;
  dataArray: IbannerInfo[];
}
//bannerInfo
export interface IbannerInfo {
  id: number;
  banner_name: string;
  banner_img: string;
  url: string;
  type: number;
  page: number;
  start_time: string;
  end_time: string;
  is_show: number;
  is_delsign: number;
  sort_id: number;
  is_complete: number;
  is_prod: number;
  dialog_type_name: string;
}

//更改banner显示/隐藏请求
export interface BannerIsShowRequest {
  id: number;
  is_show: number;
}

//删除banner信息请求
export interface DelBannerInfoRequest {
  id: number;
}
//bannerBaseInfo
export interface IbannerBaseInfo {
  banner_name: string;
  url: string;
  show_type: number;
  type: number;
  page: number;
  start_time: string;
  end_time: string;
  sort_id: number;
  desc: string;
  show_in_eventview: number;
  show_in_hall: number;
  tend_type: number;
  activity_id: number;
  tab1_id: number;
  tab2_id: number;
}
export interface IbannerImageInfo {
  id: number;
  banner_img: string;
}
export interface IUPbannerBaseInfo {
  banner_name: string;
  url: string;
  type: number;
  page: number;
  start_time: string;
  end_time: string;
  id: number;
  sort_id: number;
  is_show: number;
  is_prod: number;
}
export interface IcallBack {
  callback: Function;
}
export interface IdelBanner {
  id: number;
}