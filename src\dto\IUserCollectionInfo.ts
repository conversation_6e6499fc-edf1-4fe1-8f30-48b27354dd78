
export interface IUserCollectionInfo{
  i :number,
  pic : string
}

export interface IUserCollectionActionRecord{
  id :number;
  operate_user_id : number;
  user_id: number;
  index: number;
  url: string;
  createtime: string;
}

export interface IUserCollectionResp{
  dataArray: IUserCollectionInfo[];
}

export interface IInsertUserCollectionCleanRecordParams {
  uid: number;
  user_id: number;
  index: number;
  url: string;
}

export interface IUserCollectionCleanRecord {
  id: number;
  operate_user_id: number;
  user_id: number;
  index: number;
  url: string;
  createtime: string;
}

