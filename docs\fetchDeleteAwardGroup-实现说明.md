# fetchDeleteAwardGroup 实现说明

## 实现概述

基于现有的 `insertAwardGroup` 实现流程，成功实现了 `fetchDeleteAwardGroup` 功能，完整支持奖励组的删除操作。

## 实现步骤

### 1. API 服务层 (`src/services/apiAtyAward.js`)

添加了 `deleteAwardGroup` API 函数：

```javascript
// 删除奖励组
export async function deleteAwardGroup(params) {
  return request(`${API_VERSION}/werewolf/atyAward/deleteAwardGroup`, {
    method: `POST`,
    body: params,
  });
}
```

**特点**：
- 使用 POST 方法（与 insertAwardGroup 保持一致）
- 请求路径：`/werewolf/atyAward/deleteAwardGroup`
- 参数结构需要包含要删除的奖励组 `id` 和 `activity_id`

### 2. Model 层 (`src/pages/AppWolf/ActivityAward/models/activityAward.ts`)

#### 导入更新
```typescript
import { insertAward, insertAwardGroup, updateAwardGroup, deleteAwardGroup, atyList, confList, groupList } from "@/services/apiAtyAward"
```

#### Effect 实现
```typescript
*fetchDeleteAwardGroup({ payload }: { payload: any }, { call, put }) {
    const response: any = yield call(deleteAwardGroup, payload);
    if (!response) {
        return
    }
    message.success("删除成功！")
    yield put({ type: 'fetchAwardGroupList', payload: { activity_id: payload.activity_id } })
},
```

**特点**：
- 使用与 `insertAwardGroup` 相同的异步处理模式
- 包含相同的错误处理机制（检查 response）
- 成功后显示 "删除成功！" 消息
- 删除成功后自动刷新奖励组列表

## 使用方式

### 在组件中调用删除功能

```typescript
// 删除奖励组
const handleDelete = async (item: IactivityAwardGroup) => {
    try {
        await dispatch({
            type: 'activityAward/fetchDeleteAwardGroup',
            payload: {
                id: item.id,                    // 要删除的奖励组ID
                activity_id: current.id         // 当前活动ID
            }
        });
    } catch (error) {
        console.error('删除失败:', error);
    }
};
```

### 参数说明

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `id` | number | 是 | 要删除的奖励组ID |
| `activity_id` | number | 是 | 当前活动ID（用于删除后刷新列表） |

## 与现有功能的对比

| 特性 | insertAwardGroup | updateAwardGroup | fetchDeleteAwardGroup |
|------|------------------|------------------|----------------------|
| API 路径 | `/insertAwardGroup` | `/updateAwardGroup` | `/deleteAwardGroup` |
| HTTP 方法 | POST | POST | POST |
| 必需参数 | `activity_id`, `group_name`, `group_index` | `id`, `activity_id`, `group_name`, `group_index` | `id`, `activity_id` |
| 成功消息 | "新增成功！" | "编辑成功！" | "删除成功！" |
| 后续操作 | 刷新列表 | 刷新列表 | 刷新列表 |

## 错误处理

1. **API 层错误**：网络请求失败或服务器错误
2. **业务逻辑错误**：后端返回错误信息（如奖励组不存在、权限不足等）
3. **参数验证错误**：缺少必需参数

所有错误都会在控制台记录，并且不会显示成功消息。

## 架构一致性

新实现的 `fetchDeleteAwardGroup` 完全遵循了现有的代码架构：

1. **命名规范**：使用 `fetch` 前缀，与其他 effect 保持一致
2. **参数类型**：使用 `{ payload: any }` 类型定义
3. **异步处理**：使用 `yield call` 进行 API 调用
4. **错误处理**：检查 response 是否存在
5. **成功反馈**：使用 `message.success` 显示成功消息
6. **状态更新**：使用 `yield put` 触发列表刷新

## 测试建议

1. **功能测试**：
   - 测试删除现有奖励组
   - 验证删除后列表自动刷新
   - 确认删除操作不可逆

2. **边界测试**：
   - 测试删除不存在的奖励组
   - 测试删除权限验证
   - 测试网络异常情况

3. **集成测试**：
   - 测试删除后其他功能的正常运行
   - 验证删除操作对相关数据的影响

## 后续优化建议

1. **类型安全**：为 payload 添加更严格的类型定义
2. **确认对话框**：在删除前添加用户确认步骤
3. **批量删除**：支持同时删除多个奖励组
4. **软删除**：考虑实现软删除机制以支持恢复操作
