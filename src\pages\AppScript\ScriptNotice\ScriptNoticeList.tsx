import React from "react";
import { connect } from "dva";
import PageHeaderWrapper from "@/components/PageHeaderWrapper";
import { Card, Table, Button, Popconfirm, message } from "antd";
import { ColumnProps } from 'antd/lib/table';
import { PlusOutlined } from "@ant-design/icons";
import { IScriptNoticeListRes } from "@/dto/ScriptNotice"
import CreateModal from "./CreateModal"
import { IScriptNoticeModel } from "./models/officeNews";
import moment from "moment";


export interface IScriptNoticeListProps {
    isLoading: boolean;
    dispatch: Function;
    noticeList: IScriptNoticeListRes[];
}
export interface IScriptNoticeListState {
    isEditModal: boolean;
    isShowModal: boolean;
    noticeIndex: number;
}

@connect(({ loading, login, scriptNotice }: { loading: IdvaLoading; login: ILogin; scriptNotice: IScriptNoticeModel }) => ({
    uid: login.uid,
    isLoading: loading.models['scriptNotice'],
    noticeList: scriptNotice.noticeList,
}))

export default class ScriptNoticeList extends React.Component<IScriptNoticeListProps, IScriptNoticeListState> {
    constructor(props) {
        super(props);
        this.state = {
            isEditModal: false,
            isShowModal: false,
            noticeIndex: 0,
        }
    }
    private columns: ColumnProps<IScriptNoticeListRes>[] = [
        {
            title: 'id',
            dataIndex: 'id'
        },
        {
            title: '标题',
            dataIndex: 'title'
        },
        {
            title: '内容',
            dataIndex: 'content'
        },
        {
            title: '创建时间',
            dataIndex: 'create_time',
            render: val => {
                return <div>{moment(val).format('YYYY-MM-DD HH:mm:ss')}</div>;
            }
        },
        {
            title: '操作',
            render: (val, record, index) => {
                return (
                    <div>
                        <Button type="primary" onClick={() => this.onClickEdit(index)}>
                            编辑
                            </Button>
                        <div style={{ marginTop: 5 }}>
                            <Popconfirm
                                title="是否确定删除本推送?"
                                onConfirm={() => this.onClickDelete(index)}
                                okText="Yes"
                                cancelText="No"
                            >
                                <Button>删除</Button>
                            </Popconfirm>
                        </div>
                    </div>
                );
            }
        }
    ]
    render() {
        const { isLoading, dispatch, noticeList } = this.props;

        const { isEditModal, isShowModal, noticeIndex } = this.state;
        console.log(noticeList)
        return (
            <PageHeaderWrapper title="公告" content={this.renderHeader()}>
                <Table
                    // title={() => '已创建新闻列表'}
                    // className={styles.tableClass}
                    columns={this.columns}
                    dataSource={noticeList}
                    // loading={!!isLoading}
                    rowKey={(record, index) => index.toString()}
                />
                {isShowModal && <CreateModal notice={(noticeList && noticeList.length > 0) ? noticeList[noticeIndex] : null}
                    isEditeModal={isEditModal}
                    loading={!!isLoading}
                    dispatch={dispatch}
                    onHandleCreate={this.onHandleCreate}
                    onHandleEdit={this.onHandleEdit}
                    onHandleClose={this.onHandleClose}
                />}
            </PageHeaderWrapper>
        )
    }
    renderHeader() {
        return (
            <div>
                <div>
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        size="large"
                        loading={!!this.props.isLoading}
                        onClick={this.onClickCreate}
                    >新建</Button>
                </div>
            </div>
        );
    }

    // 点击创建
    onClickCreate = () => {
        this.setState({
            isShowModal: true,
            isEditModal: false,
        })
    };

    // 点击编辑
    onClickEdit = (index: number) => {
        this.setState({
            isShowModal: true,
            isEditModal: true,
            noticeIndex: index
        })
    };
    onHandleClose = () => {
        this.setState({
            isShowModal: false,
            isEditModal: false,
        })
    }
}