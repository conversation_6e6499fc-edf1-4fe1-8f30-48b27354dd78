import { Icoin2wItem } from '@/dto/wf2wCoin';
/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-26 11:28:47
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-06-07 14:22:43
 */
import {
  transactionStatus,
  cpBuyType,
  boxGainType,
  boxOpenType,
  boxGainEType,
  giftType,
  reportComplete,
  wfAvatarFrameType,
  wfAvatarFrameChannel,
  wfAvatarFramePreview,
  wfAvatarFrameDynamic,
  wfDelsign,
  wfAvatarFrameComplete,
  wfComplete
} from "./staticEnum";

/*
 * @Description: 天狼建模
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-26 11:28:52
 * @LastEditors: leeou
 * @LastEditTime: 2019-01-15 13:45:02
 */

export interface IpropsRequest {
  playerId: number;
  uid: number;
}

export interface IpropsResponse {
  userno: number;
  accountnum: number;
  charmnum: number;
  propsone_num: number;
  propstwo_num: number;
  propsthree_num: number;
  propsfour_num: number;
  propsfive_num: number;
  propssix_num: number;
  propsseven_num: number;
  propseight_num: number;
  propsnine_num: number;
  sixendingtime: string;
  sevenendingtime: string;
  eightendingtime: string;
  box_num: number;
  coinnum: number;
  extend_one: null;
  extend_two: null;
  extend_three: null;
  datatime: string;
  delsign: number;
  nickname: string;
  headicon: string;
  binding_phone: string;
  password: string;
}

export interface IdiamodIncRequest {
  uid: number;
  playerId: number;
  increase: number;
  comment: string;
}

export interface IdiamodIncResponse {
  diamond: number;
}

export interface IdiamodDecRequest {
  uid: number;
  playerId: number;
  decrease: number;
  comment: string;
}
export interface IGameInfoList {
  userNo: number
  address: string
  completeTime: string
  keywords: string
  result: string
}
export interface IDownInfo {
  id: number,
  url: string

}
export interface IdiamodDecResponse {
  diamond: number;
}

export interface IoperationType {
  treasureId: number;
  operationId: number;
}

export interface IoperationListRequest {
  uid: number;
  playerId: number;
  start: number;
  offset: number;
}

export interface IoperationTreasure {
  id: number;
  admin_id: number;
  user_id: number;
  type: number;
  num: number;
  createtime: string;
  comment: string;
  item_id: number;
  nickname: string;
}

export interface IoperationListResponse {
  uid: number;
  playerId: number;
  start: number;
  offset: number;
  list: IoperationTreasure[];
  count: number;
}

//用户状态查询基础信息
export interface IplayerStatusRequest {
  playerId: number;
  uid: number;
}
//用户状态查询基础信息
export interface IplayerStatusResponse {
  playerId: number;
  headIcon: string;
  nickName: string;
  createTime: string;
  loginTime: string;
  scorce: number;
  win: number;
  lose: number;
  money: string;
  wolfWinRate: string;
  goodWinRate: string;
  state: number;
  stateMsg: string;
  reason: string;
  groupName: string;//公会名称
  groupTime: string;//入会时间
  giftBuffTime: string;//400钻延时截止时间
  tlevel: IlevelItem[];
  gameTotal: number;
  changePhone: IchangePhone[];
  overseaWhiteList: number
}
export interface IchangePhone {
  id: number;
  oldPhone: string;
  newPhone: string;
  updateTime: string;
}
export interface IlevelItem {
  level: number;
  score: number;
  upscore: number
}

//用户状态刷分记录
export interface IbrushScoreRequest {
  playerId: number;
  uid: number;
  start: number;
  offset: number;
}

//用户状态刷分记录
export interface IbrushScoreResponse {
  playerId: number;
  uid: number;
  offset: number;
  count: number;
  start: number;
  dataArray: IbrushScore[];
}

//刷分记录
export interface IbrushScore {
  count: number;
  desc: string;
  time: string;
}

//用户状态刷分记录
export interface InewBrushScoreRequest {
  playerId: number;
  uid: number;
}

//新刷分记录返回
export interface InewBrushScoreResponse {
  playerId: number;
  uid: number;
  dataArray: InewBrushScore[];
}

export interface InewBrushScore {
  gameNo: number;//游戏场次id
  seat: number;//座位号
  role: string;//角色
  starttime: string;//游戏开始时间（已格式化）
  endtime: string;//游戏结束时间（已格式化）
  reviewtime: string;//审核时间（已格式化）
  user_little: string;//
}

//用户状态逃跑记录
export interface IescapeRequest {
  playerId: number;
  uid: number;
  start: number;
  offset: number;
}

//用户状态逃跑记录
export interface IescapeResponse {
  playerId: number;
  uid: number;
  offset: number;
  count: number;
  start: number;
  dataArray: Iescape[];
}

//玩家逃跑
export interface Iescape {
  seat: number;
  camp: string;
  role: string;
  life: string;
  win: string;
  award: string;
  starttime: string;
  endtime: string;
}

//用户状态游戏中发言记录
export interface IgameSpeakRequest {
  playerId: number;
  uid: number;
  start: number;
  offset: number;
}

//用户状态游戏中发言记录
export interface IgameSpeakResponse {
  playerId: number;
  uid: number;
  offset: number;
  count: number;
  start: number;
  dataArray: IgameSpeak[];
}

//用户状态游戏中发言记录
export interface IgameSpeak {
  reportTime: string;
  reportType: string;
  shutterReason: string;
  effectStartTime: string;
  effectEndTime: string;
  shutterDay: number;
  content: string;
  ossUrl: string;
}

// 用户状态背景图记录
export interface IplayerBgRequest {
  playerId: number;
  uid: number;
  start: number;
  offset: number;
}
// 用户状态背景图记录
export interface IplayerBgResponse {
  playerId: number;
  uid: number;
  offset: number;
  count: number;
  start: number;
  dataArray: IplayerBg[];
}
// 用户状态背景图记录
export interface IplayerBg {
  id: number;
  path: string;
  uasId: number;
  delsign: number;
  rbId: number;
}

//封禁玩家背景图
export interface IbannedBgRequest {
  playerId: number;
  uid: number;
  animationId: number;
}
//封禁玩家背景图
export interface IbannedBgResponse {
  animationId: number;
  playerId: number;
}

// 解除玩家封禁
export class IremoveBannedRequest {
  playerId: number;
  uid: number;
  state: number;
  dingNo: string;
}
// 解除玩家封禁
export class IremoveBannedResponse {
  state: number;
  playerId: number;
  stateMsg: string;
  reason: string;
  err_msg: string;
  removeStatus: number;//0:失败；1:成功；2:已有解除封禁记录，请提交钉钉
}

// 关玩家的禁闭
export interface IshutterPlayerRequest {
  playerId: number;
  uid: number;
  type: number;
  day: number;
  reason: string;
}

// 封娱乐模式
export interface IbanEntertainmentRequest {
  playerId: number
  uid: number
  reason: string
}

// 封玩家的号
export interface IbannedPlayerRequest {
  playerId: number;
  uid: number;
  reason: string;
}
// 封弹幕
export interface IbarragePlayerRequest {
  playerId: number;
  uid: number;
  reason: string;
}
//玩家状态操作流水
export interface IbannedListRequest {
  playerId: number;
  uid: number;
  type: number;
  operation: number;
  start: number;
  offset: number;
}

export interface IbannedListResponse {
  playerId: number;
  uid: number;
  type: number;
  operation: number;
  start: number;
  offset: number;
  count: number;
  list: IbannedList[];
}

export interface IbannedList {
  userName: string;
  playerId: number;
  type: number;
  category: number;
  operation: number;
  desc: string;
  createtime: string;
  delsignStr: string;
}

//请求广告列表
export interface IadListRequest {
  uid: number;
  start: number;
  offset: number;
}
//响应广告列表
export interface IadListResponse {
  uid: number;
  start: number;
  offset: number;
  count: number;
  dataArray: Iadvertising[];
}

export interface Iserver {
  id: number;
  name: number;
}

export interface IgetServerResponse {
  sign: number;
  server: Iserver[];
}

export interface Iadvertising {
  id: number;
  icon: string;
  loading: string;
  iconLink: string;
  loadingLink: string;
  name: string;
  left_1: number;
  left_2: number;
  right_1: number;
  right_2: number;
  ad: number;
}

//请求banner列表
export interface IbannerListRequest {
  uid: number;
  start: number;
  offset: number;
}
//响应banner列表
export interface IbannerListResponse {
  uid: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IbannerInfo[];
}
//banner
export interface IbannerInfo {
  id: number;
  banner_name: string;
  banner_img: string;
  url: string;
  type: number;
  page: number;
  start_time: string;
  end_time: string;
  is_show: number;
}

//上传banner请求
export interface IuploadBannerRequest {
  banner_name: string;
  banner_img: string;
  url: string;
  type: number;
  page: number;
  start_time: string;
  end_time: string;
}

//更改banner显示/隐藏请求
export interface BannerIsShowRequest {
  id: number;
  is_show: number;
}

//删除banner信息请求
export interface DelBannerInfoRequest {
  id: number;
}
// 操作广告请求
export interface IadOperationRequest {
  uid: number;
  aid: number;
  type: number;
}

// 操作广告响应
export interface IadOperationResponse {
  dataArray: Iadvertising[];
}

//详细资产请求
export interface ItreasureRequest {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
}
//资产-红包详情
export interface ItreasureRedbagList {
  group_id: number;
  bag_rest_num: number;
  bag_rest_size: number;
  type: number;
  createtime: string;
  bag_num: number;
  bag_size: number;
  name: string;
  desc: string;
}
export interface ItreasureRedbagResponse {
  dataArray: ItreasureRedbagList[];
}

//资产-背景板权限详情
export interface ItreasureBGAuthority {
  buytime: string;//购买时间
  user_id: string;
  price: string;//价格
  duration: string;//持续时间
}
//资产-背景板权限详情-响应
export interface ItreasureBGAuthorityResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: ItreasureBGAuthority[];
  remaintime: string;//剩余时间
}

//资产-动效商城购买详情
export interface ItreasureSpecialEffectBuy {
  name: string;
  animation_id: string;
  price: string;//价格
  createtime: string;//时间
}
//资产-动效商城购买详情-响应
export interface ItreasureSpecialEffectBuyResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: ItreasureSpecialEffectBuy[];
}
//资产-动效商城赠送详情
export interface ItreasureSpecialEffectGive {
  name: string;
  animation_id: string;
  price: string;//价格
  createtime: string;//时间
  receiver_id: string;//接收人id
  nickname: string//接收人昵称
}
//资产-动效商城购买详情-响应
export interface ItreasureSpecialEffectGiveResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: ItreasureSpecialEffectGive[];
}
//资产-动效商城接收详情
export interface ItreasureSpecialEffectReceive {
  name: string;
  animation_id: string;
  price: string;//价格
  createtime: string;//时间
  sender_id: string;//赠送人id
  nickname: string//赠送人昵称
}
//资产-动效商城接收详情-响应
export interface ItreasureSpecialEffectReceiveResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: ItreasureSpecialEffectReceive[];
}
//资产-盛典商城详情
export interface ItreasureCanonRecord {
  name: string;//盛典名称
  title: string;//名称
  content: string;//描述
  price: string;//价格
  num: number;//数量
  createtime: string//时间
}
//资产-商城消费记录详情
export interface ImallBuyRecord {
  name: string;//道具名称
  price: string;//道具价格
  create_time: string;//时间
  sender_id//赠送人id
  coin_id //货币类型
  coin_name //货币名称
}
export interface IusePropsRecord {
  game_id: number;
  user_id: number;
  create_time: string;
  name: string;
}
export interface IgiftbagReceiveRecord {
  gift_bag_no: number;//礼包名称
  coin_id: string;//购买礼包需要的货币种类
  create_time: string;//购买礼包时间
  coin_num: number;//礼包价格数量
  name: string;
  gift_bag_user_record_history_id: number
}
export interface IgiftbagItemReceiveRecord {
  gift_bag_no: number;//礼包名称
  item_dic_id: string;//物品种类
  receive_time: string;//领取物品时间
  num: number;//领取数量
  name: string;
  item_name: string;
  gift_bag_user_record_history_id: number;
  id: number
}
export interface ItreasuremallBuyRecordResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: ImallBuyRecord[];
}
export interface IusePropsRecordResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IusePropsRecord[];
}
export interface IgiftbagReciveRecordResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IgiftbagReceiveRecord[];
}
export interface IgiftbagItemReceiveRecordResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IgiftbagItemReceiveRecord[];
}
//资产-盛典商城详情-响应
export interface ItreasureCanonRecordResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: ItreasureCanonRecord[];
}
//充值记录返回
export interface ItransactionResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: Itransaction[];
}

export interface Itransaction {
  uid: number; //用户ID
  price: number; //价格(元)
  platform: string; //平台
  num: number; //钻石数量(个)
  identifier: string; //订单号
  udid: string; //设备唯一标识
  udidDetail: number; //0：不可点击；1：可以点击
  bvrs: string; //天狼版本号
  datetime: string; //日期
  status: transactionStatus; //本次充值状态
}

//道具商城购买
export interface IpropsBuyResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IpropsBuy[];
}

export interface IpropsBuy {
  name: string; //道具名称
  desc: string; //道具描述
  price: number; //道具单价
  num: number; //购买得道具数量
  datetime: string; //时间
}

//道具商城使用
export interface IpropsPayResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IpropsPay[];
}

export interface IpropsPay {
  name: string; //道具名称
  desc: string; //道具描述
  num: number; //使用道具数量
  datetime: string; //时间
}

//公会商城购买
export interface IgroupBuyResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IgroupBuy[];
}

export interface IgroupBuy {
  groupPropsName: string; //物品名称
  desc: string; //物品描述
  diamondNum: number; //花费钻石数量
  coinNum: number; //花费活跃币数量
  num: number; //购买数量
  datetime: string; //购买时间
}

//公会商城使用
export interface IgroupPayResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IgroupPay[];
}

export interface IgroupPay {
  groupPropsName: string; //物品名称
  desc: string; //物品描述
  num: number; //使用数量
  datetime: string; //使用时间
}

//CP商城购买
export interface IcpBuyResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IcpBuy[];
}

export interface IcpBuy {
  propsName: string; //物品名称
  desc: string; //物品描述
  addCharm: number; //增加的魅力值
  price: number; //单价
  type: cpBuyType; //购买渠道：1钻石，2其他
  num: number; //购买数量
  datetime: string; //购买时间
}

//CP商城使用
export interface IcpPayResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IcpPay[];
}

export interface IcpPay {
  propsName: string; //物品名称
  desc: string; //物品描述
  cpId: number; //cp id
  addCharm: number; //增加的魅力值
  price: number; //单价
  type: cpBuyType; //购买渠道：1钻石，2其他
  num: number; //购买数量
  datetime: string; //购买时间
}

export interface IcpGiveResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IcpGive[];
}

export interface IcpGive {
  propsName: string; //物品名称
  desc: string; //物品描述
  cpId: number; //接收者ID
  addCharm: number; //增加的魅力值
  addDiamond: number; //增加得钻石
  price: number; //单价
  type: cpBuyType; //购买渠道：1钻石，2其他
  num: number; //购买数量
  datetime: string; //购买时间
}

//开宝箱
export interface IboxOpenReponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IboxOpen[];
}

export interface IboxOpen {
  awardNames: string; //奖励物品名称
  // gainType: boxGainType; //获取渠道
  openType: number; //消费渠道
  // gainCoin: number; //获得兑换币数量
  // gainEType: boxGainEType; //是否兑换成兑换币
  // coin: number; //花费的兑换币数量
  price: number; //花费的钻石数
  coinName: number; //花费的货币类型
  // num: number; //获得奖品数量
  orderNo: number; //订单编号
  datetime: string; //时间
  boxType: number; //宝箱类型 1: 普通宝箱 2：限时宝箱 3：装扮宝箱
  boxName: string; //宝箱名称
}


//开宝箱
export interface IboxGiveReponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IboxGive[];
}

export interface IboxGive {
  name: string; //物品名称
  receiveNo: number; //赠送给谁
  coin: number; //花费的兑换币数量
  price: number; //花费的钻石数
  num: number; //获得奖品数量
  datetime: string; //时间
}

export interface IudidDetailsRequest {
  uid: number;
  udid: string;
  start: number;
  offset: number;
}

export interface IudidDetailsResponse {
  uid: number;
  udid: string;
  start: number;
  offset: number;
  count: number;
  dataArray: Itransaction[];
}

export interface IgiftGiveResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IgiftGive[];
}

export interface IgiftGive {
  name: string; //物品名称
  price: number; //礼物价格
  desc: string; //礼物描述
  receiveId: number; //接收人ID
  addDiamond: number; //增加得钻石
  addCharm: number; //增加得魅力值
  num: number; //赠送数量
  type: giftType; //购买渠道
  datetime: string; //时间
}

export interface IgiftReceiveResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IgiftReceive[];
}

export interface IgiftReceive {
  name: string; //物品名称
  price: number; //礼物价格
  desc: string; //礼物描述
  userId: number; //赠送人ID
  addDiamond: number; //增加得钻石
  addCharm: number; //增加得魅力值
  num: number; //赠送数量
  type: giftType; //购买渠道
  datetime: string; //时间
}

export interface IreportRequest {
  playerId: number;
  uid: number;
  // start: number;
  // offset: number;
}

export interface IreportResponse {
  playerId: number;
  uid: number;
  // start: number;
  // offset: number;
  count: number;
  dataArray: Ireport[];
}
export interface IudidQueryList {
  DeviceName: string;
  createtime: string;
  logintime: string;
  model: string;
  nickname: string;
  no: number;
  password: string;
  username: string;
}
export interface ImemberChangeSearch {
  user_noSearch: number;
  old_usernameSearch: string;
  new_usernameSearch: string;
  statusSearch: number;
  createtimeSearch: string;
  updatetimeSearch: string;
  timeSearch: string;
}

export interface Ireport {
  datetime: string; //举报时间
  type: string; //举报类型
  complete: reportComplete; //记录状态
  gameNo: number; //游戏id
  reportUser: string; //举报人
  content: string; //自定义举报内容
  version: number; //内容版本默认1提供视频列表，2提供视频地址
  videoUrl: string;//默认为空
  keyWords: string; //关键字
}

/**
 * 违规图片请求
 */
export interface IillegalImagesRequest {
  playerId: number;
  uid: number;
  start: number;
  offset: number;
}

/**
 * 违规图片返回
 */
export interface IillegalImagesResponse {
  playerId: number;
  uid: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IillegalImages[];
}

export interface IillegalImages {
  url: string;
  createtime: string;
  resason: string;
}

/*
 * 用户个人头像框查询返回数据结构
 */
export interface IuserFrameResponse {
  uid: number;
  playerId: number;
  type: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IuserAvatarFrame[];
}

/**
 * 头像框基本数据结构
 */
export interface IuserAvatarFrame {
  id: number;
  name: string;
  url: string;
  noteUrl: string;
  type: wfAvatarFrameType;
  channel: wfAvatarFrameChannel;
  preview: wfAvatarFramePreview;
  remark: string;
  price: number;
  createtime: string;
  uid:string;
  is_dynamic: wfAvatarFrameDynamic;
  is_complete: wfAvatarFrameComplete;
  delsign: wfDelsign;
}

/**
 * 头像框列表请求类
 */
export interface IavFrameListRequest {
  channel: wfAvatarFrameChannel;
  isDynamic: wfAvatarFrameDynamic;
  isComplete: wfAvatarFrameComplete;
  start: number;
  offset: number;
  frameName: string;
  frameRemark: string;
}
export interface FrameRequestPeriod {
  frameName: string;
}
export interface FrameRequestPeriodSort {
  id: number;
  show_place: number
}
export interface FrameRequestPeriodid {
  id: number;
}

/**
 * 头像框列表响应类
 */
export interface IavFrameListResponse {
  start: number;
  offset: number;
  count: number;
  dataArray: IuserAvatarFrame[];
}

/**
 *头像框目标用户信息请求
 */
export interface IavFrameUserListRequest {
  userIds: string;
}
/**
 *头像框目标用户
 */
export interface IuserSimInfo {
  no: string;
  nickname: string;
}
/**
 *头像框给与目标用户请求
 */
export interface IgiveAvFrameRequest {
  userIds: string;
  id: number;
  uid: number;
}
/**
 *头像框目标用户信息响应
 */
export interface IavFrameUserListResponse {
  err_msg: string;
  dataArray: IuserSimInfo[];
}

/**
 * 修改用户头像框状态请求
 */
export interface IupdateUserFrameDelsignRequest {
  uid: number;
  playerId: number;
  avatarId: number;
  delsign: wfDelsign;
}
/**
 * 修改用户头像框状态返回
 */
export interface IupdateUserFrameDelsignResponse {
  dataArray: IuserAvatarFrame[];
}

/**
 *头像框是否显示
 */
export interface IavFrameIsShowRequest {
  id: number;
  delsign: number;
  uid: number;
}

/**
 * 上传头像框第一步，填写除图片外的详细信息
 */
export interface IuploadAvatarFrameRequest {
  uid: number;
  name: string;
  type: wfAvatarFrameType;
  channel: wfAvatarFrameChannel;
  remark: string;
  price: number;
  is_dynamic: wfAvatarFrameDynamic;
  item_cate_id: number;
  level: number;
}

export interface IuploadAvatarFrameResponse {
  avatarId: number;
}

/**
 * 头像操作流水
 */
export interface IopeAvaRecordListRequest {
  uid: number;
  playerId: number;
  start: number;
  offset: number;
  otype: number;
}
export interface IopeAvaRecordListResponse {
  start: number;
  offset: number;
  count: number;
  list: IopeAvaRecord[];
}
export interface IopeAvaRecord {
  nickname: string;
  user_id: number;
  avatar_frame_id: number;
  avatar_frame_name: string;
  type: number;
  create_time: string;
}

export interface IavatarFrameImgRequest {
  avatarId: number;
  isDynamic: number;
}

export interface IavatarFrameImgResponse {
  dataArray: IavatarFrameImg[];
}

export interface IavatarFrameImg {
  url: string;
  name: string;
  status: boolean;
}

export interface IavatarFrameIsCompleteResponse {
  complete: number;
}

export interface IframeCompleteRequest {
  avatarId: number;
}

export interface IuploadLetteringRequest {
  name: string;
}

export interface IuploadLetteringResponse {
  id: number;
  name: string;
}

export interface IupdateUserLetteringRequest {
  playerId: number;
  avatarId: number;
  uid:string;
  letteringId: number;
}

/**
 * 成就
 */
export interface IachievementListRequest {
  uid: number;
  start: number;
  offset: number;
  isComplete: wfComplete;
}

export interface IachievementListResponse {
  uid: number;
  start: number;
  offset: number;
  count: number;
  isComplete: wfComplete;
  dataArray: Iachievement[];
}

export interface Iachievement {
  id: number;
  name: string;
  url: string;
  remark: string;
  is_complete: wfComplete;
  delsign: wfDelsign;
}

export interface IupdateAchieveDelsignRequest {
  uid: number;
  achieveId: number;
  delsign: wfDelsign;
}

export interface IupdateAchieveDelsignResponse {
  dataArray: Iachievement[];
}

export interface IsendAchieveToUserRequest {
  uid: number;
  playerId: string;
  achieveId: number;
}

export interface IuploadAchieveBaseRequest {
  uid: number;
  name: string;
  remark: string;
}

export interface IuploadAchieveCompleteRequest {
  uid: number;
  achieveId: number;
  selectCoin2w?: Icoin2wItem;
}

export interface IuploadAchieveCompleteResponse {
  is_complete: wfComplete;
}

export interface IuserAchieveRequest {
  uid: number;
  playerId: number;
  start: number;
  offset: number;
}

export interface IuserAchieveResponse {
  uid: number;
  playerId: number;
  start: number;
  offset: number;
  count: number;
  dataArray: Iachievement[]
}

export interface IuserAchieveUpdateDelsignRequest {
  uid: number;
  playerId: number;
  achieveId: number;
  delsign: wfDelsign;
}

export interface IuserAchieveUpdateDelsignResponse {
  dataArray: Iachievement[]
}
/**
 * 成就操作流水
 */
export interface IopeAchRecordRequest {
  uid: number;
  playerId: number;
  start: number;
  offset: number;
  type: number;
}
export interface IopeAchRecordResponse {
  start: number;
  offset: number;
  count: number;
  list: IopeAchRecord[];
}
export interface IopeAchRecord {
  nickname: string;
  user_id: number;
  achieve_id: number;
  achieve_name: string;
  type: number;
  create_time: string;
}

export interface IshutterRequest {
  uid: number;
  playerId: number;
}

export interface IshutterResponse {
  uid: number;
  playerId: number;
  dataArray: Ishutter[]
}

export interface Ishutter {
  type: number;
  release_time: string;
  desc: string;
  id: number;
  bgs: string;
  avatars: string;
  customDesc: string;
  isForever: number; //0非永久1永久
}

export class IliftShutterRequest {
  uid: number;
  playerId: number;
  type: number;
  dingNo: string;
}
export class IliftShutterResponse {
  status: number;//0：失败 1：成功 2：已有解除封禁记录，请提交钉钉
}

export interface IreportVideoListRequest {
  uid: number;
  playerId: number;
  gameId: number;
}

export interface IreportVideoListResponse {
  uid: number;
  playerId: number;
  gameId: number;
  dataArray: IreportVideoList[];
}

export interface IreportVideoList {
  datetime: string;
  url: string;
}

export class IboardcastImpListRequest {
  uid: number;
  playerId: number;
  start: number;
  offset: number;
}
export class IboardcastImpListResponse {
  uid: number;
  playerId: number;
  start: number;
  offset: number;
  dataArray: IboardcastImp[];
}

export class IboardcastImp {
  bcId: string;
  bcType: number;
  comment: string;
  speakTime: string;
  contentType: string;
  impTime: string;
}
export interface IidCardInfo {
  idcard: number;
  create_time: string;
  request_id: string;
}
export interface IupdateIdCardInfoReq {
  playerId: number
  idcard: string;
  reason: string
}
export class ActivityStateOperReq {
  activityId: number;
  operState: number; //0关闭 1开启
}
export class ActivityStateOperRes {
  no: number;
  num: number;
  word: string;
  state: number;
  start_time: string;
  end_time: string;
  open_start_time: string;
  open_end_time: string;
}
export class ActivityChangeNameRequest {
  activityId: number;
  name: string;
}
// tslint:disable-next-line:max-classes-per-file
export class ActivityChangePrizeNumRequest {
  activityId: number;
  num: number;
}
// tslint:disable-next-line:max-classes-per-file
export class BanUserAbilityRequest {
  uid: number;
  playerId: number;
  abilityId: number;
  banTime: number;
  reason: string;
  imgurl: string;
}

// tslint:disable-next-line:max-classes-per-file
export class RemoveForeverBlockRequest {
  uid: number;
  playerId: number;
  abilityId: number;
}

// tslint:disable-next-line:max-classes-per-file
export class SendEmailsToUsersRequest {
  userList: [];
  title: string;
  content: string;
}

// tslint:disable-next-line:max-classes-per-file
export class SearchNewUserListResponse {
  deviceToken: string;
  isAndroid: number;
}

// tslint:disable-next-line:max-classes-per-file
export class PushMessageResquest {
  userId: number[];
  content: string;
}

// tslint:disable-next-line:max-classes-per-file
export class AddScreenAdImgRequest {
  name: string;
  icon: string;
  loading: string;
  loadimgname: string;
  tend: string;
  ad_tend: string;
  state: number;
  anchor: number;
  fileType: string;
  duration: number;
}

// tslint:disable-next-line:max-classes-per-file
export class SaveAdInfoRequest {
  id: number;
  open: number;//0关闭，1开启
}

// tslint:disable-next-line:max-classes-per-file
export class IGameRecordListRequest {
  playerId: number;
  start: number;
  offset: number;
}
//违规处理
export class IDeductionRecordRequest {
  playerId: number;
  start: number;
  offset: number;
}
export class IDeductionRecordResponse {
  playerId: number;
  start: number;
  offset: number;
  count: number;
  dataArray: UserList[];
}
export interface UserList {
  no: number;   //id
  user_no: number;          // 用户id
  game_list: string;    // 被扣分局列表
  game_num: number;     //   被扣局数
  cut_score: number;   //   被扣分分数
  user_little: string;  // null or cheat_group_id
  createtime: string;   //创建时间
  type: number; //用户状态
  id_delsign: number; //0未处理 ， 1已返还  2违规不返还
}

// tslint:disable-next-line:max-classes-per-file
export class IGameRecordListResponse {
  playerId: number;
  start: number;
  offset: number;
  count: number;
  dataArray: GameRecordItem[];
}

// tslint:disable-next-line:max-classes-per-file
export class GameRecordItem {
  game_no: number;
  seat_index: number;
  group_id: number;
  camp_no: string;
  role_name: string;
  life: string;
  win: string;
  escape: string;
  award: string;
  game_desc: string;
  server_name: string;
  starttime: string;
  endtime: string;
  rated: string;
  scorce: number;
  scorce_d: number;
  mvp: number;
  userNo: number;
  nickname: string;
  speak_time: string;
  minVersion: string;
  jvm_name: string;
}

export interface IgameRecordDetailReq {
  gameNo: number;
}
export interface IgameRecordDetailResp {
  dataArray: GameRecordItem[];
}

// tslint:disable-next-line:max-classes-per-file
export interface IgameReplayRequest {
  gameNo: string
}

// tslint:disable-next-line:max-classes-per-file
export interface IgameReplayResponse {
  note: string;
  peopleList: PeopleListItem[];
}

// tslint:disable-next-line:max-classes-per-file
export interface PeopleListItem {
  headicon: string;
  role_name: string;
  role_id: number;
  seat_index: number;
}

// 融云请求参数
// tslint:disable-next-line:max-classes-per-file
export interface IrongCloudRequest {
  userNo: string;
  groupNo: string;
  time: number;
  start: number;
  offset: number;
}
// 融云返回参数
// tslint:disable-next-line:max-classes-per-file
export interface IrongCloudResponse {
  userNo: string;
  groupNo: string;
  time: number;
  start: number;
  offset: number;
  count: number;
  dataArray: IrongCloudItem[];
}
// 融云数据结构
// tslint:disable-next-line:max-classes-per-file
export interface IrongCloudItem {
  _id: string;
  appid: string;
  fromuserid: string;
  targetid: string;
  targettype: number;
  groupid: string;
  classname: string;
  content: IrongCloudContent;
  datetime: string;
  timestamp: string;
  msguid: string;
  source: string;
}

// tslint:disable-next-line:max-classes-per-file
export interface IrongCloudContent {
  content: string;
  user: IrongCloudContentUser;
  isburnafterread: boolean;
  burnduration: number;
}

// tslint:disable-next-line:max-classes-per-file
export interface IrongCloudContentUser {
  id: string;
  name: string;
  portrait: string;
}

export interface IbroadcastListReq {
  playerId: number;
}
export interface IbroadcastListResp {
  dataArray: IbroadcastItem[];
}
export interface IbroadcastItem {
  id: number;
  group_id: number;
  bc_id: number;
  bc_type: number;
  comment: number;
  user_id: number;
  createtime: number;
  nickname: string;
}
export interface IbroadcastBanReq {
  playerId: number;
  broadcastId: number;
}

export interface IapiDocItem {
  id: number;
  doc_name: string;
  doc_url: string;
  doc_desc: string;
}

export interface ItabnormalDescItem {
  id: number;
  user_name: string;
  describe: string;
  sort: number;
}

//查询逃跑列表
export interface IescapeListReq {
  uid: number;
  startTime: string;
  endTime: string;
}

export interface IescapeListItem {
  game_no: number;
  user_no: number;
}

//请求清理逃跑
export interface IclearEscapeReq {
  uid: number;
  game_no: number;
  user_no: number;
  index: number;
}

export interface ItnoticeRes {
  title: string;
  content: string;
  n_time: string
}

export interface IIdCardList {
  user_id: number;
  udid: string;
}
