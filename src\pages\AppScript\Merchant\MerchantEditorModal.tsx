/* eslint-disable react/destructuring-assignment */
/*
 * @Description: 称号-编辑模态页
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-10-12 16:56:14
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-21 13:21:26
 */

import { Component } from 'react';
import { connect } from 'dva';
import { ILogin } from '@/models/login';
import { Modal, Switch, Input, Select, message, InputNumber, Button, AutoComplete, Popconfirm } from 'antd';
import * as styles from './MerchantEditorModal.less';
import { wfPropsGiftSourceType, wfPropsGiftType, wfPropsType } from "../../../dto/staticEnum";
import { PlusOutlined, MinusCircleOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { getGiftSourceStr, checkNameExist } from '@/utils/mallTool';
import { getCateName, getSelectName, getOptionList } from '@/utils/mallTool';
import React from 'react';
const { TextArea } = Input;
const { Option } = Select;

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  statusList: scriptkill.statusList,
  dayList: scriptkill.dayList,
  currentMerchantScriptList: scriptkill.currentMerchantScriptList,
  difficultyList: scriptkill.difficultyList,
  merchantDicList: scriptkill.merchantDicList,
  cityList: scriptkill.cityList,

}))

class MerchantEditorModal extends React.Component<any, any> {
  constructor(props) {
    super(props);
    const { updateModalType, current } = props;

    console.log("current", current);
    
    //编辑
    if (updateModalType == 1) {
      this.state = {
        id: current.id,
        name: current.name,
        telStr: current.telStr,
        cityName: current.cityName,
        city: getSelectName(current.cityCode, this.props.cityList),
        cityCode: current.cityCode,
        address: current.address,
        tagList: JSON.parse(JSON.stringify(current.tagListModal)),
      };
    }
  }

  render() {
    const { updateModalType, current, isLoading } = this.props;
    let title = '';
    let okText = '提交';
    if (updateModalType == 1) {
      title = '编辑商铺ID【' + current.id + '】';
      okText = '提交编辑';
    }
    return (
      <Modal
        width={550}
        bodyStyle={{ padding: '5px 10px 5px 10px' }}
        closable={false}
        maskClosable={false}
        confirmLoading={!!isLoading}
        centered={true}
        title={title}
        onCancel={this.onCloseModel}
        onOk={this.onClickOk}
        cancelText="取消"
        okText={okText}
        visible={updateModalType > 0}
      >
        <div>
          {updateModalType > 0 && this.renderEditModal()}
        </div>
      </Modal>
    );
  }

  renderEditModal = () => {
    const { updateModalType } = this.props;
    return (
      <div className={styles.modalForm}>
        <div className={styles.formItem}>
          <div className={styles.title}>名称：</div>
          <Input
            className={styles.content}
            value={this.state.name}
            onChange={this.onChangeName}
          />
        </div>
        {/* <div className={styles.formItem}>
          <div className={styles.title}>电话：</div>
          <Input
            className={styles.content}
            value={this.state.telStr}
            onChange={this.onChangetelStr}
          />
        </div> */}
        <div className={styles.formItem}>
          <div className={styles.title}>市：</div>
          <AutoComplete
            allowClear={true}
            className={styles.content}
            options={getOptionList(this.props.cityList)}
            onSelect={this.onSelectMerchant}
            onChange={this.onChangeMerchant}
            value={this.state.city}
            placeholder="请输入选择"
            filterOption={(inputValue, option) =>
              option.value.indexOf(inputValue) !== -1
            }
          />
        </div>
        <div className={styles.formItem}>
          <div className={styles.title}>具体地址：</div>
          <Input
            className={styles.content}
            value={this.state.address}
            onChange={this.onChangeaddress}
          />
        </div>
        <div className={styles.formItem}>
          <div className={styles.title}>标签：</div>
          <div className={styles.contentItem}>
            {this.state.tagList.map((item, index) => {
              console.log("itemitem", item);

              return (
                // tslint:disable-next-line: jsx-key
                <div key={index} className={styles.modalFormSub}>
                  <div className={styles.formItemSub}>
                    <Input
                      className={styles.content}
                      value={item.content}
                      onChange={({ target: { value } }) => {
                        this.onChangeTag(value, item)
                      }}
                    />
                  </div>
                  <div className={styles.formItemSub}>
                    <Popconfirm
                      title={`确定要删除标签吗?`}
                      onConfirm={() => {
                        this.deleteItem(item);
                      }}
                      okText="Yes"
                      cancelText="No"
                    >
                      <Button
                        className={styles.contentButtonSub}
                        type="primary"
                        shape="circle"
                        icon={<MinusCircleOutlined />}
                        size="small"
                      />
                    </Popconfirm>
                  </div>
                </div>
              );

            })}
            <Button className={styles.searchButton} type="primary" icon={<PlusOutlined />} onClick={this.onClickCreateTheme}>新增主题</Button>
          </div>
        </div>
        {/* <div className={styles.formItem}>
          <div className={styles.title}>状态：</div>
          <Switch
            className={styles.toggle}
            checkedChildren="上架"
            unCheckedChildren="下架"
            defaultChecked={this.state.delsign == null || this.state.delsign == 0}
            onChange={this.onChangedelsign}
          />
        </div> */}
      </div>
    );
  };

  onSelectMerchant = (value) => {
    const val = value.substring(4, value.indexOf(" 名称"));
    this.setState({ cityCode: val });
  };

  onChangeMerchant = (data: string) => {
    this.setState({ city: data });
  };

  onChangeTag = (value, item) => {
    console.log("value", value);
    const { tagList } = this.state;
    for (const theme of tagList) {
      if (theme.ketIndex == item.ketIndex) {
        item.content = value;
      }
    }
    this.setState({ tagList })
  };

  onSelectScrpit = (value, option, item) => {
    const { themeDicList } = this.state;
    const val = value.substring(4, value.indexOf(" 名称"));
    for (const theme of themeDicList) {
      if (theme.ketIndex == item.ketIndex) {
        item.id = val;
        item.value = value;
      }
    }
    this.setState({ themeDicList })
  };

  onChangeScrpit = (value, item) => {
    const { themeDicList } = this.state;
    for (const theme of themeDicList) {
      if (theme.ketIndex == item.ketIndex) {
        item.value = value;
      }
    }
    this.setState({ themeDicList })
  };

  getThemeValue = (item) => {
    const { themeDicList } = this.state;
    for (const theme of themeDicList) {
      if (theme.ketIndex == item.ketIndex) {
        item.id = null;
      }
    }
    this.setState({ themeDicList })
  };

  onClickCreateTheme = () => {
    const { tagList } = this.state;
    let ketIndex = 0;
    for (const item of tagList) {
      if (ketIndex < item.ketIndex) {
        ketIndex = item.ketIndex;
      }
    }
    ketIndex += 1;
    const item = { ketIndex, content: null };
    tagList.push(item);
    this.setState({ tagList })
  };

  deleteItem = (item: any) => {
    const { tagList } = this.state;
    let index = 0;
    for (const theme of tagList) {
      if (theme.ketIndex == item.ketIndex) {
        break;
      }
      index++;
    }
    tagList.splice(index, 1);
    this.setState({ tagList })
  }

  onChangeHot = (checked: boolean) => {
    this.setState({ hot: checked ? 1 : 0 });
  };

  onChangeDesc = ({ target: { value } }) => {
    this.setState({ script_desc: value });
  };

  onChangeName = ({ target: { value } }) => {
    this.setState({ name: value });
  };

  onChangetelStr = ({ target: { value } }) => {
    this.setState({ telStr: value });
  };

  onChangeNum = (value) => {
    this.setState({ role_num: value });
  };

  onChangeCondition = ({ target: { value } }) => {
    this.setState({ tips: value });
  };

  onChangeDescribe = ({ target: { value } }) => {
    this.setState({ script_desc: value });
  };

  onChangeSort = ({ target: { value } }) => {
    this.setState({ cityName: value });
  };

  onChangeaddress = ({ target: { value } }) => {
    this.setState({ address: value });
  };

  onChangeSex = (value) => {
    this.setState({ sex: value });
  };

  onChangeSource = (value) => {
    this.setState({ source: value });
  };

  onChangeLevel = (value) => {
    this.setState({ level: value });
  };

  onChangeType = (value) => {
    this.setState({ difficulty: value });
  };

  onChangehot = (checked: boolean) => {
    this.setState({ is_default: checked ? 1 : 0 });
  };

  onChangedelsign = (checked: boolean) => {
    this.setState({ delsign: checked ? 0 : 1 });
  };

  checkNullStr = (value) => {
    return value || value == 0 ? value : "";
  };

  // checkSame = (value1, value2, needPre) => {
  //   if (value1 != value2) {
  //     if (needPre) {
  //       return value1 + "->" + value2;
  //     } else {
  //       return value2;
  //     }

  //   }
  //   return "";
  // };

  // checkSameDifficulty = (value1, value2) => {
  //   if (value1 != value2) {
  //     let val1 = getCateName(value1, this.props.difficultyList);
  //     let val2 = getCateName(value2, this.props.difficultyList);
  //     return val1 + "->" + val2;
  //   }
  //   return "";
  // };

  // handleRequestOperateInfo = (request: any) => {

  //   const item: any = this.props.currentMerchant;

  //   let operateInfo: any = {};
  //   operateInfo.operate_user_id = this.props.uid;
  //   operateInfo.operate_type = 1;

  //   operateInfo.name = this.checkSame(item.name, request.name, true);
  //   operateInfo.script_desc = this.checkSame(item.desc, request.script_desc, false);
  //   operateInfo.publisher = this.checkSame(item.publisher, request.publisher, true);
  //   operateInfo.role_num = this.checkSame(item.roleNum, request.role_num, true);
  //   operateInfo.tips = this.checkSame(item.tips, request.tips, false);
  //   operateInfo.hot = this.checkSame(item.hot, request.hot, true);
  //   operateInfo.difficulty = this.checkSameDifficulty(item.difficulty, request.difficulty);
  //   operateInfo.delsign = this.checkSame(item.delsign, request.delsign, true);
  //   request.operateInfo = operateInfo;
  //   this.checkSameTheme(request);
  // }

  handleRequest = (isCreating: boolean) => {
    const request: any = {
      id: this.state.id,
      name: this.state.name,
      telStr: this.state.telStr,
      cityName: this.state.cityName,
      cityCode: this.state.cityCode,
      address: this.state.address,
      tagList: this.state.tagList,

      current: this.props.currentPage,
      pageCount: this.props.pageSize,
      tabKey: this.props.tabKey,
      where: this.props.where,
    };
    return request;
  };

  //提交编辑
  handleSubmitEdit = () => {
    const { dispatch } = this.props;
    const request = this.handleRequest(false);
    // this.handleRequestOperateInfo(request);

    console.log("request", request);
    const flag = this.checkRequest(request, false);

    if (flag) {
      dispatch({
        type: 'scriptkill/updateMerchant',
        payload: request
      }).then(() => {
        this.onCloseModel();
      });
    }
  };

  // getOperateTheme = (request: any) => {
  //   let oriStr = "";
  //   let newStr = "";
  //   for (const item of this.props.currentScript.themeDicList) {
  //     oriStr += item.id + ",";
  //   }
  //   for (const item of request.themeDicList) {
  //     newStr += item.id + ",";
  //   }
  //   request.operateInfo.theme = oriStr.substring(0, oriStr.length - 1) + "->" + newStr.substring(0, newStr.length - 1);
  // }

  // checkSameTheme = (request: any) => {
  //   if (request.themeDicList.length != this.props.currentScript.themeDicList.length) {
  //     this.getOperateTheme(request);
  //     return false;
  //   } else {
  //     request.themeDicList.sort((a, b) => { return a.id - b.id });
  //     this.props.currentScript.themeDicList.sort((a, b) => { return a.id - b.id });

  //     for (let index = 0; index < this.props.currentScript.themeDicList.length; index++) {
  //       const oriT = this.props.currentScript.themeDicList[index];
  //       const newT = request.themeDicList[index];
  //       if (oriT.id != newT.id) {
  //         this.getOperateTheme(request);
  //         return false;
  //       }
  //     }
  //     return true;
  //   }
  // }

  checkRequest = (request: any, isCreating: boolean) => {
    if (!isCreating && (request.id == null || request.id == undefined || request.id <= 0)) {
      message.error("数据错误，请重新编辑");
      return false;
    }

    // if (request.script_desc == this.props.currentScript.desc &&
    //   request.publisher == this.props.currentScript.publisher &&
    //   request.role_num == this.props.currentScript.roleNum &&
    //   request.tips == this.props.currentScript.tips &&
    //   request.hot == this.props.currentScript.hot &&
    //   request.difficulty == this.props.currentScript.difficulty &&
    //   request.name == this.props.currentScript.name &&
    //   request.delsign == this.props.currentScript.delsign &&
    //   this.checkSameTheme(request)) {
    //   message.error("没有更改");
    //   return false;
    // }

    if (request.name == null || request.name == undefined || request.name == "") {
      message.error("请填写名称");
      return false;
    }

    if (!request.telStr || request.telStr == "") {
      message.error("请填写电话");
      return false;
    }

    if (!this.state.city || this.state.city == "") {
      message.error("请填写城市");
      return false;
    }

    if (!request.address || request.address == "") {
      message.error("请填写地址");
      return false;
    }

    if (!request.tagList || request.tagList.length <= 0) {
      message.error("请填写标签");
      return false;
    }

    if (!request.tagList || request.tagList.length > 2) {
      message.error("商铺标签数量不能超过2个");
      return false;
    }

    for (const item of request.tagList) {
      if (item.content == undefined || item.content == null || item.content == "") {
        message.error("请填写所有标签");
        return false;
      }
    }

    return true;
  }

  //关闭模态页
  onCloseModel = () => {
    this.props.onClickClose();
  };

  onClickOk = () => {
    const { updateModalType } = this.props;
    if (updateModalType == 1) {
      this.handleSubmitEdit();
    }
  };
}

export default MerchantEditorModal;
