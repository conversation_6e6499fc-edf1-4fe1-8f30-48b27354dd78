/*
 * @Description:
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhangyi
 * @Date: 2021-01-05 13:36:09
 * @LastEditors: zhangyi
 * @LastEditTime: 2021-01-05 17:12:58
 */
import {
  getMomentList,
  getScript,
  getScriptRecommendList,
  getScriptSourceList,
  insertScriptRecommend, updateScriptRecommend,
} from '@/services/apiScriptkill';
const request = require('request');

export interface IscriptRecommendList {
  id: number;
  scriptId: number;
  name: string;
  theme: string;
  playTime: string;
  num: string;
  url: string;
  sign: string;
  publisher: string;
  publisherTime: string;
  author: string;
  img: string;
  sort: number;
  show: number;
  delsign: number;
  scriptInfo: string;
  contentBody: string;
  oss_path_key: string;
}

export interface IscriptRecommend {
  scriptRecommendList: IscriptRecommendList[];
  scriptCount: number;
  visible: boolean;
  isUpdate: number;
  script: IscriptRecommendList;
  scriptSourceList: IscriptRecommendList[];
}

const init: IscriptRecommend = {
  scriptRecommendList: [],
  scriptCount: 0,
  visible: false,
  isUpdate: 0,
  script: null,
  scriptSourceList: [],
};

export default {
  namespace: 'scriptRecommend',

  state: init,

  effects: {
    * getScriptRecommendList({ payload }: { payload }, { call, put }) {
      const response = yield call(getScriptRecommendList, payload);
      if (response) {
        yield put({ type: 'setScriptRecommendList', payload: response });
      } else {
        yield put({ type: 'setScriptRecommendList', payload: [] });
      }
    },
    * getScript({ payload }: { payload }, { call, put }) {
      const response = yield call(getScript, payload);
      if (response) {
        request(response.url, (_err, _response, body) => {
          let title = /<title[^>]*>([\s\S]+?)<\/title>/i;
          let rx = /<body[^>]*>([\s\S]+?)<\/body>/i;
          const titleStr = title.exec(body);
          const bodyStr = rx.exec(body);
          if (titleStr && bodyStr) {
            response.title = titleStr[1];
            response.contentBody = bodyStr[1];
          }
        })
        yield put({ type: 'setScript', payload: response });
      } else {
        yield put({ type: 'setScript', payload: [] });
      }
    },
    * getScriptSourceList({ payload }: { payload }, { call, put }) {
      const response = yield call(getScriptSourceList, payload);
      if (response) {
        yield put({ type: 'setScriptSourceList', payload: response });
      } else {
        yield put({ type: 'setScriptSourceList', payload: [] });
      }
    },
    * insertScriptRecommend({ payload }: { payload }, { call, put }) {
      const response = yield call(insertScriptRecommend, payload);
      yield put({ type: 'getScriptRecommendList'});
    },
    * updateScriptRecommend({ payload }: { payload }, { call, put }) {
      const response = yield call(updateScriptRecommend, payload);
      yield put({ type: 'getScriptRecommendList'});
    },
  },

  reducers: {
    setScriptRecommendList(state: IscriptRecommend, { payload }: { payload }): IscriptRecommend {
      return { ...state, scriptRecommendList: payload };
    },
    setScriptCount(state: IscriptRecommend, { payload }: { payload }): IscriptRecommend {
      return { ...state, scriptCount: payload };
    },
    setVisible(state: IscriptRecommend, { payload }): IscriptRecommend {
      return { ...state, visible: payload };
    },
    setIsUpdate(state: IscriptRecommend, { payload }): IscriptRecommend {
      return { ...state, isUpdate: payload };
    },
    setScript(state: IscriptRecommend, { payload }): IscriptRecommend {
      return { ...state, script: payload };
    },
    setScriptSourceList(state: IscriptRecommend, { payload }): IscriptRecommend {
      return { ...state, scriptSourceList: payload };
    },
  },
};
