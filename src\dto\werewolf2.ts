/*
 * @Description: werewolf dto 第二部分
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-10-11 13:49:04
 * @LastEditors: hammercui
 * @LastEditTime: 2019-10-14 18:12:01
 */

//更新gameConfigOpen

export interface IupdateGameConfOpenReq {
  no: number;
  name: string;
  visual_no: number;
  visual_type: number;
  item_dic_id: number;
  level: number;
  price: number;
  coin: number;
  coin_e: number;
  coin_min: number;
  coin_max: number;
  weight: number;
  buy: number;
  exchange: number;
  give: number;
  achievement: number;
  img_name: string;
  type: number;
  num: number;
  num_surplus: number;
  num_user_limit: number;
  num_user_once_limit: number;
  num_daily_limit: string;
  putaway_time: string;
  slotout_time: string;
  desc: string;
  wish: string;
  delsign: string;
  sql_text: string;
}

export interface ITboxUpdateEndTimeReq {
  seasonOri: ITboxSeasonOpenReq;
  seasonNew: ITboxSeasonOpenReq;
}

export interface ITboxSeasonOpenReq {
  season: string;
  starttime: string;
  endtime: string;
}


//更新排序
export interface IupdateGameConfOpenSortReq {
  newSort: GameConfSortItem[]
}

export interface GameConfSortItem {
  gameConfigId: number;
  sort: number;
}

//创建gameConfigOpen
export interface IcreateGameConfOpenReq {
  name: string;
  visual_no: number;
  visual_type: number;
  level: number;
  price: number;
  coin: number;
  coin_e: number;
  coin_min: number;
  coin_max: number;
  weight: number;
  buy: number;
  exchange: number;
  give: number;
  achievement: number;
  img_name: string;
  type: number;
  num: number;
  num_surplus: number;
  num_user_limit: number;
  num_user_once_limit: number;
  num_daily_limit: string;
  putaway_time: string;
  slotout_time: string;
  desc: string;
  wish: string;
  delsign: string;
  sql_text: string;

  seasonRow:ITboxSeasonOpenReq;
  boxLead:BoxLeadItem;
}

export interface BoxLeadItem {
  boxNo: number;
  insertSql: string;
}