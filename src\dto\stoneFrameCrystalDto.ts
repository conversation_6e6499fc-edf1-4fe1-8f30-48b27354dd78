
export interface IInsertFrameCrystalParams {
    name: string,
    pic: string,
    season_id: number,
    hole_exp: number,
    delsign: number,

    illustrated_guide_id: number,//图鉴id
    item_dic_remark: string,
}

export interface IUpdateFrameCrystalParams extends IInsertFrameCrystalParams {
    id: number,
    item_dic_id: number,
}

export interface IFrameCrystalItem {
    id: number,
    name: string,
    pic: string,
    season_id: number,
    season_name: string,
    hole_exp: number,
    delsign: number,

    item_dic_remark: string,
    item_dic_id: number,

    illustrated_guide_id: number,
    illustrated_level: number,
    illustrated_s_no: number,
    illustrated_name: string,
    illustrated_desc: string,
    illustrated_source: number,
    illustrated_img: string,

}