/*
 * @Description: 剧本杀路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 张路
 * @Date: 2020-12-24 10:42:03
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-05 14:06:11
 */
import { AccessRouteId } from './accessRouteCof';
//import deductionRecord from '@/pages/AppWolf/ScoreViolation/models/deductionRecord';

const AppExchangeRoutes = {
  path: 'appExchange',
  name: 'appExchange',
  icon: 'https://s3.ax1x.com/2020/12/24/rcLfYT.jpg',
  Routes: ['src/layouts/Authorized'],
  authority: AccessRouteId.app_exchange,
  routes: [
    {
      path: '/appExchange/projectManager',
      name: 'project',
      icon: 'appstore',
      authority: AccessRouteId.app_exchange,
      component: './AppExchange/ExchangeSys/ProjectManager',
    },
    {
      path: '/appExchange/ExchangeSysOperateManager',
      name: 'projectOpreate',
      icon: 'appstore',
      authority: AccessRouteId.app_exchange_operate,
      component: './AppExchange/ExchangeSysOperate/ExchangeSysOperateManager',
    },
  ],
};

export default AppExchangeRoutes;
