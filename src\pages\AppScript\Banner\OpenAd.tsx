/*
 * @Description: 剧本杀开屏广告
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-26 10:09:17
 * @LastEditors: 赵宝强
 * @LastEditTime: 2021-01-26 14:17:50
 */

import React, { Component } from 'react';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { Button, Card, Image, message, Popconfirm, Table } from 'antd';
import { FormOutlined, UploadOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import { ILogin } from '@/models/login';
import { IMainPageBanner } from '@/pages/AppScript/Banner/models/mainPageBanner';
import { ColumnProps } from 'antd/lib/table';
import moment from 'moment';
import NewOpenAdModel from '@/pages/AppScript/Banner/NewOpenAdModel';
import UpdateOpenAdImg from '@/pages/AppScript/Banner/UpdateOpenAdImg';
import UpdateOpenAdInfo from '@/pages/AppScript/Banner/UpdateOpenAdInfo';
import { FormInstance } from 'antd/lib/form';


const PAGESIZE = 6;

export interface OpenAdState {
  isNewOpenAdModal: boolean,
  currentPage: number,
  updateOpenAdImage: boolean,
  currentOpenAd: OpenAd,
  isUpdateOpenAdInfo: boolean,
}

export interface OpenAdProps {
  count: number,
  openAdList: OpenAd[],
}
@connect(({ loading, login, mainPageBanner}: {loading: IdvaLoading; login: ILogin; mainPageBanner: IMainPageBanner}) =>({
  uid: login.uid,
  isloading: loading.models['mainPageBanner'],
  count: mainPageBanner.count,
  openAdList: mainPageBanner.openAdList,
}))

export default class OpenAd extends Component<OpenAdProps, OpenAdState> {
  formRef = React.createRef<FormInstance>();
  constructor(props) {
    super(props);
    this.state = {
      currentPage: 1,
      isNewOpenAdModal: false,
      updateOpenAdImage: false,

    }
    props.dispatch({
      type: 'mainPageBanner/getOpenAdList'
    });
  }

  render() {
    const { openAdList, count } = this.props;
    const { currentPage, isNewOpenAdModal, updateOpenAdImage, isUpdateOpenAdInfo} = this.state;
    return (
      <div>
      <PageHeaderWrapper title="开屏广告管理" content={this.renderHeader()}>
        <div style={{ marginTop: 20 }}>
          <Card>
            <Table
              title={() => '开屏广告管理'}
              columns={this.openAdTable}
              dataSource={openAdList}
              rowKey={(record, index) => index.toString()}
              scroll={{ x: 2000 }}
              pagination={{
                // 分页
                pageSize: PAGESIZE,
                current: currentPage,
                total: count,
                onChange: this.changePage
              }}
            />
          </Card>
        </div>
      </PageHeaderWrapper>
        <NewOpenAdModel isNewOpenAdModal={isNewOpenAdModal} onClose={this.handleCancelUpModal}/>
        <UpdateOpenAdImg onClose={this.handlerCancelAdImg} updateOpenAdImage={updateOpenAdImage}/>
        <UpdateOpenAdInfo isUpdateOpenAdInfo={isUpdateOpenAdInfo} onClose={this.handleCancelOpenAdInfo}/>
      </div>
  );
  }

  renderHeader() {
    return (
      <div>
        <Button type="primary" icon={<UploadOutlined />} size="large" onClick={this.handleClickUpload}>
          新建开屏广告
        </Button>
      </div>
    );
  }

  //上传banner
  handleClickUpload = () => {
    console.log('点击上传');
    this.setState({ isNewOpenAdModal: true });
  };
  editImageOpenAd = (record) => {
    console.log('更新图片')
    this.setState({updateOpenAdImage: true});
    const { dispatch } = this.props;
    dispatch({
      type: 'mainPageBanner/setCurrentAd',
      payload: record,
    });
  };
  changePage = (page) => {
    this.setState({ currentPage: page });
  };
  //关闭上传模态页
  handleCancelUpModal = () => {
    console.log('关闭上传模态页');
    this.setState({ isNewOpenAdModal: false });
  };
  handleCancelOpenAdInfo = (formRef) => {
    console.log('关闭修改信息窗口');
    this.setState({isUpdateOpenAdInfo : false });

  }

  handlerCancelAdImg = () => {
    console.log('关闭图片上传模板');
    this.setState({updateOpenAdImage: false});
  }
  onClickEdit = (record) => {
    console.log('开启修改信息窗口')
    this.setState({isUpdateOpenAdInfo: true});
    console.log(record)
    this.props.dispatch({
      type: 'mainPageBanner/setCurrentAd',
      payload: record,
    })
  }

  // @ts-ignore
  private openAdTable: ColumnProps<OpenAd>[] = [
    {
      title: '开屏名称',
      width: 30,
      align: 'center',
      dataIndex: 'open_ad_name',
      fixed: true,
    },
    {
      title: '是否展示',
      width: 30,
      align: 'center',
      dataIndex: 'delsign',
      fixed: true,
      render: (val) => {
        return (
          <div style={val == 0 ? { color: 'rgb(0, 200, 83)'} : { color : 'red'}}>
            {val == 0 ? '展示' : '不展示'}
          </div>
        );
      }
    },
    {
      title: '图片展示',
      width: 100,
      align: 'center',
      dataIndex: 'open_ad_url',
      render: (val, record) => {
        if (val != null && val != '') {
          return <Image src={val} width={110} height={180}></Image>
        }
      }
    },
    {
      title: '操作',
      width: 60,
      align: 'center',
      render: (val, record) => {
        let isShowShang: boolean = false;
        let isShowXia: boolean = false;
        if (record.delsign == 0) {
          isShowShang = false
          isShowXia = true
        } else {
          isShowShang = true
          isShowXia = false
        }
        return (
          <div>
            <div style={{marginBottom:8}} >
              <Button
                type="primary"
                onClick={() => this.onClickEdit(record)}
              >
                编辑
              </Button>
              {isShowXia &&(
                <Popconfirm
                  title="确定要下架当前开屏广告吗?"
                  onConfirm={() => {
                    this.props.dispatch({
                      type: 'mainPageBanner/delOpenAd',
                      payload: {
                        id: record.id,
                      }
                    });
                    console.log('下架开屏广告');
                    message.info("下架成功")
                  }}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button type="primary" size={'middle'} danger={true} style={{marginLeft: 10}}>
                    下架
                  </Button>
                </Popconfirm>
              )}
              {( isShowShang &&
                <Popconfirm
                  title="确定要上架当前开屏广告吗?"
                  onConfirm={() => {
                    this.props.dispatch({
                      type: 'mainPageBanner/shelvesOpenAd',
                      payload: {
                        id: record.id
                      }
                    });
                    message.info("上架成功")
                  }}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button type="primary" size="middle" danger={true} style={{marginLeft: 10}}>
                    上架
                  </Button>
                </Popconfirm>

              )}
            </div>
            <div style={{marginTop:15}}>
              <Button
                type="primary"
                icon={<FormOutlined />}
                style={{width:140}}
                onClick={() => this.editImageOpenAd(record)}
              >
                更新图片
              </Button>
            </div>
          </div>
        );
      }
    },
    {
      title: '跳转类型',
      width: 80,
      align: 'center',
      dataIndex: 'type',
      render: (val, record) => {
        switch (record.type) {
          case 1:
            return <div>店铺</div>
          case 2:
            return <div>剧本</div>
          case 3:
            return <div>图片跳转</div>
          case 4:
            return <div>文章</div>
          case 5:
            return <div>订单</div>
          default:
            return <div>未知类型</div>
        }
      }
    },
    {
      title: '跳转地址',
      width: 50,
      align: 'center',
      dataIndex: 'href_url',
      render: (val, record) => {
        return <div>{val == null ? '暂无' : val}</div>
      }
    },
    {
      title: '开始时间',
      width: 60,
      align: 'center',
      dataIndex: 'open_ad_start_time',
      render: (val, record) => {
        return <div>{moment(val, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss')}</div>
      }
    },
    {
      title: '结束时间',
      width: 60,
      align: 'center',
      dataIndex: 'open_ad_end_time',
      render: (val, record) => {
        return <div>{moment(val, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss')}</div>
      }
    }

  ];

}
