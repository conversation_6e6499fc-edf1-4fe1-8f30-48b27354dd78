import React, { PureComponent } from 'react';
import { connect } from 'dva';
import Link from 'umi/link';
import { router } from 'umi';
import { Card, Row, Col, Popconfirm, Avatar, Modal, Divider, Spin, Input, Button } from 'antd';
import GridContent from '@/components/PageHeaderWrapper/GridContent';
import styles from './Center.less';
import { Iproject } from "@/models/project";
import { Iuser, IcurrentUser } from "@/models/user";
import { ILogin } from "@/models/login";
import { RouteComponentProps } from 'react-router-dom';
import { FormComponentProps } from '@ant-design/compatible/lib/form';
import { IlogoutAllRequest, IresetPwdRequest } from '../../../dto/manager';
import PwdEditorModal from './PwdEditorModal';

export interface IcenterProps extends RouteComponentProps<any>, FormComponentProps {
  listLoading: boolean,
  currentUser: IcurrentUser,
  currentUserLoading: boolean,
  project: Iproject,
  projectLoading: boolean,
  uid: number
  dispatch: Function,
  isLogouing: boolean
}

interface Itag {
  key: string,
  label: string
}

export interface IcenterState {
  newTags: Itag[],
  inputVisible: boolean,
  inputValue: string,
  currTabKey: string,
  dataModelType: number,
}

@connect(({ loading, user, project, login }: { loading: IdvaLoading, user: Iuser, project: Iproject, login: ILogin }) => ({
  listLoading: loading.effects['list/fetch'],
  currentUser: user.currentUser,
  currentUserLoading: loading.effects['user/fetchCurrent'],
  project,
  projectLoading: loading.effects['project/fetchNotice'],
  uid: login.uid,
  isLogouing: loading.effects['login/fetchLogouAll']
}))
class Center extends PureComponent<IcenterProps, IcenterState> {

  private input: any;

  constructor(props) {
    super(props);
    this.state = {
      newTags: [],
      inputVisible: false,
      inputValue: '',
      currTabKey: 'safety',
      dataModelType: -1,
    };
  }

  onTabChange = key => {
    const { match } = this.props;
    this.setState({ currTabKey: key });
    // switch (key) {
    //   case 'safety':
    //     //router.push(`${match.url}/articles`);
    //     break;
    //   case 'applications':
    //     //router.push(`${match.url}/applications`);
    //     break;
    //   case 'projects':
    //     //router.push(`${match.url}/projects`);
    //     break;
    //   default:
    //     break;
    // }
  };

  showInput = () => {
    this.setState({ inputVisible: true }, () => this.input.focus());
  };

  saveInputRef = input => {
    this.input = input;
  };

  handleInputChange = e => {
    this.setState({ inputValue: e.target.value });
  };

  //处理登出
  handleLogouAll = () => {
    const { dispatch, uid } = this.props;
    const request: IlogoutAllRequest = { uid };
    dispatch({
      type: "login/fetchLogouAll",
      payload: request
    });
  }

  onCloseModel = () => {
    this.setState({ dataModelType: -1 });
  }

  changePwd = () => {
    this.setState({ dataModelType: 1 });
  }

  // handleInputConfirm = () => {
  //   const { state } = this;
  //   const { inputValue } = state;
  //   let { newTags } = state;
  //   if (inputValue && newTags.filter(tag => tag.label === inputValue).length === 0) {
  //     newTags = [...newTags, { key: `new-${newTags.length}`, label: inputValue }];
  //   }
  //   this.setState({
  //     newTags,
  //     inputVisible: false,
  //     inputValue: '',
  //   });
  // };

  private operationTabList = [
    {
      key: 'safety',
      tab: (
        <span>
          安全中心
        </span>
      ),
    },
    {
      key: 'applications',
      tab: (
        <span>
          施工中
        </span>
      ),
    },
    // {
    //   key: 'projects',
    //   tab: (
    //     <span>
    //       待定
    //     </span>
    //   ),
    // },
  ];

  render() {
    const { dataModelType, } = this.state;
    const {
      listLoading,
      currentUser,
      currentUserLoading,
      project: { notice },
      projectLoading,
      match,
      location,
      children,
    } = this.props;

    return (
      <Spin spinning={!!listLoading}>
        {dataModelType > 0 &&
          <PwdEditorModal
            dataModelType={dataModelType}
            onClickClose={this.onCloseModel}>
          </PwdEditorModal>
        }

        <GridContent className={styles.userCenter}>
          <Row gutter={24}>
            <Col lg={7} md={24}>
              <Card bordered={false} style={{ marginBottom: 24 }} loading={currentUserLoading}>
                {currentUser && Object.keys(currentUser).length ? (
                  <div>
                    <div className={styles.avatarHolder}>
                      <img alt="" src={currentUser.avatar} />
                      <div className={styles.name}>{currentUser.nickname}</div>
                      {/* <div>{currentUser.signature}</div> */}
                    </div>
                    <div className={styles.detail}>
                      <p>
                        <i className={styles.title} />
                        {currentUser.nickname}
                      </p>
                      <p>
                        <i className={styles.address} />
                      登陆ip:{currentUser.loginip}
                      /
                      时间:{currentUser.logintime}
                      </p>
                    </div>
                    <Divider dashed={true} />
                  </div>
                ) : (
                    'loading...'
                  )}
              </Card>
            </Col>
            <Col lg={17} md={24}>
              <Card
                className={styles.tabsCard}
                bordered={false}
                tabList={this.operationTabList}
                // activeTabKey={location.pathname.replace(`${match.path}/`, '')}
                onTabChange={this.onTabChange}
                loading={listLoading}
              >
                {/* {children} */}
                {this.state.currTabKey == 'safety' && this.renderSafety()}
              </Card>
            </Col>
          </Row>
        </GridContent>
      </Spin>
    );
  }

  //渲染安全中心
  renderSafety() {
    const { isLogouing } = this.props
    return (<div>
      <Row gutter={[24, 10]}>
        <Col>
          <Button type="primary" onClick={this.handleLogouAll} loading={isLogouing}>全平台登出</Button>
        </Col>
        <Col>
          <div style={{ marginTop: 5 }}>点击全平台登出，所有设备都需要重新登陆</div>
        </Col>
      </Row>
      <Row gutter={[24, 24]}>
        <Col>
          <Popconfirm
            title={`确定需要修改密码?`}
            onConfirm={this.changePwd}
            okText="是"
            cancelText="否">
            <Button type="primary" loading={isLogouing}>修改密码</Button>
          </Popconfirm>
        </Col>
        <Col>
          <div style={{ marginTop: 5 }}>点击修改登录密码</div>
        </Col>
      </Row>
    </div>)
  }
}

export default Center;
