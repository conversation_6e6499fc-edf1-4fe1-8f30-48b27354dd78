
export interface IInsertRoleSparParamsConfig {
    id: number,
    role_spar_id: number,
    min_hole_level: number,
    max_hole_level: number,
    weight: number,
    delsign: number,
}
export interface IInsertRoleSparParams {
    name: string,
    pic: string,
    season_id: number,
    level: number,
    hole_exp: number,
    delsign: number,

    configs: IInsertRoleSparParamsConfig[],

    illustrated_guide_id: number,//图鉴id
    item_dic_remark: string,

}

export interface IUpdateRoleSparParams extends IInsertRoleSparParams {
    id: number,
    item_dic_id: number,
}

export interface IRoleSparItem {
    id: number,
    name: string,
    pic: string,
    season_id: number,
    season_name: string,
    level: number,
    hole_exp: number,
    delsign: number,

    item_dic_remark: string,
    item_dic_id: number,

    illustrated_guide_id: number,
    illustrated_level: string,
    illustrated_s_no: string,
    illustrated_name: string,
    illustrated_desc: string,
    illustrated_source: number,
    illustrated_img: string,

    configs: IInsertRoleSparParamsConfig[],
}
