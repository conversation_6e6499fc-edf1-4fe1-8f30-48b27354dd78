/*
 * @Author: hammer<PERSON><PERSON>
 * @Date: 2020-07-23 15:00:34
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2021-01-19 15:28:43
 * @FilePath: /ConsoleSystemClient/src/pages/AppWolf/OfficeWeb/models/officeNews.ts
 * @Description: 官方新闻model
 */
import { pushV2List, pushV2Delete, pushV2Create, pushV2Edit, pushV2Test } from '@/services/apiWfPush';
import { IerrorMsg } from '@/dto/common';
import { message } from 'antd';
import { number } from 'prop-types';
import {
  updateMomentSort,
  deleteComment,
  getCommentList,
  getMomentPictureCanShowCount,
  updateMomentPictureUrl,
  updateMomentPicture,
  getMomentList,
  getMomentListCount,
  insertMoment,
  updateMoment,
  updateMomentDelsign,
  getMomentPicList,
  insertMomentPicture
} from '@/services/apiScriptkill';

const request = require('request');


export const OfficeNewsTagName = [
  '',
  '最新情报',
  '产品功能',
  '活动',
  '其他',
  '置顶'
]

const init: any = {
  momentList: [],
  momentListCount: 0,
  pictureList: [],
  commentData: {},
}

export default {
  namespace: 'merchantMoment',

  state: init,

  effects: {
    //请求列表
    *getMomentList({ payload }: { payload: any }, { call, put }) {
      const response = yield call(getMomentList, payload);
      for (const item of response) {
        request(item.material_url, (_err, _response, body) => {
          let rx = /<body[^>]*>([\s\S]+?)<\/body>/i;
          const bodyStr = rx.exec(body);
          if (bodyStr) {
            item.contentBody = bodyStr[1];
          }
        })
      }
      yield put({ type: 'setList', payload: response });
    },

    *getMomentListCount({ payload }: { payload: any }, { call, put }) {
      const response = yield call(getMomentListCount, payload);
      yield put({ type: 'setCount', payload: response.num });
    },

    *insertMoment({ payload }: { payload: any }, { call, put }) {
      const response = yield call(insertMoment, payload);
      if (response) {
        yield put({ type: 'getMomentList', payload });
        yield put({ type: 'getMomentListCount', payload });
      }
    },

    *updateMoment({ payload }: { payload: any }, { call, put }) {
      const response = yield call(updateMoment, payload);
      if (response) {
        yield put({ type: 'getMomentList', payload });
        yield put({ type: 'getMomentListCount', payload });
      }
    },

    *updateMomentDelsign({ payload }: { payload: any }, { call, put }) {
      const response = yield call(updateMomentDelsign, payload);
      if (response) {
        yield put({ type: 'getMomentList', payload });
        yield put({ type: 'getMomentListCount', payload });
      }
    },

    *updateMomentSort({ payload }: { payload: any }, { call, put }) {
      const response = yield call(updateMomentSort, payload);
      if (response) {
        yield put({ type: 'getMomentList', payload });
        yield put({ type: 'getMomentListCount', payload });
      }
    },

    *getMomentPicList({ payload }: { payload: any }, { call, put }) {
      const response = yield call(getMomentPicList, payload);
      if (response) {
        yield put({ type: 'setPicList', payload: response });
      } else {
        yield put({ type: 'setPicList', payload: [] });
      }
    },

    *insertMomentPicture({ payload }: { payload: any }, { call, put }) {
      const response = yield call(insertMomentPicture, payload);
      if (response) {
        yield put({ type: 'getMomentPicList', payload });
      }
    },

    *updateMomentPicture({ payload }: { payload: any }, { call, put }) {
      const response = yield call(updateMomentPicture, payload);
      if (response) {
        yield put({ type: 'getMomentPicList', payload });
      }
    },

    *updateMomentPictureUrl({ payload }: { payload: any }, { call, put }) {
      const response = yield call(updateMomentPictureUrl, payload);
    },

    *getMomentPictureCanShowCount({ payload }: { payload: any }, { call, put }) {
      const response = yield call(getMomentPictureCanShowCount, payload);
      return response;
    },

    *getCommentList({ payload }: { payload: any }, { call, put }) {
      const response = yield call(getCommentList, payload);
      if (response && response.sign == 1) {
        yield put({ type: 'setCommentData', payload: response.data });
      } else {
        yield put({ type: 'setCommentData', payload: {} });
      }
    },

    *getCommentListPage({ payload }: { payload: any }, { call, put }) {
      const response = yield call(getCommentList, payload);
      if (response && response.sign == 1) {
        yield put({ type: 'setCommentDataPage', payload: response.data });
      } else {
        message.error(response.msg);
      }
    },

    *getCommentListSubPage({ payload }: { payload: any }, { call, put }) {
      const response = yield call(getCommentList, payload);
      if (response && response.sign == 1) {
        yield put({ type: 'setCommentDataSubPage', payload: { data: response.data, commentParentId: payload.data.commentParentId } });
      } else {
        message.error(response.msg);
      }
    },

    *deleteComment({ payload }: { payload: any }, { call, put }) {
      const response = yield call(deleteComment, payload);
      if (response && response.sign == 1) {
        yield put({ type: 'setCommentDelete', payload: { commentId: payload.data.commentId } });
      } else {
        message.error(response.msg);
      }
    },
  },

  reducers: {
    setList(state: any, { payload }: { payload: any }): any {
      return { ...state, momentList: payload }
    },

    setCount(state: any, { payload }: { payload: any }): any {
      return { ...state, momentListCount: payload }
    },

    setPicList(state: any, { payload }: { payload: any }): any {
      return { ...state, pictureList: payload }
    },

    setCommentData(state: any, { payload }: { payload: any }): any {
      return { ...state, commentData: payload }
    },

    setCommentDataPage(state: any, { payload }: { payload: any }): any {
      const commentData = state.commentData;
      commentData.pageInfo = payload.pageInfo;
      commentData.commentList.push(...payload.commentList);

      return { ...state, commentData }
    },

    setCommentDataSubPage(state: any, { payload }: { payload: any }): any {
      const commentData = state.commentData;
      for (const item of commentData.commentList) {
        console.log("item", item.commentId);
        console.log("payload.commentParentId", payload.commentParentId);

        if (item.commentId == payload.commentParentId) {
          item.pageInfo = payload.data.pageInfo;
          item.subCommentList.push(...payload.data.commentList);
        }
      }
      return { ...state, commentData }
    },

    setCommentDelete(state: any, { payload }: { payload: any }): any {
      const commentData = state.commentData;

      // eslint-disable-next-line no-irregular-whitespace
      const index = commentData.commentList.findIndex(v => v.commentId === payload.commentId);
      if (index != -1) {
        commentData.commentList.splice(index, 1);
      } else {
        for (const item of commentData.commentList) {
          const subIndex = item.subCommentList.findIndex(v => v.commentId === payload.commentId);
          if (subIndex != -1) {
            item.subCommentList.splice(subIndex, 1);
          }
        }
      }

      return { ...state, commentData }
    },
  }
};
