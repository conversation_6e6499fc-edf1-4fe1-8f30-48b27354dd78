/*
 * @Description: 称号管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2019-11-11 10:09:17
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-21 16:40:34
 */
import { Component, default as React } from 'react';
import {
  Button,
  Spin,
  Card,
  Row,
  Col,
  Radio,
  Image,
  Table,
  Tag,
  Typography,
  Modal,
  Input,
  Popconfirm,
  AutoComplete,
  message
} from 'antd';
import * as styles from './ReportInfoManager.less';
import { ILogin } from '@/models/login';
import { connect } from 'dva';
import { getUserAvatar, getUserBg } from '../ScriptUtils/ScriptUtils';
import { ColumnProps } from 'antd/lib/table';
import { getTimeStr } from '@/utils/momentTool';
import { getCateName } from '@/utils/mallTool';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
export const PAGESIZE = 8;
const defaultPayload = { report_type: 0, current: 1, pageCount: PAGESIZE, };

@connect(({ loading, login, scriptkill, }: { loading: IdvaLoading; login: ILogin; scriptkill: any; }) => ({
  uid: login.uid,
  isLoading: loading.models['scriptkill'],
  merchantList: scriptkill.merchantList,
  merchantCount: scriptkill.merchantCount,
  merchantAuditCount: scriptkill.merchantAuditCount,
  merchantRefuseCount: scriptkill.merchantRefuseCount,
  merchantPassCount: scriptkill.merchantPassCount,
  statusList: scriptkill.statusList,
  reportCount: scriptkill.reportCount,
  reportList: scriptkill.reportList,
  reportTypeList: scriptkill.reportTypeList,
}))

export default class ReportInfoManager extends Component<any, any> {
  constructor(props) {
    super(props);

    this.state = {
      currentPage: 1,
      report_type: 0,
      merchantColumns: this.makeColumns(),
    }

    this.dispatchList(defaultPayload);
    this.dispatchListCount(defaultPayload);
  }

  render() {
    const { isLoading } = this.props;
    return (
      <Spin spinning={!!isLoading}>
        <PageHeaderWrapper title="检索条件"
          content={this.renderHeader()}>
          {this.renderReport()}
        </PageHeaderWrapper>
      </Spin>

    );
  }

  renderHeader() {
    const colMd = 2;
    const colInputMd = 6;
    return (
      <>
        <Row style={{ width: 1400 }}>
          <Col md={colMd}>
            筛选:
          </Col>
          <Col md={colInputMd} style={{ marginBottom: 12, marginLeft: -60 }}>
            <Radio.Group onChange={this.onChangeRadio} value={this.state.report_type}>
              {this.props.reportTypeList.map((item, index) => {
                return (
                  <Radio key={index} value={item.id}>
                    {item.name}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Col>
        </Row>
      </>
    );
  }

  renderReport() {
    const { reportList, isLoading } = this.props;
    return (
      <div>
        <Spin spinning={!!this.props.isLoading}>

        </Spin>

        <Card>
          <Table
            scroll={{ x: 1550 }}
            columns={this.state.merchantColumns}
            dataSource={reportList}
            loading={!!isLoading}
            bordered={true}
            rowKey={(record, index) => index.toString()}
            pagination={{  // 分页
              pageSize: PAGESIZE,
              current: this.state.currentPage,
              total: this.props.reportCount,
              onChange: this.changePage,
            }}
          />
        </Card>
      </div>
    );
  }

  makeColumns() {
    const columnsMerchant: ColumnProps<any>[] = [
      {
        title: 'ID',
        key: 'id',
        dataIndex: 'id',
        width: 70,
        align: 'center',
      },
      {
        title: '举报类型',
        dataIndex: 'report_type',
        key: 'report_type',
        align: 'center',
        width: 100,
        render: (val) => {
          return <div>{getCateName(val, this.props.reportTypeList)}</div>;
        }
      },
      {
        title: '操作',
        width: 120,
        dataIndex: 'delsign',
        align: 'center',
        render: (val, record, index) => {
          if (record.report_type == 3) {
            return <div>/</div>
          }
          if (record.status == 1) {
            return <div>已通过</div>
          } else {
            return <div>
              <Popconfirm
                title={`是否通过举报`}
                onConfirm={() => this.checkAction(record)}
                okText="确定"
                cancelText="取消"
              >
                <Button className={styles.marginLeft} type="primary" danger={true}>
                  通过举报
                </Button>
              </Popconfirm>
            </div>
          }
        }
      },
      {
        title: '状态',
        dataIndex: 'delsign',
        align: 'center',
        width: 70,
        render: (val) => {
          return <div>{val == 1 ? '删除' : "正常"}</div>;
        }
      },
      {
        title: '举报人',
        dataIndex: 'nickname',
        key: 'nickname',
        width: 100,
        align: 'center',
      },
      {
        title: '举报人ID',
        dataIndex: 'user_id',
        key: 'user_id',
        width: 100,
        align: 'center',
      },
      {
        title: '被举报人',
        dataIndex: 'reportNickname',
        key: 'reportNickname',
        width: 100,
        align: 'center',
      },
      {
        title: '被举报人ID',
        dataIndex: 'report_user_id',
        key: 'report_user_id',
        width: 100,
        align: 'center',
      },
      {
        title: '举报原因',
        dataIndex: 'report_text',
        key: 'report_text',
        align: 'center',
      },
      {
        title: '被举报人当前使用头像',
        dataIndex: 'avatar',
        key: 'avatar',
        align: 'center',
        width: 70,
        render: (val) => {
          return this.getImage(val, true);
        }
      },
      {
        title: '被举报头像',
        dataIndex: 'avatarUrl',
        key: 'avatarUrl',
        align: 'center',
        width: 70,
        render: (val) => {
          return this.getImage(val, true);
        }
      },
      {
        title: '被举报背景',
        dataIndex: 'bgUrl',
        key: 'bgUrl',
        align: 'center',
        width: 70,
        render: (val) => {
          return this.getImage(val, false);
        }
      },
      {
        title: '背景板使用中',
        dataIndex: 'is_use',
        key: 'is_use',
        align: 'center',
        width: 70,
        render: (val, record, index) => {
          if (record.report_type != 2) {
            return <div>/</div>
          }
          return <div>{val == 1 ? '是' : "否"}</div>;
        }
      },
      {
        title: '时间',
        dataIndex: 'create_time',
        align: 'center',
        width: 120,
        render: (val) => {
          return <div>{val == null ? '\\' : getTimeStr(val)}</div>;
        }
      },
    ];
    return columnsMerchant;
  }

  getImage = (img_url, isAvatar) => {
    if (img_url != undefined && img_url != null && img_url != "") {
      return <Image className={isAvatar ? styles.avatarImg : styles.bgImg} src={isAvatar ? getUserAvatar(img_url) : getUserBg(img_url)} width={80} height={80} />
    } else {
      return <div>无</div>
    }
  }

  onChangeRadio = e => {
    const report_type = e.target.value;
    this.setState({ report_type });
    this.setState({ currentPage: 1 });
    const req = { report_type, current: 1, pageCount: PAGESIZE };
    this.dispatchList(req);
    this.dispatchListCount(req);
  };

  checkAction = (item) => {
    const { dispatch } = this.props;
    console.log("item", item);
    if (item.report_type == 1) {//头像
      if (!item.upload_record_id || !item.report_user_id) {
        message.error("数据不全，不可以操作");
        return;
      }
      const req = {
        operate_user_id: this.props.uid,
        id: item.upload_record_id,
        report_id: item.id,
        userId: item.report_user_id,
        url: getUserAvatar(item.avatarUrl),

        isReport: true,
        report_type: this.state.report_type,
        current: this.state.currentPage,
        pageCount: PAGESIZE
      }
      dispatch({
        type: 'scriptkill/refuseUserAvatar',
        payload: req
      }).then(() => {
      });
    } else if (item.report_type == 2) {//背景
      const req = {
        operate_user_id: this.props.uid,
        id: item.background_info_id,
        report_id: item.id,
        userId: item.report_user_id,
        url: getUserBg(item.bgUrl),

        isReport: true,
        report_type: this.state.report_type,
        current: this.state.currentPage,
        pageCount: PAGESIZE
      }
      dispatch({
        type: 'scriptkill/refuseUserBg',
        payload: req
      }).then(() => {
      });
    }
  }

  changePage = (page) => {
    this.setState({ currentPage: page });
    const req = { report_type: this.state.report_type, current: page, pageCount: PAGESIZE, };
    this.dispatchList(req);
  };

  dispatchList = (req) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getReportInfo',
      payload: req
    });
  };

  dispatchListCount = (req) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'scriptkill/getReportInfoCount',
      payload: req
    });
  };

}