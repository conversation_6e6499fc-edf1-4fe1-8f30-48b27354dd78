@import '~antd/lib/style/themes/default.less';
@import '~@/utils/utils.less';

.searchError {
  color: @alert-error-icon-color;
  margin-top: 10px;
  margin-bottom: 10px;
}

.setOn {
  color: @primary-color;
}
.setOff {
  color: @alert-error-icon-color;
}

.customModalBody :global(.ant-modal-header) {
  padding: 10px;
}

.modalForm {
  display: flex;
  flex-direction: column;
  .formItem {
    display: flex;
    flex-direction: row;
    margin-top: 4px;
    justify-content: center;
    align-self: flex-start;
    .title {
      margin-top: 3px;
      width: 120px;
      font-weight: bold;
      display: flex;
      align-items: center;
      gap: 4px;
    }
    .content {
      width: 360px;
      margin-left: 15px;
    }
    .toggle {
      margin-left: 15px;
    }
  }
}

.helpIcon {
  color: @primary-color;
  cursor: pointer;
  font-size: 14px;
  opacity: 0.7;
  transition: opacity 0.3s;

  &:hover {
    opacity: 1;
  }
}

.tableInc {
  color: @primary-color;
}
.tableDec {
  color: magenta;
}
